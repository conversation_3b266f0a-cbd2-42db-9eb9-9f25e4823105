#!/usr/bin/env python3
"""
Test script to verify that the Pylance errors for traceback and image_warnings have been resolved.
"""

import ast
import sys

def test_traceback_usage():
    """Test that traceback is properly imported and used"""
    print("=== Testing Traceback Usage ===")
    
    try:
        with open('tickets.py', 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Check that traceback is imported at the top
        traceback_import_found = "import traceback" in source[:1000]  # Check first 1000 chars
        
        # Check that there are no duplicate imports
        duplicate_imports = source.count("import traceback")
        
        # Check that traceback is used properly
        traceback_usage_count = source.count("traceback.print_exc()")
        traceback_format_count = source.count("traceback.format_exc()")
        
        print(f"✅ Traceback imported at top: {traceback_import_found}")
        print(f"✅ Total traceback imports: {duplicate_imports} (should be 1)")
        print(f"✅ traceback.print_exc() usage: {traceback_usage_count}")
        print(f"✅ traceback.format_exc() usage: {traceback_format_count}")
        
        # Check for any remaining duplicate imports
        if duplicate_imports == 1:
            print("✅ No duplicate traceback imports found")
            return True
        else:
            print(f"❌ Found {duplicate_imports} traceback imports (should be 1)")
            return False
            
    except Exception as e:
        print(f"❌ Error checking traceback usage: {e}")
        return False

def test_image_warnings_fix():
    """Test that image_warnings has been fixed to warnings"""
    print("\n=== Testing image_warnings Fix ===")
    
    try:
        with open('tickets.py', 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Check that image_warnings.append is no longer present
        image_warnings_usage = source.count("image_warnings.append")
        
        # Check that the line now uses warnings.append instead
        correct_usage = "warnings.append(\"⚠️ Content may be truncated due to Discord's character limits\")" in source
        
        print(f"✅ image_warnings.append usage: {image_warnings_usage} (should be 0)")
        print(f"✅ Correct warnings.append usage: {correct_usage}")
        
        if image_warnings_usage == 0 and correct_usage:
            print("✅ image_warnings error has been fixed")
            return True
        else:
            print("❌ image_warnings error still exists")
            return False
            
    except Exception as e:
        print(f"❌ Error checking image_warnings fix: {e}")
        return False

def test_syntax_validity():
    """Test that the file has valid Python syntax"""
    print("\n=== Testing Python Syntax ===")
    
    try:
        with open('tickets.py', 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parse the AST to check for syntax errors
        ast.parse(source)
        print("✅ tickets.py has valid Python syntax")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error in tickets.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading tickets.py: {e}")
        return False

def test_specific_line_fixes():
    """Test the specific lines that were fixed"""
    print("\n=== Testing Specific Line Fixes ===")
    
    try:
        with open('tickets.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Check line 1234 (approximately) for the warnings fix
        found_correct_line = False
        for i, line in enumerate(lines, 1):
            if "warnings.append(\"⚠️ Content may be truncated due to Discord's character limits\")" in line:
                print(f"✅ Line {i}: Found correct warnings.append usage")
                found_correct_line = True
                break
        
        if not found_correct_line:
            print("❌ Could not find the corrected warnings.append line")
            return False
        
        # Check that no lines contain the old image_warnings usage
        image_warnings_lines = []
        for i, line in enumerate(lines, 1):
            if "image_warnings.append" in line:
                image_warnings_lines.append(i)
        
        if image_warnings_lines:
            print(f"❌ Found image_warnings.append on lines: {image_warnings_lines}")
            return False
        else:
            print("✅ No image_warnings.append usage found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking specific lines: {e}")
        return False

def main():
    """Run all tests"""
    print("Pylance Errors Fix Verification")
    print("=" * 40)
    
    tests = [
        test_syntax_validity,
        test_traceback_usage,
        test_image_warnings_fix,
        test_specific_line_fixes,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'=' * 40}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Pylance errors have been successfully resolved.")
        print("\nFixed Issues:")
        print("✅ image_warnings undefined variable → Changed to warnings")
        print("✅ Duplicate traceback import → Removed duplicate import")
        print("✅ All traceback usage now properly references the top-level import")
        print("✅ Python syntax is valid")
    else:
        print("⚠️  Some tests failed. Please review the fixes.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
