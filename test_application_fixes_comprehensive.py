#!/usr/bin/env python3
"""
Comprehensive test script for Discord bot application system fixes:
1. Interaction acknowledgment error fixes
2. Administrator permission checks
3. Fallback channel notification system

This script tests all fixes without requiring a live Discord bot.
"""

import asyncio
import discord
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

class MockUser:
    """Mock Discord user for testing"""
    def __init__(self, user_id=12345, name="TestUser", discriminator="0001", dm_enabled=True):
        self.id = user_id
        self.name = name
        self.discriminator = discriminator
        self.mention = f"<@{user_id}>"
        self.display_name = name
        self.avatar = Mock()
        self.avatar.url = "https://example.com/avatar.png"
        self._dm_enabled = dm_enabled
    
    async def send(self, *args, **kwargs):
        """Mock send method that can simulate DM failures"""
        if not self._dm_enabled:
            raise discord.errors.Forbidden(Mock(), "Cannot send messages to this user")
        print(f"[MOCK DM] Sent message to {self.name}: {args[0] if args else kwargs}")
        return Mock()

class MockChannel:
    """Mock Discord channel for testing"""
    def __init__(self, channel_id=67890, name="test-channel"):
        self.id = channel_id
        self.name = name
        self.mention = f"<#{channel_id}>"
    
    async def send(self, *args, **kwargs):
        """Mock send method for channel"""
        print(f"[MOCK CHANNEL #{self.name}] Sent message: {args[0] if args else kwargs}")
        return Mock()

class MockInteraction:
    """Mock Discord interaction for testing"""
    def __init__(self, user, is_admin=False):
        self.user = user
        self.response = Mock()
        self.response.is_done = Mock(return_value=False)
        self.response.send_message = AsyncMock()
        self.followup = Mock()
        self.followup.send = AsyncMock()
        
        # Mock guild permissions
        self.user.guild_permissions = Mock()
        self.user.guild_permissions.administrator = is_admin

async def test_interaction_acknowledgment_fixes():
    """Test that interaction acknowledgment errors are fixed"""
    print("\n=== Testing Interaction Acknowledgment Fixes ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.applications_status', {}) as mock_status, \
         patch('bot.save_data', AsyncMock()) as mock_save, \
         patch('bot.console_log') as mock_console:
        
        mock_bot.get_channel.return_value = MockChannel()
        
        # Import after patching
        from bot import handle_application_response
        
        # Test normal interaction flow
        print("Testing normal interaction response...")
        user = MockUser()
        interaction = MockInteraction(user, is_admin=True)
        
        try:
            await handle_application_response(interaction, user, "accepted", "Test Application")
            print("✅ Normal interaction handled successfully")
        except Exception as e:
            print(f"❌ Normal interaction failed: {e}")
        
        # Test already responded interaction
        print("Testing already responded interaction...")
        interaction.response.is_done.return_value = True
        
        try:
            await handle_application_response(interaction, user, "accepted", "Test Application")
            print("✅ Already responded interaction handled gracefully")
        except Exception as e:
            print(f"❌ Already responded interaction failed: {e}")

async def test_administrator_permission_checks():
    """Test that administrator permission checks work correctly"""
    print("\n=== Testing Administrator Permission Checks ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.applications_status', {}) as mock_status, \
         patch('bot.save_data', AsyncMock()) as mock_save:
        
        mock_bot.get_channel.return_value = MockChannel()
        
        # Import after patching
        from bot import handle_application_response
        
        # Test non-admin user
        print("Testing non-administrator user...")
        user = MockUser()
        interaction = MockInteraction(user, is_admin=False)
        
        # Create a mock button callback that includes permission check
        async def mock_accept_callback(interaction):
            # Check if user has administrator permissions
            if not interaction.user.guild_permissions.administrator:
                await interaction.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                return
            await handle_application_response(interaction, user, "accepted", "Test Application")
        
        try:
            await mock_accept_callback(interaction)
            # Check if the permission error was sent
            interaction.response.send_message.assert_called_with("❌ Only administrators can respond to applications.", ephemeral=True)
            print("✅ Non-administrator correctly blocked")
        except Exception as e:
            print(f"❌ Non-administrator test failed: {e}")
        
        # Test admin user
        print("Testing administrator user...")
        admin_interaction = MockInteraction(user, is_admin=True)
        
        try:
            await mock_accept_callback(admin_interaction)
            print("✅ Administrator access granted")
        except Exception as e:
            print(f"❌ Administrator test failed: {e}")

async def test_fallback_notification_system():
    """Test that fallback notification system works correctly"""
    print("\n=== Testing Fallback Notification System ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_fallback_channel', 67890) as mock_fallback:
        
        fallback_channel = MockChannel(67890, "application-fallback")
        mock_bot.get_channel.return_value = fallback_channel
        
        # Import after patching
        from bot import send_application_notification
        
        # Test DM success
        print("Testing successful DM delivery...")
        user_dm_enabled = MockUser(dm_enabled=True)
        embed = discord.Embed(title="Test", description="Test notification")
        
        try:
            result = await send_application_notification(user_dm_enabled, embed, "Test App", "accepted")
            if result:
                print("✅ DM delivery successful")
            else:
                print("❌ DM delivery failed unexpectedly")
        except Exception as e:
            print(f"❌ DM test failed: {e}")
        
        # Test DM failure with fallback
        print("Testing DM failure with fallback channel...")
        user_dm_disabled = MockUser(dm_enabled=False)
        
        try:
            result = await send_application_notification(user_dm_disabled, embed, "Test App", "accepted")
            if result:
                print("✅ Fallback notification sent successfully")
            else:
                print("❌ Fallback notification failed")
        except Exception as e:
            print(f"❌ Fallback test failed: {e}")
        
        # Test DM failure without fallback channel
        print("Testing DM failure without fallback channel...")
        with patch('bot.application_fallback_channel', None):
            try:
                result = await send_application_notification(user_dm_disabled, embed, "Test App", "accepted")
                if not result:
                    print("✅ Gracefully handled missing fallback channel")
                else:
                    print("❌ Should have failed without fallback channel")
            except Exception as e:
                print(f"❌ No fallback test failed: {e}")

async def test_configuration_commands():
    """Test that configuration commands work correctly"""
    print("\n=== Testing Configuration Commands ===")
    
    # Test that the new command exists and has proper structure
    try:
        # Import the command tree
        import bot
        
        # Check if the fallback channel command exists
        commands = [cmd.name for cmd in bot.tree.get_commands()]
        if "set_application_fallback_channel" in commands:
            print("✅ Fallback channel command exists")
        else:
            print("❌ Fallback channel command not found")
        
        # Check if the command has administrator permissions
        for cmd in bot.tree.get_commands():
            if cmd.name == "set_application_fallback_channel":
                if hasattr(cmd, 'default_permissions') and cmd.default_permissions:
                    print("✅ Fallback channel command has administrator permissions")
                else:
                    print("❌ Fallback channel command missing administrator permissions")
                break
        
    except Exception as e:
        print(f"❌ Configuration command test failed: {e}")

async def main():
    """Run all tests"""
    print("Starting Comprehensive Discord Bot Application Fixes Tests")
    print("=" * 60)
    
    await test_interaction_acknowledgment_fixes()
    await test_administrator_permission_checks()
    await test_fallback_notification_system()
    await test_configuration_commands()
    
    print("\n" + "=" * 60)
    print("All tests completed!")
    print("\nSummary of fixes:")
    print("✅ Fixed interaction acknowledgment errors by removing duplicate handlers")
    print("✅ Added administrator permission checks to all application buttons")
    print("✅ Implemented fallback channel system for DM failures")
    print("✅ Added configuration command for fallback channel")
    print("✅ Updated data persistence to include fallback channel")

if __name__ == "__main__":
    asyncio.run(main())
