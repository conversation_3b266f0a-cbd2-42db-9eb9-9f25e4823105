import os
import json
import logging
import asyncio
import threading
import random
import time
from datetime import datetime, timezone

def ensure_utc_timezone(dt):
    """Ensure a datetime object has UTC timezone information"""
    if dt is None:
        return None
    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt)
        except:
            return None
    if dt.tzinfo is None:
        # Assume naive datetime is UTC
        return dt.replace(tzinfo=timezone.utc)
    return dt
import pymongo
import discord
from discord.ext import tasks
from discord import app_commands
from discord.ui import Select, View, Button, Modal, TextInput
from colorama import init
from bot_instance import bot  # Import bot from bot_instance
from tickets import (
    ticket_config, set_staff_role,
    set_transcript_channel, close_ticket, create_ticket, load_ticket_data,
    save_ticket_data, add_category, set_ticket_channel,
    reopen_ticket, claim_ticket, delete_message_with_backoff,
    validate_ticket_channel_context
)
# Ticket customizer functionality is now integrated into tickets.py
from database import (
    save_data, load_data, save_transaction, get_transactions, get_guild_settings, save_guild_settings,
    save_persistent_view, get_persistent_view, deactivate_persistent_view, get_all_active_persistent_views,
    save_claim_verification, get_claim_verification, cleanup_old_persistent_views, db_manager
)
from memory_manager import get_memory_manager
from utils import (
    load_json, save_json, get_file_lock, log_permission_check, setup_logging,
    get_script_directory, console_log, log_command_execution, log_error_to_console, log_bot_status,
    safe_add_role, safe_remove_role, safe_get_role, validate_role_permissions,
    safe_add_reaction, safe_add_reaction_with_skip, validate_emoji, validate_emoji_for_reactions, clean_invalid_reactions,
    normalize_emoji_format
)

# Import performance optimization modules
try:
    from performance_manager import get_performance_manager
    from enhanced_database import get_enhanced_db_manager
    from discord_api_optimizer import get_discord_api_optimizer
    PERFORMANCE_OPTIMIZATIONS_ENABLED = True
    # Log to file only - no console spam
    logging.info("Performance optimizations loaded successfully")
except ImportError as e:
    PERFORMANCE_OPTIMIZATIONS_ENABLED = False
    # Only log to console if it's a critical issue
    logging.warning(f"Performance optimizations not available: {e}")

# Performance-optimized database operations
async def optimized_save_data():
    """Save data using enhanced database if available, fallback to regular database"""
    try:
        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            enhanced_db = get_enhanced_db_manager()
            performance_manager = get_performance_manager()

            # Use performance monitoring
            async def save_operation():
                try:
                    # Prepare data structure
                    data = {
                        "gangs": {
                            "roles": gang_roles,
                            "strikes": gang_strikes,
                            "invitations": gang_invitations
                        },
                        "applications": {
                            "status": applications_status,
                            "forms": application_forms,
                            "channel": application_channel,
                            "log_channel": application_log_channel
                        },
                        "reaction_roles": reaction_roles,
                        "sticky_messages": sticky_messages,
                        "welcome": {
                            "channel_id": welcome_channel_id,
                            "message": welcome_message,
                            "image_url": welcome_image_url
                        },
                        "tebex": {
                            "channel": tebex_channel,
                            "webhook_url": webhook_url
                        },
                        "join_role_id": join_role_id,
                        "notification_channel_id": notification_channel_id
                    }

                    # Save to enhanced database with cache invalidation
                    await enhanced_db.update_one_with_cache_invalidation(
                        "settings",
                        {"_id": "bot_data"},
                        {"$set": data},
                        upsert=True
                    )

                    logging.info("Data saved successfully to enhanced database")
                    return True

                except Exception as e:
                    logging.warning(f"Enhanced database save failed: {e}, falling back to regular database")
                    return False

            result = await performance_manager.execute_with_monitoring(save_operation)
            if result:
                return True
            else:
                # Enhanced database save failed, fall back to regular database
                logging.info("Falling back to regular database for data saving")
                return await save_data()
        else:
            # Fallback to regular database
            return await save_data()

    except Exception as e:
        logging.error(f"Error in optimized_save_data: {e}")
        # Fallback to regular database
        return await save_data()

async def optimized_load_data():
    """Load data using enhanced database if available, fallback to regular database"""
    try:
        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            enhanced_db = get_enhanced_db_manager()
            performance_manager = get_performance_manager()

            # Use performance monitoring and caching
            async def load_operation():
                try:
                    # Load from enhanced database with caching
                    data = await enhanced_db.find_one_cached(
                        "settings",
                        {"_id": "bot_data"},
                        ttl=300  # 5 minute cache
                    )

                    if data:
                        # Update global variables
                        global gang_roles, gang_strikes, gang_invitations
                        global applications_status, application_forms, application_channel, application_log_channel
                        global application_fallback_channel, application_response_channel
                        global reaction_roles, sticky_messages
                        global welcome_channel_id, welcome_message, welcome_image_url
                        global tebex_channel, webhook_url, join_role_id, notification_channel_id

                        # Load gang data
                        gangs_data = data.get("gangs", {})
                        gang_roles = gangs_data.get("roles", {})
                        gang_strikes = gangs_data.get("strikes", {})
                        gang_invitations = gangs_data.get("invitations", {})

                        # Load application data with backward compatibility
                        app_data = data.get("applications", {})
                        applications_status = app_data.get("status", {})
                        application_forms = app_data.get("forms", {})

                        # Handle both old and new data structure for channels
                        channels_data = app_data.get("channels", {})
                        logging.debug(f"Loading application channels - channels_data: {channels_data}")

                        if channels_data:
                            # New structure
                            application_channel = channels_data.get("application_channel")
                            application_log_channel = channels_data.get("log_channel")
                            application_fallback_channel = channels_data.get("fallback_channel")
                            application_response_channel = channels_data.get("response_channel")
                            logging.info(f"Loaded application channels (new structure) - channel: {application_channel}, log_channel: {application_log_channel}, fallback_channel: {application_fallback_channel}, response_channel: {application_response_channel}")
                        else:
                            # Old structure (backward compatibility)
                            application_channel = app_data.get("channel")
                            application_log_channel = app_data.get("log_channel")
                            application_fallback_channel = None  # Not available in old structure
                            application_response_channel = None  # Not available in old structure
                            logging.info(f"Loaded application channels (old structure) - channel: {application_channel}, log_channel: {application_log_channel}")

                        # Validate loaded channels
                        if application_channel:
                            logging.info(f"Application channel loaded successfully: {application_channel}")
                        else:
                            logging.warning("No application channel found in database")

                        # Load other data
                        reaction_roles = data.get("reaction_roles", {})
                        sticky_messages = data.get("sticky_messages", {})

                        welcome_data = data.get("welcome", {})
                        welcome_channel_id = welcome_data.get("channel_id")
                        welcome_message = welcome_data.get("message", "Welcome!")
                        welcome_image_url = welcome_data.get("image_url")

                        tebex_data = data.get("tebex", {})
                        tebex_channel = tebex_data.get("channel")
                        webhook_url = tebex_data.get("webhook_url")

                        join_role_id = data.get("join_role_id")
                        notification_channel_id = data.get("notification_channel_id")

                        logging.info("Data loaded successfully from enhanced database")
                        return True
                    else:
                        # No data found in enhanced database, this is normal for first run
                        logging.info("No data found in enhanced database, will use regular database")
                        return False

                except Exception as e:
                    logging.warning(f"Enhanced database load failed: {e}, falling back to regular database")
                    return False

            result = await performance_manager.execute_with_monitoring(load_operation)
            if result:
                return True
            else:
                # Enhanced database didn't have data, fall back to regular database
                logging.info("Falling back to regular database for data loading")
                return await load_data()
        else:
            # Fallback to regular database
            return await load_data()

    except Exception as e:
        logging.error(f"Error in optimized_load_data: {e}")
        # Fallback to regular database
        return await load_data()

# Helper function to use optimized save when available
async def save_data_optimized():
    """Use optimized save_data if available, otherwise use regular save_data"""
    if PERFORMANCE_OPTIMIZATIONS_ENABLED:
        return await optimized_save_data()
    else:
        return await save_data()

class RateLimitHandler:
    def __init__(self):
        self.rate_limits = {}
        self.queues = {}
        self.processing = {}
        self.max_retries = 10  # Increased from 3
        self.base_delay = 0.5
        self.max_delay = 15.0
        self.bulk_queue = asyncio.Queue()
        self.bulk_processing = False

    async def execute(self, key, coroutine, *args, **kwargs):
        """Execute a coroutine with enhanced rate limit handling"""
        if key not in self.queues:
            self.queues[key] = asyncio.Queue()
            self.processing[key] = False

        # Add to queue
        await self.queues[key].put((coroutine, args, kwargs))

        # Start processing if not already running
        if not self.processing[key]:
            self.processing[key] = True
            asyncio.create_task(self._process_queue(key))

    async def execute_bulk(self, operations):
        """Handle bulk operations more efficiently"""
        for op in operations:
            await self.bulk_queue.put(op)

        if not self.bulk_processing:
            self.bulk_processing = True
            asyncio.create_task(self._process_bulk_queue())

    async def _process_bulk_queue(self):
        """Process bulk operations with smart rate limiting"""
        try:
            batch_size = 0
            last_operation_time = time.time()

            while not self.bulk_queue.empty():
                current_time = time.time()
                time_diff = current_time - last_operation_time

                # Adjust batch size based on rate limit encounters
                if time_diff > 5:  # Reset batch size after 5 seconds of no rate limits
                    batch_size = min(50, batch_size + 10)

                # Process a batch of operations
                operations = []
                for _ in range(batch_size):
                    if self.bulk_queue.empty():
                        break
                    operations.append(await self.bulk_queue.get())

                try:
                    # Execute batch with retry logic
                    success = await self._execute_with_backoff(operations)
                    if success:
                        last_operation_time = time.time()
                    else:
                        batch_size = max(1, batch_size // 2)  # Reduce batch size on failure

                except Exception as e:
                    print(f"Error in bulk processing: {e}")
                    batch_size = max(1, batch_size // 2)

                # Add small delay between batches
                await asyncio.sleep(0.1)

        finally:
            self.bulk_processing = False

    async def _process_queue(self, key):
        """Process queued items with enhanced rate limiting"""
        try:
            while not self.queues[key].empty():
                # Apply rate limit with jitter
                if key in self.rate_limits:
                    wait_time = self.rate_limits[key] - time.time()
                    if wait_time > 0:
                        jitter = random.uniform(0, 0.1 * wait_time)
                        await asyncio.sleep(wait_time + jitter)

                # Get next item
                coroutine, args, kwargs = await self.queues[key].get()

                try:
                    # Execute with improved retry logic
                    for attempt in range(self.max_retries):
                        try:
                            await coroutine(*args, **kwargs)
                            break
                        except discord.HTTPException as e:
                            if e.status == 429:  # Rate limit
                                retry_after = e.retry_after
                                self.rate_limits[key] = time.time() + retry_after
                                # Calculate exponential backoff with jitter
                                backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
                                jitter = random.uniform(0, 0.1 * backoff)
                                await asyncio.sleep(backoff + jitter)
                                continue
                            raise
                except Exception as e:
                    print(f"Error processing {key}: {e}")

                # Dynamic delay between operations
                await asyncio.sleep(random.uniform(0.1, 0.3))

        finally:
            self.processing[key] = False

    async def _execute_with_backoff(self, operations):
        """Execute operations with exponential backoff"""
        for attempt in range(self.max_retries):
            try:
                for op in operations:
                    await op()
                return True
            except discord.HTTPException as e:
                if e.status == 429:
                    backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
                    jitter = random.uniform(0, 0.1 * backoff)
                    await asyncio.sleep(backoff + jitter)
                    continue
                raise
        return False

# Create global rate limit handler
rate_limiter = RateLimitHandler()

# Initialize colorama
init()

# Initialize file locks
file_locks = {}

# Initialize all global variables at the top level
gang_strikes = {}
gang_roles = {}
gang_members = {}
gang_leaders = {}
gang_invitations = {}  # Store pending gang invitations with confirmation system
applications_status = {}
reaction_roles = {}
reaction_message_id = None
reaction_channel_id = None
application_forms = {}
application_channel = None
application_log_channel = None
application_fallback_channel = None
application_response_channel = None
sticky_messages = {}
welcome_channel_id = None
welcome_message = "Welcome!"
welcome_image_url = None
vanity_url = None
role_name = None
notification_channel_id = None
join_role_id = None
tebex_channel = None
webhook_url = None

async def delete_message(message):
    """Delete a message with enhanced rate limit handling"""
    try:
        await rate_limiter.execute(
            'delete_message',
            message.delete,
            reason="Bulk operation"
        )
        return True
    except Exception as e:
        print(f"Error deleting message: {e}")
        return False

async def delete_messages_bulk(messages):
    """Delete multiple messages efficiently"""
    try:
        operations = [
            lambda m=msg: m.delete(reason="Bulk operation")
            for msg in messages
        ]
        await rate_limiter.execute_bulk(operations)
        return True
    except Exception as e:
        print(f"Error in bulk message deletion: {e}")
        return False

# Set up dual logging - detailed file logging + clean console output
setup_logging('bot.log', logging.INFO, logging.WARNING)

# Get the directory of the current script
script_dir = get_script_directory()

# Define file paths for data storage
DATA_FILE_PATH = os.path.join(script_dir, 'bot_data.json')
EVENT_LOGS_FILE = os.path.join(script_dir, 'event_logs.json')

# Create empty data files if they don't exist
def ensure_data_files_exist():
    """Create empty data files if they don't exist"""
    try:
        # Create bot_data.json if it doesn't exist
        if not os.path.exists(DATA_FILE_PATH):
            with open(DATA_FILE_PATH, 'w') as f:
                json.dump({}, f)
            logging.info(f"Created empty data file at {DATA_FILE_PATH}")

        # Create event_logs.json if it doesn't exist
        if not os.path.exists(EVENT_LOGS_FILE):
            with open(EVENT_LOGS_FILE, 'w') as f:
                json.dump({}, f)
            logging.info(f"Created empty event logs file at {EVENT_LOGS_FILE}")
    except Exception as e:
        logging.error(f"Error creating data files: {e}")

# Ensure data files exist
ensure_data_files_exist()

# Load configuration
settings_path = os.path.join(script_dir, 'Settings.json')

# Check if the settings file exists
if not os.path.exists(settings_path):
    print(f"Error: Settings file not found at {settings_path}.")
    exit(1)

# Load configuration
with open(settings_path) as f:
    Settings = json.load(f)

# Get the token and prefix
token = Settings.get('Token')
prefix = Settings.get("Prefix")

# Check if the token is loaded correctly
if token is None:
    console_log("Token is not set in the Settings.json file.", "ERROR")
    exit(1)  # Exit the program if the token is not found

# Initialize applications_status
applications_status = {}

# Use the existing command tree associated with the bot
tree = bot.tree


# Utility Functions
async def find_role_by_name(guild, role_name):
    for role in guild.roles:
        if role.name == role_name:
            return role
    return None

async def send_embed(channel, title, description, color=discord.Color.blue()):
    embed = discord.Embed(title=title, description=description, color=color)
    await channel.send(embed=embed)

# Vanity Role System
class Status:
    roles = 0

def safePrint(member=None, action=None, vocab=None, color=None):
    timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
    if vocab is not None:
        print(f"[{timestamp}] {member} [{color}{action} {vanity_url} {vocab} status]")
    else:
        print(f"[{timestamp}] {member} [{color}{action}]")

@bot.event
async def on_guild_join(guild):
    """Event handler for when the bot joins a new guild"""
    try:
        # Initialize default settings for the new guild
        default_settings = {
            "guild_id": guild.id,
            "vanity": {
                "url": None,
                "role_name": None
            },
            "notification_channel_id": None
        }

        # Save the default settings to database
        await save_guild_settings(guild.id, default_settings)
        print(f"Bot joined new guild: {guild.name} (ID: {guild.id})")
    except Exception as e:
        print(f"Error initializing guild settings: {e}")

@tasks.loop(seconds=10)  # Check every 10 seconds for more frequent updates
async def check_vanity_status():
    """Check vanity status across all guilds the bot is in"""
    for guild in bot.guilds:
        try:
            # Get guild-specific settings
            settings = await get_guild_settings(guild.id)
            if not settings:
                # Initialize guild settings if they don't exist
                settings = {
                    "guild_id": guild.id,
                    "vanity": {},
                    "notification_channel_id": None
                }
                await save_guild_settings(guild.id, settings)
                continue

            # Check if vanity settings exist
            vanity_settings = settings.get("vanity", {})
            if not vanity_settings.get("url") or not vanity_settings.get("role_name"):
                continue

            vanity_url = vanity_settings["url"]
            role_name = vanity_settings["role_name"]
            notification_channel_id = settings.get("notification_channel_id")

            # Find the role
            role = await find_role_by_name(guild, role_name)
            if not role:
                print(f"Role '{role_name}' not found in guild {guild.name}")
                continue

            # Get notification channel
            notification_channel = guild.get_channel(notification_channel_id) if notification_channel_id else None

            for member in guild.members:
                if member.bot:
                    continue
                # Check if the member is online, idle, or do not disturb
                if member.status in (discord.Status.online, discord.Status.idle, discord.Status.dnd):
                    has_vanity_status = any(activity.name == vanity_url for activity in member.activities if isinstance(activity, discord.CustomActivity))

                    if has_vanity_status and role not in member.roles:
                        await safe_add_role(member, role, "Vanity URL status role")
                        if notification_channel:
                            # Get custom add role title and message
                            add_role_title = vanity_settings.get("add_role_title", "Priority Queue Granted")
                            add_role_message = vanity_settings.get("add_role_message", "User just added '{status}' as their custom Discord status and received free priority queue!")

                            # Format the message with member and status
                            formatted_message = add_role_message.format(
                                member=member.mention,
                                status=vanity_url
                            )

                            embed = discord.Embed(
                                title=add_role_title,
                                description=formatted_message,
                                color=discord.Color.green()
                            )
                            await notification_channel.send(embed=embed)
                        safePrint(member, "Added role", "to", Fore.GREEN)
                    elif not has_vanity_status and role in member.roles:
                        await member.remove_roles(role)
                        if notification_channel:
                            # Get custom remove role title and message
                            remove_role_title = vanity_settings.get("remove_role_title", "Priority Queue Removed")
                            remove_role_message = vanity_settings.get("remove_role_message", "User has removed '{status}' from their custom Discord status")

                            # Format the message with member and status
                            formatted_message = remove_role_message.format(
                                member=member.mention,
                                status=vanity_url
                            )

                            embed = discord.Embed(
                                title=remove_role_title,
                                description=formatted_message,
                                color=discord.Color.red()
                            )
                            await notification_channel.send(embed=embed)
                        safePrint(member, "Removed role", "from", Fore.RED)
        except Exception as e:
            print(f"Error checking vanity status in guild {guild.name}: {e}")

@tasks.loop(minutes=30)  # Clean up every 30 minutes
async def cleanup_modal_states():
    """Clean up expired modal application states"""
    await cleanup_expired_modal_states()

@tasks.loop(hours=6)  # Backup every 6 hours
async def backup_application_data_task():
    """Periodic backup of application data with comprehensive error handling and monitoring"""
    backup_start_time = datetime.now()

    try:
        logging.info("=" * 50)
        logging.info("Starting periodic application data backup...")
        logging.info(f"Backup started at: {backup_start_time.isoformat()}")

        # Pre-backup system check
        logging.info("Performing pre-backup system checks...")

        # Check if critical data exists
        data_status = {
            "application_forms": len(application_forms) if isinstance(application_forms, dict) else 0,
            "applications_status": len(applications_status) if isinstance(applications_status, dict) else 0,
            "application_channel": application_channel is not None,
            "application_log_channel": application_log_channel is not None
        }

        logging.info(f"Data status: {data_status}")

        # Attempt backup
        success = await backup_application_data()

        backup_end_time = datetime.now()
        backup_duration = (backup_end_time - backup_start_time).total_seconds()

        if success:
            logging.info(f"✅ Periodic application data backup completed successfully in {backup_duration:.2f} seconds")
        else:
            logging.error(f"❌ Periodic application data backup failed after {backup_duration:.2f} seconds")

            # Send alert for backup failure (if notification system is available)
            try:
                await send_backup_failure_alert(backup_duration, data_status)
            except Exception as alert_error:
                logging.warning(f"Failed to send backup failure alert: {alert_error}")

        logging.info("=" * 50)

    except Exception as e:
        backup_end_time = datetime.now()
        backup_duration = (backup_end_time - backup_start_time).total_seconds()

        logging.error(f"💥 Critical error in periodic application data backup after {backup_duration:.2f} seconds: {e}")
        import traceback
        traceback.print_exc()

        # Try to send emergency alert
        try:
            await send_backup_critical_error_alert(str(e), backup_duration)
        except Exception as alert_error:
            logging.warning(f"Failed to send critical error alert: {alert_error}")

async def send_backup_failure_alert(duration, data_status):
    """Send alert when backup fails (optional notification system)"""
    try:
        # This is optional - only send if notification channel is configured
        if 'notification_channel_id' in globals() and notification_channel_id:
            channel = bot.get_channel(notification_channel_id)
            if channel:
                embed = discord.Embed(
                    title="⚠️ Backup System Alert",
                    description=f"Application data backup failed after {duration:.2f} seconds",
                    color=0xF39C12
                )
                embed.add_field(name="Data Status", value=str(data_status), inline=False)
                embed.timestamp = datetime.now()
                await channel.send(embed=embed)
    except Exception as e:
        logging.debug(f"Backup failure alert not sent: {e}")

async def send_backup_critical_error_alert(error_msg, duration):
    """Send alert for critical backup errors"""
    try:
        # This is optional - only send if notification channel is configured
        if 'notification_channel_id' in globals() and notification_channel_id:
            channel = bot.get_channel(notification_channel_id)
            if channel:
                embed = discord.Embed(
                    title="🚨 Critical Backup Error",
                    description=f"Critical error in backup system after {duration:.2f} seconds",
                    color=0xE74C3C
                )
                embed.add_field(name="Error", value=error_msg[:1024], inline=False)
                embed.timestamp = datetime.now()
                await channel.send(embed=embed)
    except Exception as e:
        logging.debug(f"Critical error alert not sent: {e}")

@tree.command(name="setup_tickets", description="Setup the ticket system for the server")
@app_commands.default_permissions(administrator=True)
async def setup_tickets_slash(interaction: discord.Interaction):
    try:
        await interaction.response.defer(ephemeral=True)

        # Import the setup_ticket_system function from tickets.py
        from tickets import setup_ticket_system

        # Call the setup_ticket_system function with the interaction
        await setup_ticket_system(interaction)

    except Exception as e:
        logging.error(f"Error in setup_tickets_slash: {e}")
        await interaction.followup.send("An error occurred while setting up the ticket system.", ephemeral=True)
        return





# Removed /ticket command - Users must use ticket panel buttons to create tickets

# The customize_ticket command is now in ticket_customizer.py

@tree.command(name="set_vanity", description="Set the vanity status and role")
@app_commands.default_permissions(administrator=True)
async def set_vanity(interaction: discord.Interaction, status: str, role: discord.Role):
    if not log_permission_check(interaction, "set_vanity"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Defer the response to prevent timeout
        await interaction.response.defer(ephemeral=True)

        # Get or create guild settings
        settings = await get_guild_settings(interaction.guild_id)
        if not settings:
            settings = {
                "guild_id": interaction.guild_id,
                "vanity": {},
                "notification_channel_id": None
            }

        # Update vanity settings
        settings["vanity"].update({
            "url": status,
            "role_name": role.name
        })

        # Save settings
        await save_guild_settings(interaction.guild_id, settings)

        # Send confirmation message
        await interaction.followup.send(
            f"✅ Vanity configuration updated:\n• Status: `{status}`\n• Role: {role.mention}\n\nUse /edit_vanity_notification to customize your notification messages!",
            ephemeral=True
        )

    except discord.Forbidden:
        await interaction.followup.send(
            "❌ I don't have the required permissions to perform this action.",
            ephemeral=True
        )
    except Exception as e:
        logging.error(f"Error in set_vanity: {str(e)}")
        await interaction.followup.send(
            "❌ An error occurred while setting up the vanity configuration. Please check the logs.",
            ephemeral=True
        )

@tree.command(name="edit_vanity_notification", description="Edit vanity notification messages")
@app_commands.default_permissions(administrator=True)
async def edit_vanity_notification(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return

    # Get current guild settings
    settings = await get_guild_settings(interaction.guild_id)
    if not settings:
        settings = {
            "guild_id": interaction.guild_id,
            "vanity": {},
            "notification_channel_id": None
        }

    # Show the notification customization modal
    modal = VanityNotificationModal()
    await interaction.response.send_modal(modal)

@tree.command(name="set_notification_channel", description="Set the notification channel")
@app_commands.default_permissions(administrator=True)
async def set_notification_channel(interaction: discord.Interaction, channel: discord.TextChannel):
    if not log_permission_check(interaction, "set_notification_channel"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Defer the response to prevent timeout
        await interaction.response.defer(ephemeral=True)

        # Get current guild settings
        settings = await get_guild_settings(interaction.guild_id)
        if not settings:
            settings = {
                "guild_id": interaction.guild_id,
                "vanity": {},
                "notification_channel_id": None
            }

        # Update notification channel
        settings["notification_channel_id"] = channel.id

        # Save to guild settings collection
        success = await save_guild_settings(interaction.guild_id, settings)

        if not success:
            await interaction.followup.send(
                "❌ Failed to save notification channel settings. Please try again.",
                ephemeral=True
            )
            return

        await interaction.followup.send(
            f"✅ Notification channel has been set to {channel.mention}",
            ephemeral=True
        )
    except Exception as e:
        logging.error(f"Error in set_notification_channel: {str(e)}")
        await interaction.followup.send(
            "❌ An error occurred while setting the notification channel. Please check the logs.",
            ephemeral=True
        )

class VanityNotificationModal(Modal):
    def __init__(self):
        super().__init__(title="Customize Vanity Notifications")

        self.add_item(TextInput(
            label="Add Role Title",
            placeholder="Enter title for when role is added",
            default="Priority Queue Granted",
            required=True
        ))

        self.add_item(TextInput(
            label="Add Role Message",
            placeholder="Use {member} for user mention and {status} for vanity status",
            default="{member} just added '{status}' as their custom Discord status and received free priority queue!",
            style=discord.TextStyle.paragraph,
            required=True
        ))

        self.add_item(TextInput(
            label="Remove Role Title",
            placeholder="Enter title for when role is removed",
            default="Priority Queue Removed",
            required=True
        ))

        self.add_item(TextInput(
            label="Remove Role Message",
            placeholder="Use {member} for user mention and {status} for vanity status",
            default="{member} has removed '{status}' from their custom Discord status",
            style=discord.TextStyle.paragraph,
            required=True
        ))

    async def on_submit(self, interaction: discord.Interaction):
        try:
            settings = await get_guild_settings(interaction.guild_id)
            if not settings:
                settings = {
                    "guild_id": interaction.guild_id,
                    "vanity": {},
                    "notification_channel_id": None
                }

            settings["vanity"].update({
                "add_role_title": self.children[0].value,
                "add_role_message": self.children[1].value,
                "remove_role_title": self.children[2].value,
                "remove_role_message": self.children[3].value
            })

            # Remove old fields if they exist
            if "embed_title" in settings["vanity"]:
                del settings["vanity"]["embed_title"]
            if "notification_message" in settings["vanity"]:
                del settings["vanity"]["notification_message"]

            await save_guild_settings(interaction.guild_id, settings)
            await interaction.response.send_message("✅ Vanity notification messages have been updated!", ephemeral=True)
        except Exception as e:
            logging.error(f"Error in VanityNotificationModal.on_submit: {str(e)}")
            await interaction.response.send_message("❌ An error occurred while saving notification settings.", ephemeral=True)

@tree.command(name="remove_vanity", description="Remove the vanity status and role")
@app_commands.default_permissions(administrator=True)
async def remove_vanity(interaction: discord.Interaction):
    if not log_permission_check(interaction, "remove_vanity"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Get current guild settings
        settings = await get_guild_settings(interaction.guild_id)

        # Reset vanity settings while preserving notification messages
        if "vanity" in settings:
            notification_settings = {
                key: settings["vanity"][key]
                for key in ["add_role_title", "add_role_message", "remove_role_title", "remove_role_message"]
                if key in settings["vanity"]
            }
            settings["vanity"] = {
                "url": None,
                "role_name": None,
                **notification_settings
            }

        # Save updated settings
        await save_guild_settings(interaction.guild_id, settings)

        # Send confirmation message
        await interaction.response.send_message("✅ Vanity status has been removed successfully.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in remove_vanity: {str(e)}")
        await interaction.response.send_message("❌ An error occurred while removing the vanity configuration.", ephemeral=True)

# In the embed message section
class EmbedModal(Modal):
    def __init__(self):
        super().__init__(title="Create Embed Message")

        self.add_item(TextInput(
            label="Title",
            placeholder="Enter the title for your embed",
            required=True
        ))

        self.add_item(TextInput(
            label="Description",
            placeholder="Enter the description for your embed",
            style=discord.TextStyle.paragraph,
            required=True
        ))

        self.add_item(TextInput(
            label="Color (Hex)",
            placeholder="#RRGGBB (e.g., #FF0000 for red) - Leave empty for default",
            required=False
        ))

        self.add_item(TextInput(
            label="Image URL",
            placeholder="Enter an image URL (optional)",
            required=False
        ))

        self.add_item(TextInput(
            label="Footer",
            placeholder="Enter footer text (optional)",
            required=False
        ))

    async def on_submit(self, interaction: discord.Interaction):
        self.interaction = interaction



@tree.command(name="send_embed", description="Send an embedded message to a channel")
@app_commands.default_permissions(administrator=True)
async def send_embed(interaction: discord.Interaction, channel: discord.TextChannel, mention: str = None):
    if not log_permission_check(interaction, "send_embed"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Send the modal
        modal = EmbedModal()
        await interaction.response.send_modal(modal)

        # Wait for modal submission
        await modal.wait()

        # Get values from modal
        title = modal.children[0].value
        description = modal.children[1].value
        color_hex = modal.children[2].value
        image_url = modal.children[3].value
        footer = modal.children[4].value

        # Create embed
        try:
            embed_color = int(color_hex.strip('#'), 16) if color_hex and color_hex.startswith('#') else discord.Color.blue().value
        except ValueError:
            embed_color = discord.Color.blue().value

        embed = discord.Embed(
            title=title,
            description=description,
            color=embed_color
        )

        # Set image if provided and valid
        if image_url and image_url.strip():
            image_url = image_url.strip()
            if image_url.startswith('http://') or image_url.startswith('https://'):
                try:
                    # Validate that the URL is accessible and is an image
                    async with aiohttp.ClientSession() as session:
                        async with session.head(image_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                            if response.status == 200:
                                content_type = response.headers.get('content-type', '').lower()
                                if content_type.startswith('image/'):
                                    embed.set_image(url=image_url)
                                else:
                                    await modal.interaction.followup.send(
                                        "⚠️ Warning: The provided URL does not appear to be an image. The embed was sent without the image.",
                                        ephemeral=True
                                    )
                            else:
                                await modal.interaction.followup.send(
                                    f"⚠️ Warning: Could not access the image URL (HTTP {response.status}). The embed was sent without the image.",
                                    ephemeral=True
                                )
                except Exception as e:
                    logging.warning(f"Error validating image URL {image_url}: {e}")
                    await modal.interaction.followup.send(
                        "⚠️ Warning: Could not validate the image URL. The embed was sent without the image.",
                        ephemeral=True
                    )
            else:
                await modal.interaction.followup.send(
                    "⚠️ Warning: Image URL must start with http:// or https://. The embed was sent without the image.",
                    ephemeral=True
                )

        # Set footer if provided
        if footer:
            embed.set_footer(text=footer)

        # Handle mentions
        content = None
        if mention:
            mention = mention.strip().lower()
            if mention == "@everyone" or mention == "everyone":
                content = "@everyone"  # Handle both with and without @ prefix
            elif mention == "@here" or mention == "here":
                content = "@here"  # Handle both with and without @ prefix
            elif mention.startswith("<@") and mention.endswith(">"):
                content = mention
            else:
                # Try to find role by name
                role = discord.utils.get(interaction.guild.roles, name=mention)
                if role:
                    content = role.mention
                else:
                    # Try to find user by name
                    user = discord.utils.get(interaction.guild.members, name=mention)
                    if user:
                        content = user.mention
                    else:
                        await interaction.followup.send(f"Could not find user or role with name: {mention}", ephemeral=True)
                        return

        # Send the embed with allowed mentions
        await channel.send(
            content=content,
            embed=embed,
            allowed_mentions=discord.AllowedMentions(everyone=True, roles=True, users=True)
        )

        # Send confirmation
        await modal.interaction.response.send_message(f"Embed sent to {channel.mention} successfully!", ephemeral=True)

    except discord.Forbidden:
        await modal.interaction.response.send_message("I don't have permission to send messages in that channel.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in send_embed: {e}")
        await modal.interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)




async def delete_command_messages(interaction):
    """Delete messages related to the command, excluding the final confirmation message."""
    try:
        messages_to_delete = []
        async for message in interaction.channel.history(limit=100):
            # Check if the message is from the user who initiated the command or the bot
            if message.author == interaction.user or message.author == bot.user:
                # Check if the message is part of the command conversation
                if message.id == interaction.id or message.content.startswith("Let's create your embed!"):
                    messages_to_delete.append(message)

        # Delete messages with delay to avoid rate limits
        for message in messages_to_delete:
            try:
                await message.delete()
                await asyncio.sleep(0.5)  # Add 500ms delay between deletions
            except discord.NotFound:
                continue  # Message already deleted
            except discord.Forbidden:
                print(f"Missing permissions to delete message {message.id}")
                continue
            except Exception as e:
                print(f"Error deleting message {message.id}: {e}")
                continue

    except Exception as e:
        print(f"Error in delete_command_messages: {e}")

async def ask_for_input(interaction, prompt):
    """Helper function to ask for user input."""
    await interaction.channel.send(prompt)
    try:
        response = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == interaction.user)
        return response.content
    except asyncio.TimeoutError:
        await interaction.channel.send("You took too long to respond!")
        return None

async def ask_for_channel(interaction, prompt):
    """Helper function to ask for a channel mention."""
    await interaction.channel.send(prompt)
    try:
        response = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == interaction.user)
        channel_id = int(response.content.strip('<>#'))
        return interaction.guild.get_channel(channel_id)
    except (ValueError, TypeError):
        await interaction.channel.send("Invalid channel mention. Please try again.")
        return None
    except asyncio.TimeoutError:
        await interaction.channel.send("You took too long to respond!")
        return None

# Auto Role System
@tree.command(name="set_join_role", description="Set the join role")
@app_commands.default_permissions(administrator=True)
async def set_join_role(interaction: discord.Interaction, role: discord.Role):
    if not log_permission_check(interaction, "set_join_role"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    global join_role_id
    join_role_id = role.id
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Join role set to '{role.name}'.")

@bot.event
async def on_member_join(member):
    if join_role_id:
        role = safe_get_role(member.guild, join_role_id)
        if role:
            success = await safe_add_role(member, role, "Auto-join role assignment")
            if success:
                console_log(f"Auto-join role assigned: {role.name} → {member.name}", "SUCCESS")

# Gang Management System
@tree.command(name="create_gang", description="Create a new gang")
@app_commands.default_permissions(administrator=True)
@app_commands.describe(
    gang_name="Name of the gang to create",
    leader="Member who will lead the gang",
    leader_role="Discord role for the gang leader",
    member_limit="Maximum number of members allowed"
)
async def create_gang(interaction: discord.Interaction, gang_name: str, leader: discord.Member, leader_role: discord.Role, member_limit: int):
    log_command_execution(interaction, f"create_gang ({gang_name})")

    if gang_name in gang_roles:
        # Create professional error embed for existing gang
        error_embed = discord.Embed(
            title="❌ Gang Creation Failed",
            description=f"Gang **{gang_name}** already exists in the system.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Error Details**",
            value=f"```Gang Name: {gang_name}\nStatus: Already Exists\nAction: No changes made```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Suggestion**",
            value="Choose a different gang name or use `/edit_gang` to modify the existing gang.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    try:
        # Log gang creation to file for debugging
        logging.info(f"Creating gang '{gang_name}' with leader {leader.name} (ID: {leader.id})")

        # Assign the leader role safely
        if await validate_role_permissions(interaction.guild, leader_role):
            success = await safe_add_role(leader, leader_role, "Gang leader assignment")
            if not success:
                await interaction.followup.send("❌ Failed to assign leader role. Please check bot permissions.", ephemeral=True)
                return
        else:
            await interaction.followup.send("❌ Bot lacks permission to manage this role. Please check role hierarchy.", ephemeral=True)
            return

        # Store gang data with new permission system
        gang_roles[gang_name] = {
            "leader": leader.id,  # Store as integer
            "leader_role": leader_role.id,
            "members": [],
            "member_limit": member_limit,
            "current_members": 0,
            "member_management_permissions": "leader_only",  # Default to leader only
            "officers": []  # List of officer member IDs for leader_officers permission
        }

        # Store leader data
        gang_leaders[leader.id] = leader_role.id  # Store as integer, not string

        # Log successful creation
        logging.info(f"Gang '{gang_name}' created successfully with {member_limit} member limit")

        await save_data()

        # Create professional gang creation success embed with permission information
        creation_embed = discord.Embed(
            title="🏴‍☠️ Gang Created Successfully",
            description=f"Gang **{gang_name}** has been established and is now operational.",
            color=0x000000  # Professional black theme
        )

        # Add elegant separator for visual hierarchy
        creation_embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        creation_embed.add_field(
            name="**Gang Details**",
            value=f"```Gang Name: {gang_name}\nMember Limit: {member_limit}\nCurrent Members: 0/{member_limit}```",
            inline=False
        )

        creation_embed.add_field(
            name="**Leadership Information**",
            value=f"**Leader:** {leader.mention}\n**Leader Role:** {leader_role.mention}",
            inline=False
        )

        creation_embed.add_field(
            name="**🔐 Member Management**",
            value="```Only the gang leader can add/remove members```",
            inline=False
        )

        creation_embed.add_field(
            name="**🎉 Congratulations**",
            value="Your gang has been successfully created! You can now start recruiting members and building your organization.",
            inline=False
        )

        # Add elegant separator at the bottom
        creation_embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        creation_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=creation_embed)

    except discord.Forbidden:
        # Create professional error embed for permission issues
        error_embed = discord.Embed(
            title="❌ Permission Error",
            description="I don't have permission to assign roles for gang creation.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Permission Issue**",
            value="```Required Permission: Manage Roles\nBot Status: Permission Denied\nAction: Gang creation failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Solution**",
            value="Please ensure the bot has 'Manage Roles' permission and that the bot's role is higher than the roles being assigned.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
    except Exception as e:
        # Create professional error embed for general errors
        error_embed = discord.Embed(
            title="❌ Gang Creation Error",
            description="An unexpected error occurred while creating the gang.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Error Information**",
            value=f"```Error Type: {type(e).__name__}\nError Details: {str(e)}\nAction: Gang creation failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Support**",
            value="Please contact an administrator if this error persists.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)

@tree.command(name="assign_member_role", description="Assign a member role to a gang leader role")
@app_commands.default_permissions(administrator=True)
async def assign_member_role(interaction: discord.Interaction, leader_role: discord.Role, member_role: discord.Role):
    # Check if the leader role is valid
    if leader_role.id not in gang_leaders.values():
        # Create professional error embed for invalid leader role
        error_embed = discord.Embed(
            title="❌ Invalid Leader Role",
            description=f"The role **{leader_role.name}** is not configured as a gang leader role.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Role Status**",
            value=f"```Role: {leader_role.name}\nType: Not a Leader Role\nAction: Role assignment failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Solution**",
            value="Only roles that are configured as gang leader roles can have member roles assigned to them.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    # Ensure the gang_roles dictionary has an entry for the leader role
    gang_name = next((name for name, details in gang_roles.items() if details["leader_role"] == leader_role.id), None)
    if gang_name is None:
        # Create professional error embed for no gang found
        error_embed = discord.Embed(
            title="❌ Gang Not Found",
            description=f"No gang found for the leader role **{leader_role.name}**.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Role Information**",
            value=f"```Leader Role: {leader_role.name}\nStatus: Not Associated with Gang\nAction: Role assignment failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Solution**",
            value="Ensure the leader role is properly configured for a gang using `/create_gang`.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    # Check if the member role is already associated
    if member_role.id in gang_roles[gang_name]["members"]:
        # Create professional error embed for already associated role
        error_embed = discord.Embed(
            title="❌ Role Already Associated",
            description=f"The role **{member_role.name}** is already associated with gang **{gang_name}**.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Association Status**",
            value=f"```Gang: {gang_name}\nLeader Role: {leader_role.name}\nMember Role: {member_role.name}\nStatus: Already Associated```",
            inline=False
        )

        error_embed.add_field(
            name="**ℹ️ Information**",
            value="This member role is already configured for the specified gang.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    # Add the member role to the gang's associated roles
    gang_roles[gang_name]["members"].append(member_role.id)
    await save_data()  # Save the updated gang roles

    # Create professional role assignment success embed
    assignment_embed = discord.Embed(
        title="🎭 Role Assignment Successful",
        description=f"Member role successfully associated with gang leadership.",
        color=0x000000  # Professional black theme
    )

    assignment_embed.add_field(
        name="**Assignment Details**",
        value=f"```Gang: {gang_name}\nLeader Role: {leader_role.name}\nMember Role: {member_role.name}```",
        inline=False
    )

    assignment_embed.add_field(
        name="**✅ Configuration Complete**",
        value="Gang members can now be assigned this role when added to the gang.",
        inline=False
    )

    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
    assignment_embed.set_footer(text=f"Gang Management System • {current_time}")

    await interaction.response.send_message(embed=assignment_embed)

@tree.command(name="manage_gang", description="Manage gang members")
async def manage_gang(interaction: discord.Interaction, member: discord.Member, action: str):
    """
    Manage gang members by adding or removing them.

    Parameters:
    -----------
    member: The member to add/remove
    action: Either 'add' or 'remove'
    """
    try:
        await interaction.response.defer()

        # Find gang where user is the leader
        gang_name = None
        user_id = interaction.user.id

        # Check all gangs to find one where the user is the leader
        for name, details in gang_roles.items():
            if details["leader"] == user_id:
                gang_name = name
                break

        if gang_name is None:
            # Create professional permission error embed
            permission_embed = discord.Embed(
                title="❌ Insufficient Permissions",
                description="You do not have permission to add or remove members from any gang.",
                color=0x000000  # Professional black theme
            )

            permission_embed.add_field(
                name="**Permission Requirements**",
                value="```To manage gang members, you must be:\n• A gang leader```",
                inline=False
            )

            permission_embed.add_field(
                name="**💡 Contact Information**",
                value="Only gang leaders can add or remove members. Contact server administrators if you need assistance.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            permission_embed.set_footer(text=f"Gang Management System • {current_time}")

            await interaction.followup.send(embed=permission_embed, ephemeral=True)
            return

        if action == "add":
            # Check member limit before adding
            if gang_roles[gang_name]["current_members"] >= gang_roles[gang_name]["member_limit"]:
                # Create professional error embed for member limit reached
                error_embed = discord.Embed(
                    title="❌ Member Limit Reached",
                    description=f"Cannot add more members to gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Capacity Information**",
                    value=f"```Current Members: {gang_roles[gang_name]['current_members']}\nMember Limit: {gang_roles[gang_name]['member_limit']}\nAvailable Slots: 0```",
                    inline=False
                )

                error_embed.add_field(
                    name="**💡 Solution**",
                    value="Contact an administrator to increase the member limit or remove inactive members.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed)
                return

            if member.id not in gang_roles[gang_name]["members"]:
                # Get all member roles associated with the gang
                member_role_ids = [role_id for role_id in gang_roles[gang_name]["members"] if isinstance(role_id, int)]

                if not member_role_ids:
                    await interaction.followup.send("No member roles are associated with this gang. Please add a member role first.")
                    return

                # Try to find a valid member role
                member_role = None
                for role_id in member_role_ids:
                    role = interaction.guild.get_role(role_id)
                    if role:
                        member_role = role
                        break

                if member_role:
                    # Send gang invitation instead of directly adding
                    success, message = await send_gang_invitation(gang_name, interaction.user, member, interaction)

                    if success:
                        # Create professional response embed for invitation sent
                        response_embed = discord.Embed(
                            title="📨 Gang Invitation Sent",
                            description=f"Successfully sent gang invitation to **{member.display_name}**.",
                            color=0x000000  # Professional black theme
                        )

                        response_embed.add_field(
                            name="**Invitation Details**",
                            value=f"```Target Member: {member.display_name}\nGang: {gang_name}\nStatus: Invitation Pending\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                            inline=False
                        )

                        response_embed.add_field(
                            name="**📋 Next Steps**",
                            value=f"**{member.display_name}** will receive a DM with Accept/Decline buttons. They can respond at any time to join or decline the invitation.",
                            inline=False
                        )

                        response_embed.add_field(
                            name="**✅ Invitation Status**",
                            value="The invitation has been sent and will remain active until the user responds.",
                            inline=False
                        )

                        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                        response_embed.set_footer(text=f"Gang Management System • {current_time}")

                        await interaction.followup.send(embed=response_embed)
                    else:
                        # Create professional error embed for invitation failure
                        error_embed = discord.Embed(
                            title="❌ Invitation Failed",
                            description=f"Could not send gang invitation to **{member.display_name}**.",
                            color=0x000000  # Professional black theme
                        )

                        error_embed.add_field(
                            name="**Error Details**",
                            value=f"```Target Member: {member.display_name}\nGang: {gang_name}\nError: {message}```",
                            inline=False
                        )

                        error_embed.add_field(
                            name="**💡 Common Solutions**",
                            value="• Ask the user to enable DMs from server members\n• Try again after they adjust their privacy settings\n• Contact the user through other means to inform them",
                            inline=False
                        )

                        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                        error_embed.set_footer(text=f"Gang Management System • {current_time}")

                        await interaction.followup.send(embed=error_embed)
                else:
                    # Create professional error embed for no valid member role
                    error_embed = discord.Embed(
                        title="❌ Configuration Error",
                        description="Could not find a valid member role for this gang.",
                        color=0x000000  # Professional black theme
                    )

                    error_embed.add_field(
                        name="**Configuration Issue**",
                        value=f"```Gang: {gang_name}\nIssue: No member role configured\nAction: Member addition failed```",
                        inline=False
                    )

                    error_embed.add_field(
                        name="**💡 Solution**",
                        value="Contact an administrator to configure member roles for this gang using `/assign_member_role`.",
                        inline=False
                    )

                    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                    error_embed.set_footer(text=f"Gang Management System • {current_time}")

                    await interaction.followup.send(embed=error_embed)
            else:
                # Create professional error embed for already a member
                error_embed = discord.Embed(
                    title="❌ Already a Member",
                    description=f"**{member.display_name}** is already a member of gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Member Status**",
                    value=f"```Member: {member.display_name}\nGang: {gang_name}\nStatus: Already Active Member```",
                    inline=False
                )

                error_embed.add_field(
                    name="**ℹ️ Information**",
                    value="This user is already an active member of the specified gang.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed)

        elif action == "remove":
            if member.id in gang_roles[gang_name]["members"]:
                # Get member role IDs
                member_role_ids = [role_id for role_id in gang_roles[gang_name]["members"] if isinstance(role_id, int)]

                # Remove member roles
                for role_id in member_role_ids:
                    role = interaction.guild.get_role(role_id)
                    if role and role in member.roles:
                        await member.remove_roles(role)

                gang_roles[gang_name]["members"].remove(member.id)
                gang_roles[gang_name]["current_members"] -= 1
                await save_data()

                # Send targeted notifications for member removal
                await send_targeted_member_notification(gang_name, "member_removed", interaction, target_member=member)

                # Create professional response embed for the command interface
                response_embed = discord.Embed(
                    title="👥 Member Removed Successfully",
                    description=f"Successfully removed **{member.display_name}** from gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                response_embed.add_field(
                    name="**Removal Details**",
                    value=f"```Former Member: {member.display_name}\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                    inline=False
                )

                response_embed.add_field(
                    name="**✅ Notification Status**",
                    value="Targeted notifications have been sent to relevant parties (you, the removed member, and gang leader if applicable).",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                response_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=response_embed)
            else:
                # Create professional error embed for not a member
                error_embed = discord.Embed(
                    title="❌ Not a Member",
                    description=f"**{member.display_name}** is not a member of gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Member Status**",
                    value=f"```Member: {member.display_name}\nGang: {gang_name}\nStatus: Not a Member```",
                    inline=False
                )

                error_embed.add_field(
                    name="**ℹ️ Information**",
                    value="This user is not currently a member of the specified gang.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed)

        else:
            # Create professional error embed for invalid action
            error_embed = discord.Embed(
                title="❌ Invalid Action",
                description="The specified action is not valid for member management.",
                color=0x000000  # Professional black theme
            )

            error_embed.add_field(
                name="**Valid Actions**",
                value="```add    - Add a member to the gang\nremove - Remove a member from the gang```",
                inline=False
            )

            error_embed.add_field(
                name="**💡 Usage**",
                value="Please specify either 'add' or 'remove' as the action parameter.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            error_embed.set_footer(text=f"Gang Management System • {current_time}")

            await interaction.followup.send(embed=error_embed)

    except discord.Forbidden:
        logging.error(f"Permission error in manage_gang: {interaction.guild.id}")
        await interaction.followup.send("I don't have permission to manage roles.", ephemeral=True)
    except discord.HTTPException as e:
        logging.error(f"Discord API error in manage_gang: {e}")
        await interaction.followup.send(f"Discord API error: {str(e)}", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in manage_gang: {e}")
        await interaction.followup.send("An error occurred while managing the gang.", ephemeral=True)

@tree.command(name="remove_gang", description="Remove a gang")
@app_commands.default_permissions(administrator=True)
async def remove_gang(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if name in gang_roles:
        del gang_roles[name]
        # Also remove from gang_leaders if they exist
        gang_leader_id = gang_leaders.pop(next((id for id, role in gang_leaders.items() if role == name), None), None)
        if gang_leader_id:
            del gang_leaders[gang_leader_id]
        await save_data()  # Save the updated gang roles

        # Create professional gang removal success embed
        removal_embed = discord.Embed(
            title="🗑️ Gang Removed Successfully",
            description=f"Gang **{name}** has been permanently dissolved.",
            color=0x000000  # Professional black theme
        )

        removal_embed.add_field(
            name="**Removal Details**",
            value=f"```Gang Name: {name}\nStatus: Permanently Dissolved\nData: Completely Removed```",
            inline=False
        )

        removal_embed.add_field(
            name="**⚠️ Important Notice**",
            value="All gang data, roles, and member associations have been permanently removed from the system.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        removal_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=removal_embed)
    else:
        # Create professional error embed for gang not found
        error_embed = discord.Embed(
            title="❌ Gang Not Found",
            description=f"Gang **{name}** does not exist in the system.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Error Details**",
            value=f"```Requested Gang: {name}\nStatus: Not Found\nAction: No changes made```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Suggestion**",
            value="Use `/check_gang_list` to view all existing gangs in the system.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)

@tree.command(name="check_gang_list", description="Check the list of gangs and their members")
@app_commands.default_permissions(administrator=True)
async def check_gang_list(interaction: discord.Interaction):
    if not gang_roles:
        # Create professional "no gangs" embed
        no_gangs_embed = discord.Embed(
            title="📋 Gang List",
            description="No gangs are currently registered in the system.",
            color=0x000000  # Professional black theme
        )

        no_gangs_embed.add_field(
            name="**System Status**",
            value="```No Active Gangs\nTotal Gangs: 0\nStatus: Empty```",
            inline=False
        )

        no_gangs_embed.add_field(
            name="**💡 Getting Started**",
            value="Use `/create_gang` to establish the first gang in your server.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        no_gangs_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=no_gangs_embed)
        return

    # Create professional gang list embed
    embed = discord.Embed(
        title="🏴‍☠️ Gang Registry",
        description=f"Complete overview of all {len(gang_roles)} registered gangs in the system.",
        color=0x000000  # Professional black theme
    )

    # Add elegant separator for visual hierarchy
    embed.add_field(
        name="\u200b",  # Invisible field name for spacing
        value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    for gang_name, details in gang_roles.items():
        leader = interaction.guild.get_member(details["leader"])
        members = [interaction.guild.get_member(member_id) for member_id in details["members"] if isinstance(member_id, int)]
        members_list = ", ".join(member.mention for member in members if member) or "No members"

        strike_count = gang_strikes.get(gang_name, 0)
        strike_text = f"⚠️ {strike_count} Strikes" if strike_count > 0 else "✅ Clean Record"

        # Get permission description
        permission_level = details.get("member_management_permissions", "leader_only")
        permission_descriptions = {
            "leader_only": "Leader Only",
            "leader_officers": "Leader + Officers",
            "all_members": "All Members"
        }
        permission_text = permission_descriptions.get(permission_level, "Unknown")

        embed.add_field(
            name=f"🏴‍☠️ **{gang_name}**",
            value=(
                f"**Leader:** {leader.mention if leader else 'Unknown'}\n"
                f"**Members:** {details['current_members']}/{details['member_limit']}\n"
                f"**Status:** {strike_text}\n"
                f"**Member Management:** {permission_text}\n"
                f"**Member List:** {members_list}"
            ),
            inline=False
        )

        # Add separator between gangs
        embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

    # Add summary information
    total_members = sum(details['current_members'] for details in gang_roles.values())
    total_strikes = sum(gang_strikes.values())

    embed.add_field(
        name="**📊 System Summary**",
        value=f"```Total Gangs: {len(gang_roles)}\nTotal Members: {total_members}\nTotal Strikes: {total_strikes}```",
        inline=False
    )

    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
    embed.set_footer(text=f"Gang Management System • {current_time}")

    await interaction.response.send_message(embed=embed)

# Add this command in the Gang Management System section
@tree.command(name="remove_strike", description="Remove a strike from a gang")
@app_commands.default_permissions(administrator=True)
async def remove_strike(interaction: discord.Interaction, gang_name: str, reason: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if gang_name not in gang_roles:
        # Create professional error embed for gang not found
        error_embed = discord.Embed(
            title="❌ Gang Not Found",
            description=f"Gang **{gang_name}** does not exist in the system.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Error Details**",
            value=f"```Requested Gang: {gang_name}\nStatus: Not Found\nAction: Strike removal failed```",
            inline=False
        )

        error_embed.add_field(
            name="**💡 Suggestion**",
            value="Use `/check_gang_list` to view all existing gangs in the system.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    if gang_name not in gang_strikes or gang_strikes[gang_name] <= 0:
        # Create professional error embed for no strikes
        error_embed = discord.Embed(
            title="❌ No Strikes to Remove",
            description=f"Gang **{gang_name}** has no strikes that can be removed.",
            color=0x000000  # Professional black theme
        )

        error_embed.add_field(
            name="**Strike Status**",
            value=f"```Gang: {gang_name}\nCurrent Strikes: {gang_strikes.get(gang_name, 0)}\nAction: Cannot remove strikes```",
            inline=False
        )

        error_embed.add_field(
            name="**ℹ️ Information**",
            value="Gangs must have at least one strike before strikes can be removed.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        error_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.response.send_message(embed=error_embed)
        return

    gang_strikes[gang_name] -= 1
    await save_data()

    # Create professional strike removal notification embed
    embed = await create_gang_notification_embed(
        title="Strike Removed",
        description=f"Great news! A strike has been removed from your gang **{gang_name}**.",
        gang_name=gang_name,
        notification_type="strike_removed",
        current_strikes=gang_strikes[gang_name],
        reason=reason or "Administrative decision"
    )

    # Send professional embed notifications to all gang members
    await notify_gang_members_with_embed(gang_name, embed, interaction)

    # Create professional response embed for the administrator
    response_embed = discord.Embed(
        title="✅ Strike Removal Successful",
        description=f"Strike successfully removed from gang **{gang_name}**.",
        color=0x000000  # Professional black theme
    )

    response_embed.add_field(
        name="**Gang Details**",
        value=f"```Gang: {gang_name}\nCurrent Strikes: {gang_strikes[gang_name]}\nReason: {reason or 'Administrative decision'}```",
        inline=False
    )

    response_embed.add_field(
        name="**✅ Notification Status**",
        value="All gang members have been notified of this strike removal.",
        inline=False
    )

    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
    response_embed.set_footer(text=f"Gang Management System • {current_time}")

    await interaction.response.send_message(embed=response_embed)

# Add this command for gang leaders to view their gang stats
@tree.command(name="view_my_gang", description="View your gang's statistics")
async def view_my_gang(interaction: discord.Interaction):
    try:
        await interaction.response.defer()

        # Find gang where user is leader
        user_id = interaction.user.id
        gang_name = None

        for name, details in gang_roles.items():
            if details["leader"] == user_id:
                gang_name = name
                break

        if gang_name is None:
            await interaction.followup.send("You are not a gang leader.", ephemeral=True)
            return

        gang_details = gang_roles[gang_name]
        # Get member list excluding role IDs
        member_ids = [mid for mid in gang_details["members"] if isinstance(mid, int)]
        members = [interaction.guild.get_member(member_id) for member_id in member_ids]
        members_list = ", ".join(member.mention for member in members if member) or "No members"

        # Create professional gang statistics embed
        embed = discord.Embed(
            title=f"🏴‍☠️ Gang Statistics",
            description=f"Detailed overview of your gang **{gang_name}**.",
            color=0x000000  # Professional black theme
        )

        # Add elegant separator for visual hierarchy
        embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        embed.add_field(
            name="**👥 Membership Information**",
            value=f"```Current Members: {gang_details['current_members']}/{gang_details['member_limit']}\nAvailable Slots: {gang_details['member_limit'] - gang_details['current_members']}```",
            inline=False
        )

        embed.add_field(
            name="**📋 Member Roster**",
            value=members_list,
            inline=False
        )

        # Add strike information with appropriate styling
        strike_count = gang_strikes.get(gang_name, 0)
        strike_status = "⚠️ Warning Status" if strike_count > 0 else "✅ Good Standing"

        embed.add_field(
            name="**⚖️ Disciplinary Record**",
            value=f"```Status: {strike_status}\nCurrent Strikes: {strike_count}\nRecord: {'Clean' if strike_count == 0 else 'Requires Attention'}```",
            inline=False
        )

        # Add elegant separator at the bottom
        embed.add_field(
            name="\u200b",  # Invisible field name for spacing
            value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.followup.send(embed=embed)

    except Exception as e:
        print(f"Error in view_my_gang: {e}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
        except:
            pass

# Add these helper functions in the Gang Management System section, before the commands

async def create_gang_notification_embed(title, description, gang_name, notification_type, **kwargs):
    """Create a professional gang notification embed matching the bot's theme"""
    embed = discord.Embed(
        title=f"🏴‍☠️ {title}",
        description=description,
        color=0x000000  # Professional black theme matching ticket system
    )

    # Add elegant separator for visual hierarchy
    embed.add_field(
        name="\u200b",  # Invisible field name for spacing
        value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add gang information
    embed.add_field(
        name="**Gang Information**",
        value=f"```{gang_name}```",
        inline=False
    )

    # Add notification-specific fields
    if notification_type == "strike_added":
        embed.add_field(
            name="**Strike Details**",
            value=f"**Current Strikes:** {kwargs.get('current_strikes', 'Unknown')}\n**Reason:** {kwargs.get('reason', 'No reason provided')}",
            inline=False
        )
        embed.add_field(
            name="**⚠️ Important Notice**",
            value="Multiple strikes may result in gang penalties or dissolution. Please review gang conduct guidelines.",
            inline=False
        )

    elif notification_type == "strike_removed":
        embed.add_field(
            name="**Strike Removal Details**",
            value=f"**Current Strikes:** {kwargs.get('current_strikes', 'Unknown')}\n**Reason:** {kwargs.get('reason', 'Administrative decision')}",
            inline=False
        )
        embed.add_field(
            name="**✅ Good News**",
            value="Your gang's conduct has been recognized. Keep up the positive behavior!",
            inline=False
        )

    elif notification_type == "member_added":
        embed.add_field(
            name="**New Member Details**",
            value=f"**Member:** {kwargs.get('member_mention', 'Unknown')}\n**Role:** {kwargs.get('role_name', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**👥 Welcome**",
            value="Please welcome your new gang member and help them integrate into the group!",
            inline=False
        )

    elif notification_type == "member_removed":
        embed.add_field(
            name="**Member Removal Details**",
            value=f"**Former Member:** {kwargs.get('member_mention', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**📋 Update**",
            value="Gang membership has been updated. Continue building your team!",
            inline=False
        )

    elif notification_type == "settings_changed":
        changes_text = "\n".join([f"• {change}" for change in kwargs.get('changes', [])])
        embed.add_field(
            name="**Changes Made**",
            value=f"```{changes_text}```",
            inline=False
        )
        embed.add_field(
            name="**⚙️ Administrative Update**",
            value="Gang settings have been modified by server administration. These changes are now in effect.",
            inline=False
        )

    elif notification_type == "leadership_change":
        embed.add_field(
            name="**Leadership Transition**",
            value=f"**Former Leader:** {kwargs.get('old_leader', 'Unknown')}\n**New Leader:** {kwargs.get('new_leader', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**👑 Leadership Change**",
            value="Gang leadership has been transferred. Please support your new leader!",
            inline=False
        )

    elif notification_type == "action_confirmation":
        embed.add_field(
            name="**Action Details**",
            value=f"**Action:** {kwargs.get('action', 'Unknown')}\n**Target:** {kwargs.get('target', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**✅ Confirmation**",
            value="Your action has been completed successfully. All relevant parties have been notified.",
            inline=False
        )

    elif notification_type == "membership_change":
        action = kwargs.get('action', 'Unknown')
        if "Added" in action:
            embed.add_field(
                name="**Welcome Information**",
                value=f"**Added by:** {kwargs.get('added_by', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
                inline=False
            )
            embed.add_field(
                name="**🎉 Welcome**",
                value="Welcome to the gang! Please familiarize yourself with gang rules and participate actively in gang activities.",
                inline=False
            )
        else:
            embed.add_field(
                name="**Removal Information**",
                value=f"**Removed by:** {kwargs.get('removed_by', 'Unknown')}\n**Former Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
                inline=False
            )
            embed.add_field(
                name="**📋 Notice**",
                value="Your membership in this gang has ended. Thank you for your participation.",
                inline=False
            )

    elif notification_type == "leadership_notification":
        embed.add_field(
            name="**Action Summary**",
            value=f"**Action:** {kwargs.get('action', 'Unknown')}\n**Target:** {kwargs.get('target', 'Unknown')}\n**Performed by:** {kwargs.get('performed_by', 'Unknown')}\n**Current Count:** {kwargs.get('current_count', 'Unknown')}/{kwargs.get('member_limit', 'Unknown')}",
            inline=False
        )
        embed.add_field(
            name="**👑 Leadership Notice**",
            value="This action was performed by a gang member with appropriate permissions. No action required from you.",
            inline=False
        )

    # Add elegant separator at the bottom
    embed.add_field(
        name="\u200b",  # Invisible field name for spacing
        value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
        inline=False
    )

    # Add professional footer with timestamp
    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
    embed.set_footer(text=f"Gang Management System • {current_time}")

    return embed

def check_member_management_permission(gang_name, user_id):
    """Check if a user has permission to add/remove members from a gang"""
    if gang_name not in gang_roles:
        return False, "Gang not found"

    gang_data = gang_roles[gang_name]
    permission_level = gang_data.get("member_management_permissions", "leader_only")

    # Leader always has permission
    if gang_data["leader"] == user_id:
        return True, "leader"

    # Check permission levels
    if permission_level == "leader_only":
        return False, "Only the gang leader can add/remove members"

    elif permission_level == "leader_officers":
        # Check if user is an officer
        if user_id in gang_data.get("officers", []):
            return True, "officer"
        return False, "Only the gang leader and designated officers can add/remove members"

    elif permission_level == "all_members":
        # Check if user is a member
        if user_id in gang_data["members"]:
            return True, "member"
        return False, "Only gang members can add/remove other members"

    return False, "Invalid permission configuration"

async def get_gang_notification_recipients(gang_name, guild):
    """Get all users who should be notified about gang invitation responses"""
    if gang_name not in gang_roles:
        return []

    gang_data = gang_roles[gang_name]
    recipients = []

    # Always include the gang leader
    leader_id = gang_data["leader"]
    leader = guild.get_member(leader_id)
    if leader:
        recipients.append(leader)

    # Include officers if they exist
    officers = gang_data.get("officers", [])
    for officer_id in officers:
        officer = guild.get_member(officer_id)
        if officer and officer not in recipients:
            recipients.append(officer)

    # For "all_members" permission level, include all members with manage gang permissions
    permission_level = gang_data.get("member_management_permissions", "leader_only")
    if permission_level == "all_members":
        for member_id in gang_data.get("members", []):
            member = guild.get_member(member_id)
            if member and member not in recipients:
                recipients.append(member)

    return recipients

class GangInvitationView(discord.ui.View):
    """Persistent view for gang invitation confirmations"""

    def __init__(self, invitation_id: str):
        super().__init__(timeout=None)  # Persistent view with no timeout
        self.invitation_id = invitation_id

    @discord.ui.button(label="Accept", style=discord.ButtonStyle.success, emoji="✅", custom_id="gang_invite_accept")
    async def accept_invitation(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle gang invitation acceptance"""
        try:
            await interaction.response.defer()

            # Get invitation details from database
            if self.invitation_id not in gang_invitations:
                # Create professional error embed for expired invitation
                error_embed = discord.Embed(
                    title="❌ Invitation Expired",
                    description="This gang invitation is no longer valid.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Status Information**",
                    value="```Status: Invitation Expired\nReason: No longer in system\nAction: Cannot process acceptance```",
                    inline=False
                )

                error_embed.add_field(
                    name="**💡 Next Steps**",
                    value="Contact the gang leader to request a new invitation.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed, ephemeral=True)
                return

            invitation = gang_invitations[self.invitation_id]
            gang_name = invitation["gang_name"]
            inviter_id = invitation["inviter_id"]
            target_id = invitation["target_id"]

            # Verify the user responding is the target
            if interaction.user.id != target_id:
                await interaction.followup.send("This invitation is not for you.", ephemeral=True)
                return

            # Check if gang still exists
            if gang_name not in gang_roles:
                # Create professional error embed for gang no longer exists
                error_embed = discord.Embed(
                    title="❌ Gang No Longer Exists",
                    description=f"The gang **{gang_name}** no longer exists in the system.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Status Information**",
                    value=f"```Gang: {gang_name}\nStatus: Deleted/Disbanded\nAction: Cannot process acceptance```",
                    inline=False
                )

                error_embed.add_field(
                    name="**ℹ️ Information**",
                    value="The gang may have been disbanded or deleted after this invitation was sent.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                # Remove expired invitation
                del gang_invitations[self.invitation_id]
                await save_data()

                await interaction.followup.send(embed=error_embed, ephemeral=True)
                return

            # Check if user is already a member
            if target_id in gang_roles[gang_name]["members"]:
                # Create professional error embed for already a member
                error_embed = discord.Embed(
                    title="❌ Already a Member",
                    description=f"You are already a member of gang **{gang_name}**.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Member Status**",
                    value=f"```Gang: {gang_name}\nStatus: Active Member\nAction: Invitation no longer needed```",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                # Remove invitation since user is already a member
                del gang_invitations[self.invitation_id]
                await save_data()

                await interaction.followup.send(embed=error_embed, ephemeral=True)
                return

            # Check member limit
            if gang_roles[gang_name]["current_members"] >= gang_roles[gang_name]["member_limit"]:
                # Create professional error embed for member limit reached
                error_embed = discord.Embed(
                    title="❌ Gang Full",
                    description=f"Gang **{gang_name}** has reached its member limit.",
                    color=0x000000  # Professional black theme
                )

                error_embed.add_field(
                    name="**Capacity Information**",
                    value=f"```Current Members: {gang_roles[gang_name]['current_members']}\nMember Limit: {gang_roles[gang_name]['member_limit']}\nAvailable Slots: 0```",
                    inline=False
                )

                error_embed.add_field(
                    name="**💡 Solution**",
                    value="Contact the gang leader to increase the member limit or wait for a slot to become available.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                error_embed.set_footer(text=f"Gang Management System • {current_time}")

                await interaction.followup.send(embed=error_embed, ephemeral=True)
                return

            # Add member to gang
            gang_roles[gang_name]["members"].append(target_id)
            gang_roles[gang_name]["current_members"] += 1

            # Get guild from invitation data since interaction.guild is None in DMs
            guild_id = invitation["guild_id"]
            guild = bot.get_guild(guild_id)
            if not guild:
                logging.error(f"Could not find guild {guild_id} for gang invitation")
                # Still save the member addition but skip role assignment
                await save_data()

                # Create success embed without role assignment
                success_embed = discord.Embed(
                    title="🎉 Welcome to the Gang!",
                    description=f"You have successfully joined gang **{gang_name}**!",
                    color=0x000000  # Professional black theme
                )

                success_embed.add_field(
                    name="**Gang Information**",
                    value=f"```Gang: {gang_name}\nStatus: Active Member\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                    inline=False
                )

                success_embed.add_field(
                    name="**⚠️ Note**",
                    value="You have been added to the gang, but roles could not be assigned automatically. Contact a server administrator.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                success_embed.set_footer(text=f"Gang Management System • {current_time}")

                # Remove invitation from system
                del gang_invitations[self.invitation_id]
                await save_data()

                # Update the original message to show acceptance
                self.clear_items()
                await interaction.followup.edit_message(interaction.message.id, embed=success_embed, view=self)
                return

            member = guild.get_member(target_id)
            if not member:
                logging.error(f"Could not find member {target_id} in guild {guild_id}")
                # Still save the member addition but skip role assignment
                await save_data_optimized()

                # Create success embed without role assignment
                success_embed = discord.Embed(
                    title="🎉 Welcome to the Gang!",
                    description=f"You have successfully joined gang **{gang_name}**!",
                    color=0x000000  # Professional black theme
                )

                success_embed.add_field(
                    name="**Gang Information**",
                    value=f"```Gang: {gang_name}\nStatus: Active Member\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                    inline=False
                )

                success_embed.add_field(
                    name="**⚠️ Note**",
                    value="You have been added to the gang, but roles could not be assigned automatically. Contact a server administrator.",
                    inline=False
                )

                current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                success_embed.set_footer(text=f"Gang Management System • {current_time}")

                # Remove invitation from system
                del gang_invitations[self.invitation_id]
                await save_data()

                # Update the original message to show acceptance
                self.clear_items()
                await interaction.followup.edit_message(interaction.message.id, embed=success_embed, view=self)
                return

            # Find member role for this gang
            member_role = None
            for role in guild.roles:
                if role.id in gang_roles[gang_name]["members"] and isinstance(role.id, int):
                    member_role = role
                    break

            if member_role:
                success = await safe_add_role(member, member_role, "Gang member role assignment")
                if success:
                    logging.info(f"Assigned role {member_role.name} to new gang member {member.display_name}")
                else:
                    logging.warning(f"Could not assign role to new gang member {member.display_name}")

            # Remove invitation from system
            del gang_invitations[self.invitation_id]
            await save_data()

            # Create acceptance confirmation embed
            success_embed = discord.Embed(
                title="🎉 Welcome to the Gang!",
                description=f"You have successfully joined gang **{gang_name}**!",
                color=0x000000  # Professional black theme
            )

            success_embed.add_field(
                name="**Gang Information**",
                value=f"```Gang: {gang_name}\nStatus: Active Member\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                inline=False
            )

            success_embed.add_field(
                name="**🎊 Welcome Message**",
                value="Welcome to the gang! Please familiarize yourself with gang rules and participate actively in gang activities.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            success_embed.set_footer(text=f"Gang Management System • {current_time}")

            # Update the original message to show acceptance
            self.clear_items()
            await interaction.followup.edit_message(interaction.message.id, embed=success_embed, view=self)

            # Notify all authorized gang members about the acceptance
            try:
                recipients = await get_gang_notification_recipients(gang_name, guild)
                inviter_member = guild.get_member(inviter_id)

                for recipient in recipients:
                    # Create personalized notification embed
                    if recipient.id == inviter_id:
                        # Special message for the person who sent the invitation
                        notification_embed = discord.Embed(
                            title="🎉 Invitation Accepted!",
                            description=f"**{member.display_name}** has accepted your invitation to join gang **{gang_name}**!",
                            color=0x00ff00  # Green color for success
                        )
                    else:
                        # General notification for other authorized members
                        notification_embed = discord.Embed(
                            title="🎉 New Member Joined",
                            description=f"**{member.display_name}** has accepted an invitation and joined gang **{gang_name}**.",
                            color=0x00ff00  # Green color for success
                        )

                    notification_embed.add_field(
                        name="**Gang Information**",
                        value=f"```Gang: {gang_name}\nNew Member: {member.display_name}\nInvited by: {inviter_member.display_name if inviter_member else 'Unknown'}\nCurrent Count: {gang_roles[gang_name]['current_members']}/{gang_roles[gang_name]['member_limit']}```",
                        inline=False
                    )

                    current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                    notification_embed.set_footer(text=f"Gang Management System • {current_time}")

                    try:
                        await recipient.send(embed=notification_embed)
                        logging.info(f"Sent gang invitation acceptance notification to {recipient.display_name}")
                    except Exception as e:
                        logging.warning(f"Could not notify gang member {recipient.id}: {e}")

            except Exception as e:
                logging.error(f"Error sending gang invitation acceptance notifications: {e}")

        except Exception as e:
            logging.error(f"Error processing gang invitation acceptance: {e}")
            await interaction.followup.send("An error occurred while processing your acceptance. Please try again.", ephemeral=True)

    @discord.ui.button(label="Decline", style=discord.ButtonStyle.danger, emoji="❌", custom_id="gang_invite_decline")
    async def decline_invitation(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle gang invitation decline"""
        try:
            await interaction.response.defer()

            # Get invitation details from database
            if self.invitation_id not in gang_invitations:
                await interaction.followup.send("This invitation is no longer valid.", ephemeral=True)
                return

            invitation = gang_invitations[self.invitation_id]
            gang_name = invitation["gang_name"]
            target_id = invitation["target_id"]
            inviter_id = invitation["inviter_id"]
            guild_id = invitation["guild_id"]

            # Verify the user responding is the target
            if interaction.user.id != target_id:
                await interaction.followup.send("This invitation is not for you.", ephemeral=True)
                return

            # Get guild and member info for notifications
            guild = bot.get_guild(guild_id)
            target_member = guild.get_member(target_id) if guild else None

            # Remove invitation from system
            del gang_invitations[self.invitation_id]
            await save_data()

            # Create decline confirmation embed
            decline_embed = discord.Embed(
                title="❌ Invitation Declined",
                description=f"You have declined the invitation to join gang **{gang_name}**.",
                color=0x000000  # Professional black theme
            )

            decline_embed.add_field(
                name="**Decision Recorded**",
                value=f"```Gang: {gang_name}\nStatus: Invitation Declined\nAction: No further action required```",
                inline=False
            )

            decline_embed.add_field(
                name="**ℹ️ Information**",
                value="You can still be invited to join this gang again in the future if desired.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            decline_embed.set_footer(text=f"Gang Management System • {current_time}")

            # Update the original message to show decline
            self.clear_items()
            await interaction.followup.edit_message(interaction.message.id, embed=decline_embed, view=self)

            # Notify all authorized gang members about the decline
            if guild and target_member:
                try:
                    recipients = await get_gang_notification_recipients(gang_name, guild)
                    inviter_member = guild.get_member(inviter_id)

                    for recipient in recipients:
                        # Create personalized notification embed
                        if recipient.id == inviter_id:
                            # Special message for the person who sent the invitation
                            notification_embed = discord.Embed(
                                title="❌ Invitation Declined",
                                description=f"**{target_member.display_name}** has declined your invitation to join gang **{gang_name}**.",
                                color=0xff6b6b  # Red color for decline
                            )
                        else:
                            # General notification for other authorized members
                            notification_embed = discord.Embed(
                                title="❌ Invitation Declined",
                                description=f"**{target_member.display_name}** has declined an invitation to join gang **{gang_name}**.",
                                color=0xff6b6b  # Red color for decline
                            )

                        notification_embed.add_field(
                            name="**Gang Information**",
                            value=f"```Gang: {gang_name}\nDeclined by: {target_member.display_name}\nInvited by: {inviter_member.display_name if inviter_member else 'Unknown'}\nStatus: Invitation Declined```",
                            inline=False
                        )

                        notification_embed.add_field(
                            name="**ℹ️ Information**",
                            value="The user can still be invited again in the future if desired.",
                            inline=False
                        )

                        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                        notification_embed.set_footer(text=f"Gang Management System • {current_time}")

                        try:
                            await recipient.send(embed=notification_embed)
                            logging.info(f"Sent gang invitation decline notification to {recipient.display_name}")
                        except Exception as e:
                            logging.warning(f"Could not notify gang member {recipient.id}: {e}")

                except Exception as e:
                    logging.error(f"Error sending gang invitation decline notifications: {e}")

        except Exception as e:
            logging.error(f"Error processing gang invitation decline: {e}")
            await interaction.followup.send("An error occurred while processing your decline. Please try again.", ephemeral=True)

async def send_gang_invitation(gang_name, inviter, target_member, interaction):
    """Send a professional gang invitation with confirmation buttons"""
    try:
        # Generate unique invitation ID
        invitation_id = f"{gang_name}_{target_member.id}_{int(datetime.now().timestamp())}"

        # Store invitation in database
        gang_invitations[invitation_id] = {
            "gang_name": gang_name,
            "inviter_id": inviter.id,
            "target_id": target_member.id,
            "guild_id": interaction.guild.id,
            "created_at": datetime.now().isoformat(),
            "status": "pending"
        }
        await save_data()

        # Get gang information
        gang_data = gang_roles[gang_name]

        # Create professional invitation embed
        invitation_embed = discord.Embed(
            title="🏴‍☠️ Gang Invitation",
            description=f"You have been invited to join gang **{gang_name}**!",
            color=0x000000  # Professional black theme
        )

        invitation_embed.add_field(
            name="**Gang Information**",
            value=f"```Gang Name: {gang_name}\nCurrent Members: {gang_data['current_members']}/{gang_data['member_limit']}\nInvited by: {inviter.display_name}```",
            inline=False
        )

        invitation_embed.add_field(
            name="**📋 What This Means**",
            value="Joining this gang will grant you access to gang-specific roles, activities, and community features.",
            inline=False
        )

        invitation_embed.add_field(
            name="**⚡ Action Required**",
            value="Please click **Accept** to join the gang or **Decline** to politely refuse the invitation.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        invitation_embed.set_footer(text=f"Gang Management System • {current_time}")

        # Create persistent view with confirmation buttons
        view = GangInvitationView(invitation_id)

        # Send invitation to target member
        try:
            await target_member.send(embed=invitation_embed, view=view)
            return True, "Invitation sent successfully"
        except discord.Forbidden:
            # Remove invitation if we can't send DM
            del gang_invitations[invitation_id]
            await save_data()
            return False, "Could not send invitation - user has DMs disabled"
        except Exception as e:
            # Remove invitation if sending failed
            del gang_invitations[invitation_id]
            await save_data()
            return False, f"Failed to send invitation: {str(e)}"

    except Exception as e:
        logging.error(f"Error creating gang invitation: {e}")
        return False, f"Error creating invitation: {str(e)}"

async def restore_gang_invitation_views():
    """Restore persistent gang invitation views on bot startup with enhanced error handling"""
    try:
        logging.info(f"Starting gang invitation view restoration...")
        logging.info(f"Found {len(gang_invitations)} pending gang invitations")

        if not gang_invitations:
            logging.info("No pending gang invitations to restore")
            return

        restored_count = 0
        failed_count = 0

        # Register all pending gang invitation views
        for invitation_id, invitation_data in gang_invitations.items():
            try:
                # Validate invitation data
                if not isinstance(invitation_data, dict):
                    logging.warning(f"Invalid invitation data for {invitation_id}: {type(invitation_data)}")
                    failed_count += 1
                    continue

                required_fields = ["gang_name", "inviter_id", "target_id", "guild_id"]
                missing_fields = [field for field in required_fields if field not in invitation_data]

                if missing_fields:
                    logging.warning(f"Invitation {invitation_id} missing required fields: {missing_fields}")
                    failed_count += 1
                    continue

                # Create and register the view
                view = GangInvitationView(invitation_id)
                bot.add_view(view)
                restored_count += 1

                logging.info(f"Restored gang invitation view for invitation: {invitation_id} (Gang: {invitation_data.get('gang_name', 'Unknown')})")

            except Exception as e:
                logging.error(f"Error restoring individual invitation view {invitation_id}: {e}")
                failed_count += 1

        logging.info(f"Gang invitation view restoration completed: {restored_count} restored, {failed_count} failed")

        if failed_count > 0:
            logging.warning(f"Some gang invitation views failed to restore. Consider cleaning up invalid invitations.")

    except Exception as e:
        logging.error(f"Critical error in restore_gang_invitation_views: {e}")
        import traceback
        traceback.print_exc()

async def cleanup_expired_gang_invitations():
    """Clean up expired or invalid gang invitations"""
    try:
        current_time = datetime.now()
        expired_invitations = []

        for invitation_id, invitation_data in gang_invitations.items():
            try:
                # Check if invitation is older than 7 days
                created_at = datetime.fromisoformat(invitation_data.get("created_at", ""))
                age = current_time - created_at

                if age.days > 7:
                    expired_invitations.append(invitation_id)
                    logging.info(f"Marking invitation {invitation_id} for cleanup (age: {age.days} days)")

            except (ValueError, TypeError) as e:
                # Invalid date format or missing created_at
                expired_invitations.append(invitation_id)
                logging.warning(f"Marking invitation {invitation_id} for cleanup due to invalid date: {e}")

        # Remove expired invitations
        for invitation_id in expired_invitations:
            del gang_invitations[invitation_id]
            logging.info(f"Cleaned up expired invitation: {invitation_id}")

        if expired_invitations:
            await save_data()
            logging.info(f"Cleaned up {len(expired_invitations)} expired gang invitations")
        else:
            logging.info("No expired gang invitations found")

    except Exception as e:
        logging.error(f"Error cleaning up expired gang invitations: {e}")

async def force_restore_gang_invitation_views():
    """Force restore gang invitation views - useful for debugging"""
    try:
        logging.info("Force restoring gang invitation views...")

        # Clear existing views first
        for view in bot.persistent_views:
            if isinstance(view, GangInvitationView):
                bot.remove_view(view)

        # Restore all views
        await restore_gang_invitation_views()
        logging.info("Force restore completed")

    except Exception as e:
        logging.error(f"Error in force restore: {e}")

@tree.command(name="diagnose_roles", description="Diagnose role configuration issues (Admin only)")
@app_commands.default_permissions(administrator=True)
async def diagnose_roles(interaction: discord.Interaction):
    """Diagnose role configuration and permission issues"""
    if not log_permission_check(interaction, "diagnose_roles"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        log_command_execution(interaction, "diagnose_roles", False)
        return

    log_command_execution(interaction, "diagnose_roles")

    try:
        await interaction.response.defer(ephemeral=True)

        guild = interaction.guild
        bot_member = guild.me

        # Create diagnostic embed
        embed = discord.Embed(
            title="🔍 Role Diagnostic Report",
            description="Analyzing role configuration and permissions...",
            color=0x3498db
        )

        # Check bot permissions
        perms = bot_member.guild_permissions
        embed.add_field(
            name="🤖 Bot Permissions",
            value=f"**Manage Roles:** {'✅' if perms.manage_roles else '❌'}\n"
                  f"**Administrator:** {'✅' if perms.administrator else '❌'}\n"
                  f"**Bot's Highest Role:** {bot_member.top_role.name} (Position: {bot_member.top_role.position})",
            inline=False
        )

        # Check configured roles
        issues = []

        # Check join role
        if join_role_id:
            join_role = safe_get_role(guild, join_role_id)
            if join_role:
                can_manage = await validate_role_permissions(guild, join_role)
                embed.add_field(
                    name="🚪 Auto-Join Role",
                    value=f"**Role:** {join_role.name}\n**Can Manage:** {'✅' if can_manage else '❌'}",
                    inline=True
                )
                if not can_manage:
                    issues.append(f"Cannot manage auto-join role '{join_role.name}' (position: {join_role.position})")
            else:
                embed.add_field(
                    name="🚪 Auto-Join Role",
                    value=f"**Role ID:** {join_role_id}\n**Status:** ❌ Not Found",
                    inline=True
                )
                issues.append(f"Auto-join role with ID {join_role_id} not found")

        # Check gang roles
        gang_role_issues = 0
        for gang_name, gang_data in gang_roles.items():
            leader_role_id = gang_data.get("leader_role")
            if leader_role_id:
                leader_role = safe_get_role(guild, leader_role_id)
                if not leader_role:
                    gang_role_issues += 1
                    issues.append(f"Gang '{gang_name}' leader role (ID: {leader_role_id}) not found")
                elif not await validate_role_permissions(guild, leader_role):
                    gang_role_issues += 1
                    issues.append(f"Cannot manage gang '{gang_name}' leader role '{leader_role.name}'")

        embed.add_field(
            name="👥 Gang Roles",
            value=f"**Total Gangs:** {len(gang_roles)}\n**Role Issues:** {gang_role_issues}",
            inline=True
        )

        # Check reaction roles
        reaction_role_issues = 0
        emoji_issues = 0
        if reaction_roles and "roles" in reaction_roles:
            for emoji, role_id in reaction_roles["roles"].items():
                # Check emoji validity with strict validation
                is_valid, validation_msg = validate_emoji(emoji, guild, strict=True)
                if not is_valid:
                    emoji_issues += 1
                    issues.append(f"Invalid emoji {emoji}: {validation_msg}")

                # Check role validity
                role = safe_get_role(guild, role_id)
                if not role:
                    reaction_role_issues += 1
                    issues.append(f"Reaction role for {emoji} (ID: {role_id}) not found")
                elif not await validate_role_permissions(guild, role):
                    reaction_role_issues += 1
                    issues.append(f"Cannot manage reaction role '{role.name}' for {emoji}")

        embed.add_field(
            name="⚡ Reaction Roles",
            value=f"**Total Roles:** {len(reaction_roles.get('roles', {}))}\n**Role Issues:** {reaction_role_issues}\n**Emoji Issues:** {emoji_issues}",
            inline=True
        )

        # Summary
        if issues:
            embed.add_field(
                name="⚠️ Issues Found",
                value="\n".join(f"• {issue}" for issue in issues[:10]) +
                      (f"\n• ... and {len(issues) - 10} more" if len(issues) > 10 else ""),
                inline=False
            )
            embed.color = 0xe74c3c  # Red for issues
        else:
            embed.add_field(
                name="✅ All Good!",
                value="No role configuration issues detected.",
                inline=False
            )
            embed.color = 0x2ecc71  # Green for success

        # Add recommendations
        recommendations = []
        if not perms.manage_roles:
            recommendations.append("Grant the bot 'Manage Roles' permission")
        if bot_member.top_role.position < 5:
            recommendations.append("Move the bot's role higher in the role hierarchy")

        if recommendations:
            embed.add_field(
                name="💡 Recommendations",
                value="\n".join(f"• {rec}" for rec in recommendations),
                inline=False
            )

        await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        log_error_to_console(e, "role diagnosis")
        await interaction.followup.send("❌ An error occurred while diagnosing roles.", ephemeral=True)



async def send_targeted_member_notification(gang_name, notification_type, interaction, target_member=None, **kwargs):
    """Send targeted notifications for member management actions"""
    if gang_name not in gang_roles:
        return

    gang_data = gang_roles[gang_name]
    actor_id = interaction.user.id

    # Create different embeds for different recipients
    if notification_type == "member_added":
        # Notification for the person performing the action (confirmation)
        actor_embed = await create_gang_notification_embed(
            title="Member Addition Confirmed",
            description=f"You have successfully added **{target_member.display_name}** to gang **{gang_name}**.",
            gang_name=gang_name,
            notification_type="action_confirmation",
            action="Member Addition",
            target=target_member.display_name,
            current_count=gang_data['current_members'],
            member_limit=gang_data['member_limit']
        )

        # Notification for the person being added
        target_embed = await create_gang_notification_embed(
            title="Welcome to the Gang",
            description=f"You have been added to gang **{gang_name}**! Welcome to the team.",
            gang_name=gang_name,
            notification_type="membership_change",
            action="Added to Gang",
            added_by=interaction.user.display_name,
            current_count=gang_data['current_members'],
            member_limit=gang_data['member_limit']
        )

        # Note: No longer sending confirmation DM to the person performing the action
        # This is handled by the command response embed instead

        # Send welcome notification to the new member
        try:
            await target_member.send(embed=target_embed)
        except Exception as e:
            logging.warning(f"Could not notify new member {target_member.id}: {e}")

        # Notify leader only if they are not the person performing the action
        leader_id = gang_data["leader"]
        if leader_id != actor_id:
            leader = interaction.guild.get_member(leader_id)
            if leader:
                leader_embed = await create_gang_notification_embed(
                    title="Gang Member Added",
                    description=f"**{target_member.display_name}** has been added to your gang **{gang_name}** by **{interaction.user.display_name}**.",
                    gang_name=gang_name,
                    notification_type="leadership_notification",
                    action="Member Addition",
                    target=target_member.display_name,
                    performed_by=interaction.user.display_name,
                    current_count=gang_data['current_members'],
                    member_limit=gang_data['member_limit']
                )
                try:
                    await leader.send(embed=leader_embed)
                except Exception as e:
                    logging.warning(f"Could not notify gang leader {leader_id}: {e}")

    elif notification_type == "member_removed":
        # Notification for the person performing the action (confirmation)
        actor_embed = await create_gang_notification_embed(
            title="Member Removal Confirmed",
            description=f"You have successfully removed **{target_member.display_name}** from gang **{gang_name}**.",
            gang_name=gang_name,
            notification_type="action_confirmation",
            action="Member Removal",
            target=target_member.display_name,
            current_count=gang_data['current_members'],
            member_limit=gang_data['member_limit']
        )

        # Notification for the person being removed
        target_embed = await create_gang_notification_embed(
            title="Gang Membership Ended",
            description=f"You have been removed from gang **{gang_name}**.",
            gang_name=gang_name,
            notification_type="membership_change",
            action="Removed from Gang",
            removed_by=interaction.user.display_name,
            current_count=gang_data['current_members'],
            member_limit=gang_data['member_limit']
        )

        # Note: No longer sending confirmation DM to the person performing the action
        # This is handled by the command response embed instead

        # Send removal notification to the former member
        try:
            await target_member.send(embed=target_embed)
        except Exception as e:
            logging.warning(f"Could not notify removed member {target_member.id}: {e}")

        # Notify leader only if they are not the person performing the action
        leader_id = gang_data["leader"]
        if leader_id != actor_id:
            leader = interaction.guild.get_member(leader_id)
            if leader:
                leader_embed = await create_gang_notification_embed(
                    title="Gang Member Removed",
                    description=f"**{target_member.display_name}** has been removed from your gang **{gang_name}** by **{interaction.user.display_name}**.",
                    gang_name=gang_name,
                    notification_type="leadership_notification",
                    action="Member Removal",
                    target=target_member.display_name,
                    performed_by=interaction.user.display_name,
                    current_count=gang_data['current_members'],
                    member_limit=gang_data['member_limit']
                )
                try:
                    await leader.send(embed=leader_embed)
                except Exception as e:
                    logging.warning(f"Could not notify gang leader {leader_id}: {e}")

async def notify_gang_members_with_embed(gang_name, embed, interaction):
    """Send professional embed notifications to all gang members (for non-member management actions)"""
    if gang_name not in gang_roles:
        return

    # Notify leader
    leader_id = gang_roles[gang_name]["leader"]
    leader = interaction.guild.get_member(leader_id)
    if leader:
        try:
            await leader.send(embed=embed)
        except Exception as e:
            logging.warning(f"Could not notify gang leader {leader.id}: {e}")

    # Notify members
    for member_id in gang_roles[gang_name]["members"]:
        if isinstance(member_id, int):
            member = interaction.guild.get_member(member_id)
            if member:
                try:
                    await member.send(embed=embed)
                except Exception as e:
                    logging.warning(f"Could not notify gang member {member.id}: {e}")

async def notify_gang_changes(gang_name, changes, interaction):
    """Notify gang members about changes using professional embeds"""
    embed = await create_gang_notification_embed(
        title="Gang Settings Updated",
        description=f"Your gang **{gang_name}** has been updated by server administration.",
        gang_name=gang_name,
        notification_type="settings_changed",
        changes=changes
    )
    await notify_gang_members_with_embed(gang_name, embed, interaction)

def verify_member_count(gang_name):
    """Verify and correct member count"""
    actual_members = len([m for m in gang_roles[gang_name]["members"] if isinstance(m, int)])
    gang_roles[gang_name]["current_members"] = actual_members
    return actual_members

def check_bot_permissions(guild):
    """Check if bot has required permissions"""
    bot_member = guild.get_member(bot.user.id)
    required_permissions = ["manage_roles", "send_messages", "embed_links"]
    return [perm for perm in required_permissions if not getattr(bot_member.guild_permissions, perm)]

@tree.command(name="issue_strike", description="Issue a strike to a gang")
@app_commands.default_permissions(administrator=True)
async def issue_strike(interaction: discord.Interaction, gang_name: str, reason: str):
    try:
        # Defer the response immediately to prevent timeout
        await interaction.response.defer()

        if gang_name not in gang_roles:
            # Create professional error embed for gang not found
            error_embed = discord.Embed(
                title="❌ Gang Not Found",
                description=f"Gang **{gang_name}** does not exist in the system.",
                color=0x000000  # Professional black theme
            )

            error_embed.add_field(
                name="**Error Details**",
                value=f"```Requested Gang: {gang_name}\nStatus: Not Found\nAction: Strike issuance failed```",
                inline=False
            )

            error_embed.add_field(
                name="**💡 Suggestion**",
                value="Use `/check_gang_list` to view all existing gangs in the system.",
                inline=False
            )

            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
            error_embed.set_footer(text=f"Gang Management System • {current_time}")

            await interaction.followup.send(embed=error_embed)
            return

        # Initialize strikes for the gang if not exists
        if gang_name not in gang_strikes:
            gang_strikes[gang_name] = 0

        # Add the strike
        gang_strikes[gang_name] += 1
        await save_data()

        # Create professional strike notification embed
        embed = await create_gang_notification_embed(
            title="Strike Issued",
            description=f"Your gang **{gang_name}** has received a disciplinary strike.",
            gang_name=gang_name,
            notification_type="strike_added",
            current_strikes=gang_strikes[gang_name],
            reason=reason
        )

        # Send professional embed notifications to all gang members
        await notify_gang_members_with_embed(gang_name, embed, interaction)

        # Create professional response embed for the administrator
        response_embed = discord.Embed(
            title="⚠️ Strike Issued Successfully",
            description=f"Disciplinary strike issued to gang **{gang_name}**.",
            color=0x000000  # Professional black theme
        )

        response_embed.add_field(
            name="**Strike Details**",
            value=f"```Gang: {gang_name}\nCurrent Strikes: {gang_strikes[gang_name]}\nReason: {reason}```",
            inline=False
        )

        response_embed.add_field(
            name="**⚠️ Notification Status**",
            value="All gang members have been notified of this disciplinary action.",
            inline=False
        )

        current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
        response_embed.set_footer(text=f"Gang Management System • {current_time}")

        await interaction.followup.send(embed=response_embed)

    except Exception as e:
        print(f"Error in issue_strike: {e}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
        except:
            pass

# Updated edit_gang command
@tree.command(name="edit_gang", description="Edit gang settings")
@app_commands.default_permissions(administrator=True)
async def edit_gang(interaction: discord.Interaction, gang_name: str, new_member_limit: int = None, new_leader: discord.Member = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        await interaction.response.defer()

        # Initial validations
        if gang_name not in gang_roles:
            await interaction.followup.send("Gang not found.", ephemeral=True)
            return

        # Check bot permissions
        missing_permissions = check_bot_permissions(interaction.guild)
        if missing_permissions:
            await interaction.followup.send(
                f"Bot is missing required permissions: {', '.join(missing_permissions)}",
                ephemeral=True
            )
            return

        # Create backup before making changes
        await create_backup()

        # Store original data for rollback
        original_data = {
            "gang_roles": gang_roles.copy(),
            "gang_leaders": gang_leaders.copy()
        }

        try:
            changes = []
            if new_member_limit is not None:
                if new_member_limit <= 0:
                    await interaction.followup.send("Member limit must be greater than 0", ephemeral=True)
                    return

                if new_member_limit < gang_roles[gang_name]["current_members"]:
                    await interaction.followup.send(
                        "New member limit cannot be less than current member count.",
                        ephemeral=True
                    )
                    return

                old_limit = gang_roles[gang_name]["member_limit"]
                gang_roles[gang_name]["member_limit"] = new_member_limit
                changes.append(f"Member limit changed from {old_limit} to {new_member_limit}")

            if new_leader:
                # Check if new leader is already leading another gang
                for other_gang, details in gang_roles.items():
                    if details["leader"] == new_leader.id and other_gang != gang_name:
                        await interaction.followup.send(
                            "This user is already a leader of another gang.",
                            ephemeral=True
                        )
                        return

                old_leader_id = gang_roles[gang_name]["leader"]
                old_leader = interaction.guild.get_member(old_leader_id)

                # Remove new leader from members list if they're there
                if new_leader.id in gang_roles[gang_name]["members"]:
                    gang_roles[gang_name]["members"].remove(new_leader.id)
                    gang_roles[gang_name]["current_members"] -= 1

                # Update gang data
                gang_roles[gang_name]["leader"] = new_leader.id
                gang_leaders[new_leader.id] = gang_roles[gang_name]["leader_role"]

                # Remove old leader's data
                if old_leader_id in gang_leaders:
                    del gang_leaders[old_leader_id]

                # Update leader roles
                leader_role_id = gang_roles[gang_name]["leader_role"]
                leader_role = interaction.guild.get_role(leader_role_id)

                if leader_role:
                    # Handle old leader
                    if old_leader:
                        await old_leader.remove_roles(leader_role)
                        # Convert old leader to member if there's a member role
                        member_role_id = next((role_id for role_id in gang_roles[gang_name]["members"]
                                             if isinstance(role_id, int)), None)
                        if member_role_id:
                            member_role = interaction.guild.get_role(member_role_id)
                            if member_role:
                                await old_leader.add_roles(member_role)
                                gang_roles[gang_name]["members"].append(old_leader_id)
                                gang_roles[gang_name]["current_members"] += 1

                    # Assign new leader role
                    await new_leader.add_roles(leader_role)

                changes.append(f"Leader changed from {old_leader.mention if old_leader else 'Unknown'} to {new_leader.mention}")

            # Verify member count
            verify_member_count(gang_name)

            await save_data()

            # Create response embed
            embed = discord.Embed(
                title="Gang Settings Updated",
                description=f"Gang: {gang_name}",
                color=discord.Color.green()
            )

            for change in changes:
                embed.add_field(name="Change", value=change, inline=False)

            # Notify members about changes
            await notify_gang_changes(gang_name, changes, interaction)

            await interaction.followup.send(embed=embed)
            logging.info(f"Gang {gang_name} edited by {interaction.user}: {', '.join(changes)}")

        except Exception as e:
            # Rollback changes on error
            gang_roles.update(original_data["gang_roles"])
            gang_leaders.update(original_data["gang_leaders"])
            raise e

    except discord.Forbidden:
        logging.error(f"Permission error in edit_gang: {interaction.guild.id}")
        await interaction.followup.send("I don't have permission to manage roles.", ephemeral=True)
    except discord.HTTPException as e:
        logging.error(f"Discord API error in edit_gang: {e}")
        await interaction.followup.send(f"Discord API error: {str(e)}", ephemeral=True)
    except Exception as e:
        logging.error(f"Error editing gang: {e}")
        await interaction.followup.send("An error occurred while editing the gang.", ephemeral=True)

# Application System
@tree.command(name="set_application_channel", description="Set the application channel")
@app_commands.default_permissions(administrator=True)
async def set_application_channel(interaction: discord.Interaction, channel: discord.TextChannel):
    global application_channel

    # Set the application channel
    application_channel = channel.id
    logging.info(f"Application channel set to {channel.id} ({channel.name}) by {interaction.user}")

    # Save data to MongoDB
    try:
        await save_data_optimized()
        logging.info(f"Successfully saved application channel {channel.id} to MongoDB")
    except Exception as e:
        logging.error(f"Failed to save application channel to MongoDB: {e}")
        await interaction.response.send_message(
            f"❌ Error saving configuration: {e}\nPlease try again or contact an administrator.",
            ephemeral=True
        )
        return

    # Send success response
    await interaction.response.send_message(f"✅ Application channel set to {channel.mention}")

    # Create the application panel in the new channel
    try:
        await send_application_embed(channel)
        logging.info(f"Successfully created application panel in channel {channel.id}")
    except Exception as e:
        logging.error(f"Failed to create application panel in channel {channel.id}: {e}")
        await interaction.followup.send(
            f"⚠️ Channel set successfully, but failed to create application panel: {e}",
            ephemeral=True
        )

@tree.command(name="set_application_log_channel", description="Set the application log channel and optionally the response notification channel")
@app_commands.default_permissions(administrator=True)
async def set_application_log_channel(interaction: discord.Interaction, log_channel: discord.TextChannel, response_channel: discord.TextChannel = None):
    global application_log_channel, application_response_channel

    # Check permissions
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You don't have permission to use this command.", ephemeral=True)
        return

    # Set the application log channel
    application_log_channel = log_channel.id
    logging.info(f"Application log channel set to {log_channel.id} ({log_channel.name}) by {interaction.user}")

    # Set the application response channel if provided
    if response_channel:
        application_response_channel = response_channel.id
        logging.info(f"Application response channel set to {response_channel.id} ({response_channel.name}) by {interaction.user}")

    # Save data to MongoDB
    try:
        await save_data_optimized()
        logging.info(f"Successfully saved application channels to MongoDB")
    except Exception as e:
        logging.error(f"Failed to save application channels to MongoDB: {e}")
        await interaction.response.send_message(
            f"❌ Error saving configuration: {e}\nPlease try again or contact an administrator.",
            ephemeral=True
        )
        return

    # Create detailed confirmation embed
    embed = discord.Embed(
        title="✅ Application Channel Configuration Updated",
        color=0x2ECC71
    )

    embed.add_field(
        name="📝 Application Log Channel",
        value=f"{log_channel.mention}\n*Applications will be logged here for staff review*",
        inline=False
    )

    if response_channel:
        embed.add_field(
            name="📬 Application Response Channel",
            value=f"{response_channel.mention}\n*Response notifications will be sent here when applications are processed*",
            inline=False
        )
    else:
        embed.add_field(
            name="📬 Application Response Channel",
            value="❌ Not configured\n*Use `/set_application_response_channel` to configure response notifications*",
            inline=False
        )

    embed.set_footer(text=f"Configured by {interaction.user.display_name}")
    embed.timestamp = datetime.now()

    await interaction.response.send_message(embed=embed, ephemeral=True)










@tree.command(name="create_application", description="Create a new application form")
@app_commands.default_permissions(administrator=True)
async def create_application(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    class ApplicationModal(Modal):
        def __init__(self):
            super().__init__(title="Create Application")

            self.add_item(TextInput(label="Application Name", placeholder="Enter the application name"))
            self.add_item(TextInput(label="Questions", placeholder="Enter questions separated by semicolons", style=discord.TextStyle.paragraph))

        async def on_submit(self, interaction: discord.Interaction):
            name = self.children[0].value
            questions = self.children[1].value.split(';')
            application_forms[name] = [q.strip() for q in questions if q.strip()]
            await save_data_optimized()
            await interaction.response.send_message(f"Application '{name}' created with {len(application_forms[name])} questions.")

    await interaction.response.send_modal(ApplicationModal())

@tree.command(name="list_applications", description="List all applications with questions")
@app_commands.default_permissions(administrator=True)
async def list_applications(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if not application_forms:
        await interaction.response.send_message("No applications found.")
        return

    embed = discord.Embed(title="Application List", color=discord.Color.blue())
    for name, questions in application_forms.items():
        embed.add_field(name=name, value="\n".join(questions), inline=False)

    await interaction.response.send_message(embed=embed)

@tree.command(name="edit_application", description="Edit an existing application form")
@app_commands.default_permissions(administrator=True)
async def edit_application(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    log_permission_check(interaction, "edit_application")
    if name not in application_forms:
        await interaction.response.send_message(f"Application '{name}' not found.")
        return

    modal = EditApplicationModal(name)
    await interaction.response.send_modal(modal)

class EditApplicationModal(discord.ui.Modal):
    def __init__(self, name):
        super().__init__(title="Edit Application")
        self.old_name = name
        self.add_item(discord.ui.TextInput(label="Application Name", default=name))
        self.add_item(discord.ui.TextInput(label="Questions", default="; ".join(application_forms[name]), style=discord.TextStyle.paragraph))

    async def on_submit(self, interaction: discord.Interaction):
        new_name = self.children[0].value
        questions = self.children[1].value.split(';')
        if new_name != self.old_name:
            application_forms[new_name] = application_forms.pop(self.old_name)
        application_forms[new_name] = [q.strip() for q in questions if q.strip()]
        await save_data()
        await interaction.response.send_message(f"Application '{new_name}' updated with {len(application_forms[new_name])} questions.")


@tree.command(name="remove_application", description="Remove an existing application form")
@app_commands.default_permissions(administrator=True)
async def remove_application(interaction: discord.Interaction, name: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if name in application_forms:
        del application_forms[name]
        await save_data()  # Save data after setting the welcome channel
        await interaction.response.send_message(f"Application '{name}' has been removed.")
    else:
        await interaction.response.send_message(f"Application '{name}' does not exist.")



async def send_application_embed(channel):
    # Create options with professional icons for each application form
    options = [
        discord.SelectOption(
            label=name,
            value=name,
            emoji="📋",
            description=f"Apply for {name} position"
        ) for name in application_forms.keys()
    ]

    # Create a dropdown with a professional placeholder
    select = Select(placeholder="Select an application type", options=options)

    async def select_callback(interaction):
        selected_application = select.values[0]

        # Create a professional-looking embed for application confirmation
        embed = discord.Embed(
            title="Application Confirmation",
            description=f"You've selected the **{selected_application}** application.\n\nBefore proceeding, please note:\n• This application will take approximately 5-10 minutes to complete\n• You have 60 minutes to submit your responses\n• All information provided will be reviewed by our staff team",
            color=0x2b2d31  # Professional dark theme color
        )

        # Add a clean divider
        embed.add_field(name="", value="━━━━━━━━━━━━━━━━━━━━━━━", inline=False)

        # Add application instructions
        embed.add_field(
            name="Instructions",
            value="• Answer all questions honestly and thoroughly\n• Provide specific examples when possible\n• Review your answers before submitting",
            inline=False
        )

        # Add professional footer and thumbnail
        if interaction.guild.icon:
            embed.set_thumbnail(url=interaction.guild.icon.url)

        embed.set_footer(text=f"© {datetime.now().year} Application System • Confidential")

        # Create professional-looking buttons
        view = View()
        confirm_button = Button(label="Begin Application", style=discord.ButtonStyle.primary)
        cancel_button = Button(label="Cancel", style=discord.ButtonStyle.secondary)

        async def confirm_callback(interaction):
            user_id = str(interaction.user.id)
            logging.debug(f"Begin Application clicked by user {user_id}")

            # Check if user has a pending modal (DMs failed)
            if user_id in modal_application_state and "pending_modal" in modal_application_state[user_id]:
                logging.debug(f"User {user_id} has pending modal, sending first modal")
                # DMs failed, use modal fallback
                modal = modal_application_state[user_id]["pending_modal"]
                del modal_application_state[user_id]["pending_modal"]  # Clean up

                # Use safe modal sending with comprehensive error handling
                modal_sent = await safe_send_modal(interaction, modal, user_id, "application start")
                logging.debug(f"Modal sent result for user {user_id}: {modal_sent}")

                if not modal_sent:
                    # Modal sending failed, clean up and send error message
                    logging.error(f"Modal sending failed for user {user_id}")
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            "❌ Unable to start modal application. Please try again or contact an administrator.",
                            ephemeral=True
                        )
                    if user_id in modal_application_state:
                        del modal_application_state[user_id]
            else:
                # Normal DM flow
                logging.debug(f"User {user_id} using normal DM flow")
                await interaction.response.send_message("Your application has been initiated. Please check your DMs to complete the process.")
                await start_application(interaction.user, selected_application)

        async def cancel_callback(interaction):
            # Clean up any modal application state for this user
            user_id = str(interaction.user.id)
            if user_id in modal_application_state:
                del modal_application_state[user_id]
                logging.info(f"Cleaned up modal application state for user {user_id} due to cancellation")

            await interaction.response.send_message("Application process canceled.", ephemeral=True)

        confirm_button.callback = confirm_callback
        cancel_button.callback = cancel_callback
        view.add_item(confirm_button)
        view.add_item(cancel_button)

        # Try to send DM with confirmation embed
        try:
            await interaction.user.send(embed=embed, view=view)
            # If successful, send response to original interaction
            await interaction.response.send_message(
                "✅ **Application Confirmation Sent**\n\n"
                "Please check your direct messages to confirm and begin your application process.",
                ephemeral=True
            )
        except discord.errors.Forbidden:
            # DMs are disabled, use modal fallback system
            logging.info(f"DMs disabled for user {interaction.user.id}, preparing modal fallback for application {selected_application}")

            # Initialize modal application state for this user
            user_id = str(interaction.user.id)
            questions = application_forms.get(selected_application, [])

            if not questions:
                await interaction.response.send_message(
                    "❌ Application form not found. Please contact an administrator.",
                    ephemeral=True
                )
                return

            # Set up modal application state
            modal_application_state[user_id] = {
                "application_name": selected_application,
                "questions": questions,
                "answers": [],
                "current_question_index": 0,
                "total_questions": len(questions),
                "created_at": datetime.now()
            }

            # Create the first modal
            questions_in_first_modal = min(5, len(questions))
            modal = ApplicationModal(
                user_id=user_id,
                questions=questions[:questions_in_first_modal],
                start_index=0,
                total_questions=len(questions),
                application_name=selected_application
            )

            # Store the modal for the confirm button to use
            modal_application_state[user_id]["pending_modal"] = modal

            # Send fallback response with confirmation buttons
            fallback_embed = discord.Embed(
                title="Application Confirmation (Modal Mode)",
                description=f"You've selected the **{selected_application}** application.\n\n"
                           f"Since direct messages are disabled, we'll use interactive forms to collect your responses.\n\n"
                           f"**Application Details:**\n"
                           f"• Total questions: **{len(questions)}**\n"
                           f"• Estimated time: **5-10 minutes**\n"
                           f"• Timeout: **60 minutes**\n\n"
                           f"Please confirm below to begin the application process.",
                color=0x2b2d31  # Professional dark theme color
            )
            fallback_embed.add_field(
                name="📋 Modal Application Process",
                value="• Questions will be presented in interactive forms\n"
                      "• Each form contains up to 5 questions\n"
                      "• Complete all forms to submit your application\n"
                      "• Your progress will be saved between forms",
                inline=False
            )
            fallback_embed.set_footer(text=f"© {datetime.now().year} Application System • Modal Mode")
            fallback_embed.timestamp = datetime.now()

            # Create new view with the same buttons (they'll handle modal mode)
            fallback_view = View()
            fallback_view.add_item(confirm_button)
            fallback_view.add_item(cancel_button)

            await interaction.response.send_message(embed=fallback_embed, view=fallback_view, ephemeral=True)

        except Exception as e:
            # Handle any other errors
            logging.error(f"Unexpected error sending application confirmation to user {interaction.user.id}: {e}")
            await interaction.response.send_message(
                "❌ An unexpected error occurred while starting your application. Please try again or contact an administrator.",
                ephemeral=True
            )

    select.callback = select_callback

    # Create a professional-looking main embed for the application portal
    embed = discord.Embed(
        title="Application Portal",
        description="Welcome to our official application system. Select an application type below to begin the process.",
        color=0x2b2d31  # Professional dark theme color
    )

    # Add information about the application process
    embed.add_field(
        name="Application Process",
        value="1. Select an application type below\n2. Complete the application form\n3. Submit your responses\n4. Wait for staff review",
        inline=False
    )

    # Add a note about privacy
    embed.add_field(
        name="Privacy Notice",
        value="All information provided will be kept confidential and reviewed only by authorized staff members.",
        inline=False
    )

    # Add professional footer and timestamp
    embed.set_footer(text=f"© {datetime.now().year} Application System")
    embed.timestamp = datetime.now()

    # Add guild icon if available
    if channel.guild.icon:
        embed.set_thumbnail(url=channel.guild.icon.url)

    # Create a view with the dropdown
    view = View()
    view.add_item(select)

    # Send the embed with the dropdown menu
    await channel.send(embed=embed, view=view)

async def recreate_application_panel():
    """
    Recreate the application panel with enhanced error handling and validation.
    This function ensures the application panel is properly restored after bot restarts.
    """
    try:
        logging.info("Starting application panel recreation...")
        logging.debug(f"Current application_channel value: {application_channel} (type: {type(application_channel)})")
        logging.debug(f"Current application_forms count: {len(application_forms) if application_forms else 0}")

        # Validate that we have application forms configured
        if not application_forms:
            logging.warning("No application forms configured - skipping panel recreation")
            logging.debug("application_forms is empty or None")
            return

        # Validate that application channel is configured
        if not application_channel:
            logging.warning("No application channel configured - skipping panel recreation")
            logging.debug(f"application_channel is falsy: {repr(application_channel)}")
            return

        # Get the channel object
        logging.info(f"Attempting to get channel object for ID: {application_channel}")
        channel = bot.get_channel(application_channel)
        if not channel:
            logging.error(f"Application channel {application_channel} not found - cannot recreate panel")
            return

        # Check bot permissions in the channel
        permissions = channel.permissions_for(channel.guild.me)
        if not permissions.send_messages:
            logging.error(f"Bot lacks send_messages permission in application channel {channel.name}")
            return
        if not permissions.manage_messages:
            logging.warning(f"Bot lacks manage_messages permission in application channel {channel.name} - cannot clean old messages")

        # Clean up existing bot messages if we have permission
        if permissions.manage_messages:
            try:
                await cleanup_bot_messages_with_rate_limiting(channel)
            except Exception as e:
                logging.warning(f"Error during message cleanup: {e}")
            except Exception as e:
                logging.warning(f"Error during message cleanup: {e}")

        # Create new application panel
        await send_application_embed(channel)
        logging.info(f"Successfully recreated application panel in channel {channel.name} with {len(application_forms)} application forms")

    except Exception as e:
        logging.error(f"Error in recreate_application_panel: {e}")
        import traceback
        traceback.print_exc()

async def cleanup_bot_messages_with_rate_limiting(channel):
    """
    Clean up bot messages with proper rate limiting and error handling.
    Implements exponential backoff for Discord API rate limits.
    """
    import asyncio

    try:
        logging.info(f"Starting rate-limited message cleanup in channel {channel.name}")

        # Collect bot messages first
        bot_messages = []
        async for message in channel.history(limit=100):
            if message.author == bot.user:
                bot_messages.append(message)

        if not bot_messages:
            logging.info("No bot messages found to clean up")
            return

        logging.info(f"Found {len(bot_messages)} bot messages to clean up")

        # Try bulk delete for messages newer than 14 days
        now = datetime.now(timezone.utc)
        bulk_deletable = []
        individual_delete = []

        for message in bot_messages:
            # Discord bulk delete only works for messages less than 14 days old
            if (now - message.created_at).days < 14:
                bulk_deletable.append(message)
            else:
                individual_delete.append(message)

        deleted_count = 0

        # Bulk delete newer messages (more efficient)
        if bulk_deletable and len(bulk_deletable) > 1:
            try:
                # Discord allows bulk delete of 2-100 messages at once
                chunk_size = min(100, len(bulk_deletable))
                await channel.delete_messages(bulk_deletable[:chunk_size])
                deleted_count += chunk_size
                logging.info(f"Bulk deleted {chunk_size} messages")

                # If there are more messages, handle them individually with rate limiting
                if len(bulk_deletable) > chunk_size:
                    individual_delete.extend(bulk_deletable[chunk_size:])

            except discord.HTTPException as e:
                logging.warning(f"Bulk delete failed: {e}, falling back to individual deletion")
                individual_delete.extend(bulk_deletable)
        elif len(bulk_deletable) == 1:
            individual_delete.extend(bulk_deletable)

        # Delete remaining messages individually with rate limiting
        if individual_delete:
            retry_delay = 1  # Start with 1 second delay
            max_retries = 3

            for i, message in enumerate(individual_delete):
                for attempt in range(max_retries):
                    try:
                        await message.delete()
                        deleted_count += 1

                        # Rate limiting: wait between deletions
                        if i < len(individual_delete) - 1:  # Don't wait after the last message
                            await asyncio.sleep(0.5)  # 500ms between deletions

                        break  # Success, move to next message

                    except discord.HTTPException as e:
                        if e.status == 429:  # Rate limited
                            retry_after = getattr(e, 'retry_after', retry_delay)
                            logging.warning(f"Rate limited, waiting {retry_after} seconds before retry {attempt + 1}")
                            await asyncio.sleep(retry_after)
                            retry_delay = min(retry_delay * 2, 60)  # Exponential backoff, max 60 seconds
                        elif e.status == 404:  # Message not found (already deleted)
                            logging.debug(f"Message {message.id} already deleted")
                            break
                        elif e.status == 403:  # Forbidden
                            logging.warning(f"Cannot delete message {message.id} - insufficient permissions")
                            break
                        else:
                            logging.warning(f"HTTP error deleting message {message.id}: {e}")
                            if attempt == max_retries - 1:
                                break
                            await asyncio.sleep(retry_delay)
                    except discord.NotFound:
                        logging.debug(f"Message {message.id} not found (already deleted)")
                        break
                    except Exception as e:
                        logging.warning(f"Unexpected error deleting message {message.id}: {e}")
                        break

        logging.info(f"Message cleanup completed - deleted {deleted_count} messages")

    except Exception as e:
        logging.error(f"Error in cleanup_bot_messages_with_rate_limiting: {e}")
        import traceback
        traceback.print_exc()

async def verify_data_integrity():
    """
    Verify the integrity of all bot data and fix common issues.
    This function ensures data consistency after bot restarts.
    """
    try:
        logging.info("Starting data integrity verification...")

        # Verify application data integrity
        issues_found = 0

        # Check application forms
        if not isinstance(application_forms, dict):
            logging.error("application_forms is not a dictionary - data corruption detected")
            issues_found += 1
        else:
            # Validate each application form
            for form_name, questions in application_forms.items():
                if not isinstance(questions, list):
                    logging.warning(f"Application form '{form_name}' has invalid questions format")
                    issues_found += 1
                elif len(questions) == 0:
                    logging.warning(f"Application form '{form_name}' has no questions")
                    issues_found += 1

        # Check application status data
        if not isinstance(applications_status, dict):
            logging.error("applications_status is not a dictionary - data corruption detected")
            issues_found += 1
        else:
            # Clean up invalid application status entries
            invalid_entries = []
            for user_id, status in applications_status.items():
                if not isinstance(status, dict):
                    invalid_entries.append(user_id)
                    continue

                # Check required fields
                required_fields = ["responded", "message_id", "application_name"]
                missing_fields = [field for field in required_fields if field not in status]
                if missing_fields:
                    logging.warning(f"Application status for user {user_id} missing fields: {missing_fields}")
                    issues_found += 1

            # Remove invalid entries
            for user_id in invalid_entries:
                del applications_status[user_id]
                logging.info(f"Removed invalid application status entry for user {user_id}")
                issues_found += 1

        # Verify channel configurations
        if application_channel and not bot.get_channel(application_channel):
            logging.warning(f"Application channel {application_channel} not accessible")
            issues_found += 1

        if application_log_channel and not bot.get_channel(application_log_channel):
            logging.warning(f"Application log channel {application_log_channel} not accessible")
            issues_found += 1

        # Verify modal application state cleanup
        if modal_application_state:
            current_time = datetime.now()
            expired_states = []
            for user_id, state in modal_application_state.items():
                if "created_at" in state:
                    time_diff = current_time - state["created_at"]
                    if time_diff.total_seconds() > 3600:  # 1 hour
                        expired_states.append(user_id)

            for user_id in expired_states:
                del modal_application_state[user_id]
                logging.info(f"Cleaned up expired modal application state for user {user_id}")

        if issues_found > 0:
            logging.warning(f"Data integrity verification completed with {issues_found} issues found and addressed")
            # Save corrected data
            await save_data_optimized()
        else:
            logging.info("Data integrity verification completed - no issues found")

        return issues_found == 0

    except Exception as e:
        logging.error(f"Error in verify_data_integrity: {e}")
        import traceback
        traceback.print_exc()
        return False

async def backup_application_data():
    """
    Create a backup of critical application data for recovery purposes.
    Enhanced with proper error handling and MongoDB integration.
    """
    try:
        logging.info("Starting application data backup...")

        # Validate and prepare data before backup
        logging.debug("Validating data structures for backup...")

        # Validate application forms
        if not isinstance(application_forms, dict):
            logging.warning("application_forms is not a dict, using empty dict for backup")
            forms_backup = {}
        else:
            forms_backup = application_forms.copy()

        # Validate application status
        if not isinstance(applications_status, dict):
            logging.warning("applications_status is not a dict, using empty dict for backup")
            status_backup = {}
        else:
            status_backup = applications_status.copy()

        # Validate gang data safely (this is where the error occurs)
        gang_backup = {}
        try:
            # Check if gang variables exist and are valid
            if 'gang_roles' in globals() and isinstance(gang_roles, dict):
                gang_backup['roles'] = gang_roles.copy()
            else:
                logging.debug("gang_roles not available or invalid, skipping from backup")
                gang_backup['roles'] = {}

            if 'gang_members' in globals() and isinstance(gang_members, dict):
                gang_backup['members'] = gang_members.copy()
            else:
                logging.debug("gang_members not available or invalid, skipping from backup")
                gang_backup['members'] = {}

            if 'gang_leaders' in globals() and isinstance(gang_leaders, dict):
                gang_backup['leaders'] = gang_leaders.copy()
            else:
                logging.debug("gang_leaders not available or invalid, skipping from backup")
                gang_backup['leaders'] = {}

            if 'gang_strikes' in globals() and isinstance(gang_strikes, dict):
                gang_backup['strikes'] = gang_strikes.copy()
            else:
                logging.debug("gang_strikes not available or invalid, skipping from backup")
                gang_backup['strikes'] = {}

            if 'gang_invitations' in globals() and isinstance(gang_invitations, dict):
                gang_backup['invitations'] = gang_invitations.copy()
            else:
                logging.debug("gang_invitations not available or invalid, skipping from backup")
                gang_backup['invitations'] = {}

            logging.debug(f"Gang data validated - roles: {len(gang_backup['roles'])}, members: {len(gang_backup['members'])}")

        except Exception as gang_error:
            logging.warning(f"Error processing gang data for backup: {gang_error}")
            gang_backup = {
                'roles': {},
                'members': {},
                'leaders': {},
                'strikes': {},
                'invitations': {}
            }

        # Handle modal application state safely
        modal_backup = {}
        if isinstance(modal_application_state, dict):
            for user_id, state in modal_application_state.items():
                try:
                    if isinstance(state, dict):
                        state_copy = state.copy()
                        # Handle datetime serialization
                        if "created_at" in state_copy:
                            created_at = state_copy["created_at"]
                            if isinstance(created_at, datetime):
                                state_copy["created_at"] = created_at.isoformat()
                            elif created_at is None:
                                state_copy["created_at"] = datetime.now().isoformat()
                        modal_backup[user_id] = state_copy
                except Exception as e:
                    logging.warning(f"Error processing modal state for user {user_id}: {e}")

        backup_data = {
            "timestamp": datetime.now().isoformat(),
            "application_forms": forms_backup,
            "applications_status": status_backup,
            "application_channel": application_channel,
            "application_log_channel": application_log_channel,
            "modal_application_state": modal_backup,
            "gang_data": gang_backup,  # Include gang data in backup
            "backup_version": "2.1"  # Updated version for gang data inclusion
        }

        logging.debug(f"Backup data prepared - forms: {len(forms_backup)}, status: {len(status_backup)}, modal: {len(modal_backup)}, gangs: {len(gang_backup.get('roles', {}))}")

        # Validate database connection before proceeding
        if not await validate_database_connection():
            logging.error("Database validation failed - cannot create backup")
            return False

        # Use the database manager for backup (already validated)
        try:
            # Get the backup collection (db_manager is already validated)
            backup_collection = db_manager.get_collection("application_backups")
            logging.debug("Application backups collection obtained successfully")

            # Insert backup with timestamp-based ID
            backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_document = {
                "_id": backup_id,
                **backup_data
            }

            result = await backup_collection.insert_one(backup_document)

            if result.inserted_id:
                logging.info(f"Application data backup created successfully with ID: {backup_id}")

                # Clean up old backups (keep last 10)
                try:
                    old_backups = await backup_collection.find({}, {"_id": 1}).sort("_id", -1).skip(10).to_list(None)
                    if old_backups:
                        old_ids = [backup["_id"] for backup in old_backups]
                        await backup_collection.delete_many({"_id": {"$in": old_ids}})
                        logging.info(f"Cleaned up {len(old_ids)} old backup(s)")
                except Exception as cleanup_error:
                    logging.warning(f"Error cleaning up old backups: {cleanup_error}")

                return True
            else:
                logging.error("Failed to insert backup document")
                return False

        except Exception as db_error:
            logging.error(f"Database error during backup: {db_error}")
            import traceback
            traceback.print_exc()
            return False

    except Exception as e:
        logging.error(f"Critical error in backup_application_data: {e}")
        import traceback
        traceback.print_exc()
        return False



async def validate_database_connection():
    """
    Validate database connection with retry logic and comprehensive checks.
    """
    import asyncio

    max_retries = 3
    retry_delay = 2  # seconds

    for attempt in range(max_retries):
        try:
            logging.debug(f"Database validation attempt {attempt + 1}/{max_retries}")

            # Use the comprehensive initialization check
            initialization_success = await ensure_database_manager_initialized()
            if initialization_success:
                return True

            # If initialization failed, log and retry
            logging.warning(f"Database manager initialization failed (attempt {attempt + 1})")
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
                continue
            return False

        except Exception as e:
            logging.warning(f"Error validating database connection (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay)
                retry_delay *= 2
                continue
            logging.error(f"Database validation failed after {max_retries} attempts")
            return False

    return False

async def restore_application_data_from_backup():
    """
    Restore application data from the most recent backup if current data is corrupted.
    """
    try:
        from database import load_data as db_load_data

        # Load backup data
        backup_data = await db_load_data()
        if not backup_data or "application_backup" not in backup_data:
            logging.warning("No application backup data found")
            return False

        backup = backup_data["application_backup"]

        # Restore global variables
        global application_forms, applications_status, application_channel, application_log_channel, application_fallback_channel, application_response_channel, modal_application_state

        application_forms = backup.get("application_forms", {})
        applications_status = backup.get("applications_status", {})
        application_channel = backup.get("application_channel")
        application_log_channel = backup.get("application_log_channel")
        application_fallback_channel = backup.get("application_fallback_channel")
        application_response_channel = backup.get("application_response_channel")

        # Restore modal application state with datetime conversion
        modal_state = backup.get("modal_application_state", {})
        modal_application_state = {}
        for user_id, state in modal_state.items():
            restored_state = state.copy()
            if "created_at" in restored_state and isinstance(restored_state["created_at"], str):
                try:
                    restored_state["created_at"] = datetime.fromisoformat(restored_state["created_at"])
                except:
                    restored_state["created_at"] = datetime.now()
            modal_application_state[user_id] = restored_state

        logging.info(f"Application data restored from backup (timestamp: {backup.get('timestamp', 'unknown')})")
        return True

    except Exception as e:
        logging.error(f"Error restoring application data from backup: {e}")
        return False

async def ensure_database_manager_initialized():
    """
    Ensure the database manager is properly initialized with all collections.
    This function should be called before any database operations.
    """
    try:
        logging.info("Ensuring database manager is properly initialized...")

        # Check if db_manager exists
        if db_manager is None:
            logging.error("Database manager is None - not imported correctly")
            return False

        # Ensure database connection
        logging.debug("Establishing database connection...")
        connection_success = await db_manager.ensure_connection()

        if not connection_success:
            logging.error("Failed to establish database connection")
            return False

        # Verify database manager state
        if not db_manager.is_connected:
            logging.error("Database manager reports not connected")
            return False

        # Verify collections are initialized
        if not hasattr(db_manager, 'collections') or not isinstance(db_manager.collections, dict):
            logging.error("Database manager collections not properly initialized")
            return False

        # Check for required collections
        required_collections = [
            "gangs", "applications", "settings", "sticky_messages",
            "tebex_settings", "reaction_roles", "transactions",
            "guild_settings", "persistent_views", "claim_verifications",
            "application_backups"
        ]

        missing_collections = []
        for collection_name in required_collections:
            if collection_name not in db_manager.collections:
                missing_collections.append(collection_name)

        if missing_collections:
            logging.error(f"Missing collections in database manager: {missing_collections}")
            logging.error(f"Available collections: {list(db_manager.collections.keys())}")
            return False

        # Test application_backups collection specifically
        try:
            backup_collection = db_manager.get_collection("application_backups")
            if backup_collection is None:
                logging.error("application_backups collection returned None from get_collection")
                return False

            # Test collection access
            await backup_collection.count_documents({})
            logging.debug("application_backups collection is accessible")

        except Exception as collection_test_error:
            logging.error(f"Error testing application_backups collection: {collection_test_error}")
            return False

        logging.info("Database manager initialization verified successfully")
        logging.debug(f"Available collections: {list(db_manager.collections.keys())}")
        return True

    except Exception as e:
        logging.error(f"Error ensuring database manager initialization: {e}")
        import traceback
        traceback.print_exc()
        return False

async def validate_startup_systems():
    """
    Validate all critical systems during startup to prevent runtime errors.
    """
    try:
        logging.info("Validating startup systems...")

        validation_results = {
            "database": False,
            "application_config": False,
            "reaction_roles": False,
            "backup_system": False
        }

        # Validate database connection
        validation_results["database"] = await validate_database_connection()

        # Validate application configuration
        if application_forms and isinstance(application_forms, dict):
            if application_channel:
                channel = bot.get_channel(application_channel)
                validation_results["application_config"] = channel is not None
            else:
                logging.info("Application channel not configured (this is optional)")
                validation_results["application_config"] = True  # Not required
        else:
            logging.info("No application forms configured (this is optional)")
            validation_results["application_config"] = True  # Not required

        # Validate reaction roles (optional system)
        if reaction_channel_id and reaction_message_id and reaction_roles:
            channel = bot.get_channel(reaction_channel_id)
            validation_results["reaction_roles"] = channel is not None
        else:
            logging.info("Reaction roles not configured (this is optional)")
            validation_results["reaction_roles"] = True  # Not required

        # Validate backup system
        try:
            # Test backup system without actually creating a backup
            validation_results["backup_system"] = validation_results["database"]
        except Exception as e:
            logging.warning(f"Backup system validation failed: {e}")
            validation_results["backup_system"] = False

        # Log results
        passed = sum(validation_results.values())
        total = len(validation_results)

        logging.info(f"Startup validation completed: {passed}/{total} systems validated")

        for system, status in validation_results.items():
            status_text = "✅ PASS" if status else "❌ FAIL"
            logging.info(f"  {system}: {status_text}")

        # Return overall success (all critical systems must pass)
        critical_systems = ["database"]  # Only database is truly critical
        critical_passed = all(validation_results[system] for system in critical_systems)

        if critical_passed:
            logging.info("All critical systems validated successfully")
        else:
            logging.error("Critical system validation failed")

        return critical_passed

    except Exception as e:
        logging.error(f"Error during startup validation: {e}")
        return False

async def validate_interaction_for_modal(interaction):
    """
    Validate that an interaction is suitable for sending a modal.
    Modals can only be sent as initial responses to interactions.
    """
    try:
        if interaction is None:
            logging.error("Interaction is None - cannot send modal")
            return False

        if not hasattr(interaction, 'response'):
            logging.error("Interaction missing response attribute - cannot send modal")
            return False

        if interaction.response.is_done():
            logging.error("Interaction response already used - cannot send modal")
            return False

        # Check if interaction is expired (Discord interactions expire after 15 minutes)
        if hasattr(interaction, 'created_at'):
            from datetime import datetime, timezone
            current_time = datetime.now(timezone.utc)
            interaction_age = (current_time - interaction.created_at).total_seconds()

            if interaction_age > 900:  # 15 minutes
                logging.error(f"Interaction is too old ({interaction_age:.1f}s) - cannot send modal")
                return False

        return True

    except Exception as e:
        logging.error(f"Error validating interaction for modal: {e}")
        return False

async def safe_send_modal(interaction, modal, user_id=None, context="application"):
    """
    Safely send a modal with comprehensive validation and error handling.

    Args:
        interaction: Discord interaction object
        modal: Modal object to send
        user_id: Optional user ID for logging
        context: Context description for logging

    Returns:
        bool: True if modal was sent successfully, False otherwise
    """
    try:
        # Validate interaction
        if not await validate_interaction_for_modal(interaction):
            logging.error(f"Cannot send {context} modal - invalid interaction context")
            return False

        # Validate modal
        if modal is None:
            logging.error(f"Cannot send {context} modal - modal is None")
            return False

        # Validate modal components
        if not await validate_modal_components(modal, context):
            logging.error(f"Cannot send {context} modal - component validation failed")
            return False

        # Identify any component type issues
        invalid_count = force_fix_modal_component_types(modal, context)
        if invalid_count > 0:
            logging.error(f"Found {invalid_count} components with invalid types in {context} modal - this will likely cause HTTP 400")

        # Inspect modal payload before sending
        inspect_modal_payload(modal, context)

        # Add detailed pre-send inspection of modal components
        logging.debug(f"About to send {context} modal - inspecting components before Discord API call:")
        for i, component in enumerate(modal.children):
            component_type = getattr(component, 'type', 'None')
            component_style = getattr(component, 'style', 'None')
            component_label = getattr(component, 'label', 'None')

            # Get the actual integer value that will be sent to Discord
            type_value = None
            if hasattr(component_type, 'value'):
                type_value = component_type.value
            elif isinstance(component_type, int):
                type_value = component_type
            else:
                type_value = str(component_type)

            style_value = None
            if hasattr(component_style, 'value'):
                style_value = component_style.value
            elif isinstance(component_style, int):
                style_value = component_style
            else:
                style_value = str(component_style)

            logging.debug(f"  Component {i}: type={component_type} (value={type_value}), style={component_style} (value={style_value}), label='{component_label}'")

            # Check if the type value is in Discord's valid range
            if isinstance(type_value, int) and type_value not in [4, 5, 6, 7, 10, 12]:
                logging.error(f"  ❌ Component {i} has invalid type value {type_value} that will be rejected by Discord!")
            elif type_value in [4, 5]:
                logging.debug(f"  ✅ Component {i} has valid TextInput type value {type_value}")
            else:
                logging.warning(f"  ⚠️ Component {i} has type value {type_value} - not a TextInput type")

        # Create a test modal to compare
        test_modal = create_minimal_test_modal()

        # Final payload inspection before sending
        logging.debug("=== FINAL PAYLOAD INSPECTION BEFORE DISCORD API CALL ===")
        try:
            # Try to get the raw payload that would be sent
            if hasattr(modal, 'to_dict'):
                actual_payload = modal.to_dict()
                logging.debug(f"Actual modal payload: {actual_payload}")

                # Detailed component analysis
                if 'components' in actual_payload:
                    for row_idx, component_row in enumerate(actual_payload['components']):
                        if 'components' in component_row:
                            for comp_idx, component in enumerate(component_row['components']):
                                comp_type = component.get('type')
                                comp_style = component.get('style')
                                comp_label = component.get('label')

                                logging.debug(f"Row {row_idx}, Component {comp_idx}: type={comp_type}, style={comp_style}, label='{comp_label}'")

                                # Check if this is the problematic component
                                if isinstance(comp_type, int) and comp_type not in [4, 5, 6, 7, 10, 12]:
                                    logging.error(f"🚨 FOUND INVALID COMPONENT TYPE IN FINAL PAYLOAD: {comp_type}")
                                    logging.error(f"🚨 This component will cause HTTP 400 error!")
                                    logging.error(f"🚨 Component details: {component}")

                                    # Try to fix it in the payload
                                    logging.warning("Attempting to fix component type in payload...")
                                    component['type'] = 4  # Force TextInput type
                                    logging.warning(f"Fixed component type to: {component['type']}")
        except Exception as payload_error:
            logging.error(f"Error inspecting final payload: {payload_error}")

        logging.debug("=== END FINAL PAYLOAD INSPECTION ===")

        # Remove any existing monkey patches to avoid recursion
        if hasattr(modal, '_original_to_dict'):
            modal.to_dict = modal._original_to_dict

        # Store the original method
        modal._original_to_dict = modal.to_dict

        # DIRECT FIX: Create a completely new modal with correct components
        logging.debug("=== CREATING DIRECT FIX MODAL ===")

        # SIMPLE FIX: Apply one-time payload fix without recursion
        def create_fixed_payload():
            try:
                # Call the original to_dict method directly
                result = modal._original_to_dict()

                logging.debug(f"Original modal payload: {result}")

                # CRITICAL FIX: Remove any invalid top-level 'type' field
                if 'type' in result:
                    logging.warning(f"Removing invalid top-level 'type' field: {result['type']}")
                    del result['type']

                # Ensure only valid top-level fields exist
                valid_modal_fields = {'custom_id', 'title', 'components'}
                invalid_fields = set(result.keys()) - valid_modal_fields
                for field in invalid_fields:
                    logging.warning(f"Removing invalid top-level field: {field} = {result[field]}")
                    del result[field]

                # Add top-level custom_id if missing
                if 'custom_id' not in result or not result['custom_id']:
                    result['custom_id'] = f"modal_{hash(result.get('title', 'modal'))}"
                    logging.warning(f"Added missing top-level custom_id: {result['custom_id']}")

                # Fix component types and ensure custom_ids
                if 'components' in result:
                    for row_idx, component_row in enumerate(result['components']):
                        if 'components' in component_row:
                            for comp_idx, component in enumerate(component_row['components']):
                                # Force type to 4 (TextInput)
                                if component.get('type') != 4:
                                    original_type = component.get('type')
                                    component['type'] = 4
                                    logging.warning(f"Fixed component type from {original_type} to 4")

                                # Ensure custom_id exists
                                if 'custom_id' not in component or not component['custom_id']:
                                    component['custom_id'] = f"text_input_{row_idx}_{comp_idx}"
                                    logging.warning(f"Added component custom_id: {component['custom_id']}")

                # Final validation
                logging.debug(f"Fixed modal payload: {result}")
                logging.debug(f"Modal top-level fields: {list(result.keys())}")

                return result
            except Exception as e:
                logging.error(f"Error creating fixed payload: {e}")
                return modal._original_to_dict()

        # Replace to_dict with the fixed version (no recursion)
        modal.to_dict = create_fixed_payload

        # Send the modal with fixed payload
        await interaction.response.send_modal(modal)

        user_info = f" to user {user_id}" if user_id else ""
        logging.info(f"Successfully sent {context} modal{user_info}")
        return True

    except discord.InteractionResponded:
        logging.error(f"Cannot send {context} modal - interaction already responded")
        return False
    except discord.HTTPException as e:
        logging.error(f"HTTP error sending {context} modal: {e}")

        # Try to get more details about the error
        if hasattr(e, 'response') and hasattr(e.response, 'text'):
            try:
                error_text = await e.response.text()
                logging.error(f"HTTP error response text: {error_text}")
            except:
                pass

        # Log the modal payload that caused the error
        try:
            error_payload = modal.to_dict()
            logging.error(f"Modal payload that caused HTTP error: {error_payload}")

            # Analyze the payload for invalid types
            if 'components' in error_payload:
                for row_idx, row in enumerate(error_payload['components']):
                    if 'components' in row:
                        for comp_idx, comp in enumerate(row['components']):
                            comp_type = comp.get('type')
                            if isinstance(comp_type, int) and comp_type not in [4, 5, 6, 7, 10, 12]:
                                logging.error(f"🚨 FOUND INVALID TYPE IN ERROR PAYLOAD: Row {row_idx}, Component {comp_idx}, Type {comp_type}")
                            elif not isinstance(comp_type, int):
                                logging.error(f"🚨 FOUND NON-INTEGER TYPE IN ERROR PAYLOAD: Row {row_idx}, Component {comp_idx}, Type {comp_type} ({type(comp_type)})")
        except Exception as payload_error:
            logging.error(f"Error analyzing modal payload: {payload_error}")


        # If HTTP error, try emergency modal as fallback for application modals
        if context.startswith("application") and hasattr(modal, 'user_id') and hasattr(modal, 'questions'):
            try:
                logging.warning(f"Attempting emergency modal fallback for {context}")
                emergency_modal = create_emergency_modal(
                    modal.user_id,
                    modal.questions,
                    modal.start_index,
                    modal.total_questions,
                    modal.application_name
                )

                if emergency_modal:
                    # Try sending the emergency modal
                    await interaction.response.send_modal(emergency_modal)
                    logging.info(f"Successfully sent emergency modal for {context}")
                    return True
                else:
                    logging.error("Failed to create emergency modal")
            except Exception as emergency_error:
                logging.error(f"Emergency modal fallback also failed: {emergency_error}")

        return False
    except Exception as e:
        logging.error(f"Unexpected error sending {context} modal: {e}")
        import traceback
        traceback.print_exc()
        return False

def is_valid_text_input_component_type(component_type):
    """
    Check if a component type is valid for TextInput in modals.
    Handles both enum values (ComponentType.text_input) and integer values (4, 5).

    Args:
        component_type: Component type to validate

    Returns:
        bool: True if valid TextInput component type, False otherwise
    """
    try:
        # Check against ComponentType enum if available
        if hasattr(discord, 'ComponentType') and hasattr(discord.ComponentType, 'text_input'):
            if component_type == discord.ComponentType.text_input:
                return True

        # Check against integer values (4 = short TextInput, 5 = paragraph TextInput)
        if component_type in [4, 5]:
            return True

        # Check if it's an enum with a value attribute
        if hasattr(component_type, 'value') and component_type.value in [4, 5]:
            return True

        return False

    except Exception as e:
        logging.error(f"Error validating component type {component_type}: {e}")
        return False

def get_component_type_info(component_type):
    """
    Get readable information about a component type for logging.

    Args:
        component_type: Component type to describe

    Returns:
        str: Human-readable description of the component type
    """
    try:
        if hasattr(component_type, 'value'):
            return f"{component_type} (value: {component_type.value})"
        else:
            return str(component_type)
    except:
        return f"Unknown ({component_type})"

def inspect_modal_payload(modal, context="modal"):
    """
    Inspect the modal's internal structure to see what would be sent to Discord.
    This helps identify component type issues before they cause HTTP 400 errors.
    """
    try:
        logging.debug(f"=== MODAL PAYLOAD INSPECTION ({context}) ===")

        # Check if modal has to_dict method (discord.py internal serialization)
        if hasattr(modal, 'to_dict'):
            try:
                modal_dict = modal.to_dict()
                logging.debug(f"Modal dict: {modal_dict}")

                if 'components' in modal_dict:
                    for i, component_row in enumerate(modal_dict['components']):
                        if 'components' in component_row:
                            for j, component in enumerate(component_row['components']):
                                comp_type = component.get('type', 'Unknown')
                                comp_style = component.get('style', 'Unknown')
                                comp_label = component.get('label', 'Unknown')
                                logging.debug(f"  Serialized Component {j}: type={comp_type}, style={comp_style}, label='{comp_label}'")

                                if isinstance(comp_type, int) and comp_type not in [4, 5, 6, 7, 10, 12]:
                                    logging.error(f"  ❌ FOUND INVALID TYPE IN SERIALIZED PAYLOAD: {comp_type}")
            except Exception as e:
                logging.warning(f"Could not serialize modal to dict: {e}")

        # Direct component inspection
        logging.debug(f"Direct component inspection:")
        for i, component in enumerate(modal.children):
            # Get all relevant attributes
            attrs = {}
            for attr in ['type', 'style', 'label', 'placeholder', 'required', 'min_length', 'max_length']:
                if hasattr(component, attr):
                    value = getattr(component, attr)
                    if hasattr(value, 'value'):
                        attrs[attr] = f"{value} (value={value.value})"
                    else:
                        attrs[attr] = value
                else:
                    attrs[attr] = 'Not set'

            logging.debug(f"  Component {i} attributes: {attrs}")

        logging.debug(f"=== END MODAL PAYLOAD INSPECTION ===")

    except Exception as e:
        logging.error(f"Error during modal payload inspection: {e}")

def test_textinput_creation():
    """
    Create test TextInput components to verify their type values.
    This helps identify if the issue is with TextInput creation itself.
    """
    try:
        logging.debug("=== TESTING TEXTINPUT CREATION ===")

        # Test short style TextInput
        test_short = discord.ui.TextInput(
            label="Test Short",
            style=discord.TextStyle.short,
            placeholder="Test"
        )

        short_type = getattr(test_short, 'type', 'No type')
        short_type_value = getattr(short_type, 'value', short_type) if hasattr(short_type, 'value') else short_type
        short_style = getattr(test_short, 'style', 'No style')
        short_style_value = getattr(short_style, 'value', short_style) if hasattr(short_style, 'value') else short_style

        logging.debug(f"Short TextInput: type={short_type} (value={short_type_value}), style={short_style} (value={short_style_value})")

        # Test paragraph style TextInput
        test_paragraph = discord.ui.TextInput(
            label="Test Paragraph",
            style=discord.TextStyle.paragraph,
            placeholder="Test"
        )

        para_type = getattr(test_paragraph, 'type', 'No type')
        para_type_value = getattr(para_type, 'value', para_type) if hasattr(para_type, 'value') else para_type
        para_style = getattr(test_paragraph, 'style', 'No style')
        para_style_value = getattr(para_style, 'value', para_style) if hasattr(para_style, 'value') else para_style

        logging.debug(f"Paragraph TextInput: type={para_type} (value={para_type_value}), style={para_style} (value={para_style_value})")

        # Check if type values are in valid range
        for name, type_val in [("Short", short_type_value), ("Paragraph", para_type_value)]:
            if isinstance(type_val, int):
                if type_val in [4, 5, 6, 7, 10, 12]:
                    logging.debug(f"✅ {name} TextInput has valid type value: {type_val}")
                else:
                    logging.error(f"❌ {name} TextInput has INVALID type value: {type_val}")
            else:
                logging.warning(f"⚠️ {name} TextInput type value is not integer: {type_val}")

        logging.debug("=== END TEXTINPUT CREATION TEST ===")

    except Exception as e:
        logging.error(f"Error during TextInput creation test: {e}")

def create_minimal_test_modal():
    """
    Create a minimal test modal to isolate the TextInput type issue.
    This helps identify if the problem is with TextInput creation itself.
    """
    try:
        logging.debug("=== CREATING MINIMAL TEST MODAL ===")

        # Create a minimal modal
        class TestModal(discord.ui.Modal):
            def __init__(self):
                super().__init__(title="Test Modal")

                # Create a single TextInput
                test_input = discord.ui.TextInput(
                    label="Test Input",
                    placeholder="Test placeholder",
                    style=discord.TextStyle.short,
                    required=True
                )

                # Log the component details immediately after creation
                input_type = getattr(test_input, 'type', 'No type')
                input_type_value = getattr(input_type, 'value', input_type) if hasattr(input_type, 'value') else input_type
                input_style = getattr(test_input, 'style', 'No style')
                input_style_value = getattr(input_style, 'value', input_style) if hasattr(input_style, 'value') else input_style

                logging.debug(f"Test TextInput created: type={input_type} (value={input_type_value}), style={input_style} (value={input_style_value})")

                # Check if type value is valid
                if isinstance(input_type_value, int):
                    if input_type_value in [4, 5, 6, 7, 10, 12]:
                        logging.debug(f"✅ Test TextInput has VALID type value: {input_type_value}")
                    else:
                        logging.error(f"❌ Test TextInput has INVALID type value: {input_type_value}")
                else:
                    logging.warning(f"⚠️ Test TextInput type value is not integer: {input_type_value} (type: {type(input_type_value)})")

                self.add_item(test_input)

                # Check the modal's children after adding
                logging.debug(f"Test modal children count: {len(self.children)}")
                for i, child in enumerate(self.children):
                    child_type = getattr(child, 'type', 'No type')
                    child_type_value = getattr(child_type, 'value', child_type) if hasattr(child_type, 'value') else child_type
                    logging.debug(f"Child {i}: type={child_type} (value={child_type_value})")

        # Create the test modal
        test_modal = TestModal()

        # Try to serialize it to see what would be sent to Discord
        if hasattr(test_modal, 'to_dict'):
            try:
                modal_dict = test_modal.to_dict()
                logging.debug(f"Test modal serialized: {modal_dict}")

                # Check the serialized components
                if 'components' in modal_dict:
                    for i, component_row in enumerate(modal_dict['components']):
                        if 'components' in component_row:
                            for j, component in enumerate(component_row['components']):
                                comp_type = component.get('type', 'Unknown')
                                comp_style = component.get('style', 'Unknown')
                                logging.debug(f"Serialized component {j}: type={comp_type}, style={comp_style}")

                                if isinstance(comp_type, int) and comp_type not in [4, 5, 6, 7, 10, 12]:
                                    logging.error(f"❌ INVALID TYPE IN TEST MODAL SERIALIZATION: {comp_type}")
                                elif comp_type in [4, 5]:
                                    logging.debug(f"✅ Valid TextInput type in serialization: {comp_type}")
                                else:
                                    logging.warning(f"⚠️ Unexpected component type in serialization: {comp_type}")
            except Exception as serialization_error:
                logging.error(f"Error serializing test modal: {serialization_error}")

        logging.debug("=== END MINIMAL TEST MODAL ===")
        return test_modal

    except Exception as e:
        logging.error(f"Error creating minimal test modal: {e}")
        return None

def test_isolated_textinput_creation():
    """
    Test TextInput creation in complete isolation to identify the root cause.
    """
    try:
        logging.debug("=== ISOLATED TEXTINPUT CREATION TEST ===")

        # Test 1: Create TextInput with minimal parameters
        logging.debug("Test 1: Minimal TextInput creation")
        minimal_input = discord.ui.TextInput(label="Test")
        min_type = getattr(minimal_input, 'type', 'No type')
        min_type_value = getattr(min_type, 'value', min_type) if hasattr(min_type, 'value') else min_type
        logging.debug(f"Minimal TextInput: type={min_type} (value={min_type_value})")

        # Test 2: Create TextInput with short style
        logging.debug("Test 2: Short style TextInput creation")
        short_input = discord.ui.TextInput(
            label="Short Test",
            style=discord.TextStyle.short
        )
        short_type = getattr(short_input, 'type', 'No type')
        short_type_value = getattr(short_type, 'value', short_type) if hasattr(short_type, 'value') else short_type
        logging.debug(f"Short TextInput: type={short_type} (value={short_type_value})")

        # Test 3: Create TextInput with paragraph style
        logging.debug("Test 3: Paragraph style TextInput creation")
        para_input = discord.ui.TextInput(
            label="Paragraph Test",
            style=discord.TextStyle.paragraph
        )
        para_type = getattr(para_input, 'type', 'No type')
        para_type_value = getattr(para_type, 'value', para_type) if hasattr(para_type, 'value') else para_type
        logging.debug(f"Paragraph TextInput: type={para_type} (value={para_type_value})")

        # Test 4: Create TextInput with all parameters (like in ApplicationModal)
        logging.debug("Test 4: Full parameter TextInput creation")
        full_input = discord.ui.TextInput(
            label="Full Test Input",
            placeholder="Test placeholder",
            style=discord.TextStyle.short,
            required=True,
            max_length=1000,
            min_length=1
        )
        full_type = getattr(full_input, 'type', 'No type')
        full_type_value = getattr(full_type, 'value', full_type) if hasattr(full_type, 'value') else full_type
        logging.debug(f"Full TextInput: type={full_type} (value={full_type_value})")

        # Analyze all results
        test_results = [
            ("Minimal", min_type_value),
            ("Short", short_type_value),
            ("Paragraph", para_type_value),
            ("Full", full_type_value)
        ]

        for test_name, type_value in test_results:
            if isinstance(type_value, int):
                if type_value == 4:
                    logging.debug(f"✅ {test_name} TextInput has correct type value: {type_value}")
                elif type_value in [5, 6, 7, 10, 12]:
                    logging.warning(f"⚠️ {test_name} TextInput has valid but unexpected type value: {type_value}")
                else:
                    logging.error(f"❌ {test_name} TextInput has INVALID type value: {type_value}")
            else:
                logging.warning(f"⚠️ {test_name} TextInput has non-integer type: {type_value} (type: {type(type_value)})")

        logging.debug("=== END ISOLATED TEXTINPUT CREATION TEST ===")

    except Exception as e:
        logging.error(f"Error during isolated TextInput creation test: {e}")
        import traceback
        traceback.print_exc()

def force_fix_modal_component_types(modal, context="modal"):
    """
    Aggressively fix all component types in a modal to ensure they're valid.
    This is a last resort to ensure modal sending works.
    """
    try:
        logging.debug(f"=== FORCE FIXING COMPONENT TYPES ({context}) ===")

        fixed_count = 0
        for i, component in enumerate(modal.children):
            original_type = getattr(component, 'type', None)
            original_type_value = getattr(original_type, 'value', original_type) if hasattr(original_type, 'value') else original_type

            # Check if component needs fixing
            needs_fix = False
            if original_type is None:
                needs_fix = True
                logging.warning(f"Component {i} has no type attribute")
            elif isinstance(original_type_value, int) and original_type_value not in [4, 5, 6, 7, 10, 12]:
                needs_fix = True
                logging.warning(f"Component {i} has invalid type value: {original_type_value}")
            elif not isinstance(original_type_value, int):
                # Try to get the value if it's an enum
                if hasattr(original_type, 'value'):
                    if original_type.value not in [4, 5, 6, 7, 10, 12]:
                        needs_fix = True
                        logging.warning(f"Component {i} has invalid enum type value: {original_type.value}")
                else:
                    needs_fix = True
                    logging.warning(f"Component {i} has non-integer type: {original_type_value}")

            if needs_fix:
                # Cannot fix read-only type property, just log the issue
                logging.error(f"Component {i} has invalid type {original_type_value} but type property is read-only")
                logging.error(f"Component {i} details: label={getattr(component, 'label', 'None')}, style={getattr(component, 'style', 'None')}")
                fixed_count += 1  # Count as "identified" rather than "fixed"
            else:
                logging.debug(f"Component {i} type is valid: {original_type_value}")

        if fixed_count > 0:
            logging.error(f"Identified {fixed_count} components with invalid types in {context} modal")
        else:
            logging.debug(f"All component types are valid in {context} modal")

        logging.debug(f"=== END FORCE FIXING COMPONENT TYPES ===")
        return fixed_count

    except Exception as e:
        logging.error(f"Error during force fix of component types: {e}")
        return 0

def create_emergency_modal(user_id, questions, start_index, total_questions, application_name):
    """
    Create a modal using a completely different approach as emergency fallback.
    """
    try:
        logging.debug("=== CREATING EMERGENCY MODAL ===")

        # Create a basic modal
        class EmergencyModal(discord.ui.Modal):
            def __init__(self):
                super().__init__(title=f"Application ({start_index + 1}-{start_index + len(questions)} of {total_questions})", timeout=300)
                self.user_id = user_id
                self.questions = questions
                self.start_index = start_index
                self.total_questions = total_questions
                self.application_name = application_name

                # Add components one by one with explicit validation
                for i, question in enumerate(questions[:5]):  # Max 5 components
                    question_num = start_index + i + 1
                    label = question[:45] if len(question) <= 45 else question[:42] + "..."

                    # Create TextInput with minimal parameters to avoid issues
                    text_input = discord.ui.TextInput(
                        label=label,
                        placeholder=f"Answer {question_num}",
                        required=True,
                        max_length=1000
                    )

                    # Log the component immediately after creation
                    comp_type = getattr(text_input, 'type', 'None')
                    comp_type_value = getattr(comp_type, 'value', comp_type) if hasattr(comp_type, 'value') else comp_type
                    logging.debug(f"Emergency modal component {i}: type={comp_type} (value={comp_type_value})")

                    self.add_item(text_input)

            async def on_submit(self, interaction: discord.Interaction):
                # Copy the exact logic from ApplicationModal.on_submit
                try:
                    user_id = self.user_id

                    if user_id not in modal_application_state:
                        await interaction.response.send_message(
                            "❌ Application session expired. Please start a new application.",
                            ephemeral=True
                        )
                        return

                    # Get responses from the modal
                    responses = [child.value for child in self.children]

                    # Store responses in the state
                    state = modal_application_state[user_id]
                    logging.debug(f"Emergency modal state keys: {list(state.keys())}")

                    if "responses" not in state:
                        state["responses"] = []
                    if "answers" not in state:
                        state["answers"] = []

                    # Add responses for this batch of questions (match ApplicationModal format)
                    for i, response in enumerate(responses):
                        question_index = self.start_index + i
                        if question_index < self.total_questions:  # Check against total, not current batch
                            # Get the actual question text from the application form
                            if self.application_name in application_forms:
                                all_questions = application_forms[self.application_name]
                                if question_index < len(all_questions):
                                    question_text = all_questions[question_index]
                                else:
                                    question_text = f"Question {question_index + 1}"
                            else:
                                question_text = f"Question {question_index + 1}"

                            state["responses"].append({
                                "question": question_text,
                                "answer": response
                            })
                            # Also add to answers array for compatibility
                            state["answers"].append(response)

                    # Update current question index for compatibility
                    if "current_question_index" not in state:
                        state["current_question_index"] = 0
                    state["current_question_index"] = self.start_index + len(responses)

                    # Check if we have more questions
                    current_index = state["current_question_index"]
                    remaining_questions = self.total_questions - current_index

                    logging.debug(f"Emergency modal: current_index={current_index}, total={self.total_questions}, remaining={remaining_questions}")

                    if remaining_questions > 0:
                        # Send continue embed instead of automatic modal chaining
                        logging.debug(f"Emergency modal: More questions remaining, sending continue embed")
                        await send_continue_application_embed(interaction, user_id, state)
                    else:
                        # All questions answered, complete the application
                        logging.debug(f"Emergency modal: All questions completed, finishing application")
                        # complete_modal_application will handle the response, so don't send one here
                        await complete_modal_application(interaction.user, user_id, interaction)

                except Exception as e:
                    logging.error(f"Error in emergency modal submission: {e}")
                    await interaction.response.send_message("❌ An error occurred. Please try again.", ephemeral=True)

        emergency_modal = EmergencyModal()

        # Test the payload
        try:
            test_payload = emergency_modal.to_dict()
            logging.debug(f"Emergency modal payload: {test_payload}")

            # Validate payload
            if 'components' in test_payload:
                for row in test_payload['components']:
                    if 'components' in row:
                        for comp in row['components']:
                            comp_type = comp.get('type')
                            if isinstance(comp_type, int) and comp_type in [4, 5, 6, 7, 10, 12]:
                                logging.debug(f"✅ Emergency modal component has valid type: {comp_type}")
                            else:
                                logging.error(f"❌ Emergency modal component has invalid type: {comp_type}")
        except Exception as payload_error:
            logging.error(f"Error testing emergency modal payload: {payload_error}")

        logging.debug("=== END CREATING EMERGENCY MODAL ===")
        return emergency_modal

    except Exception as e:
        logging.error(f"Error creating emergency modal: {e}")
        return None

class FixedApplicationModal(discord.ui.Modal):
    """
    A Modal class that overrides to_dict() to ensure all component types are correct
    in the final payload sent to Discord. This works around discord.py component type issues.
    """

    def to_dict(self):
        """Override to_dict to fix component types in the serialized payload"""
        try:
            # Get the original payload
            original_dict = super().to_dict()

            logging.debug("=== FIXING MODAL PAYLOAD ===")
            logging.debug(f"Original payload: {original_dict}")

            # CRITICAL FIX: Remove any invalid top-level 'type' field
            if 'type' in original_dict:
                logging.warning(f"Removing invalid top-level 'type' field: {original_dict['type']}")
                del original_dict['type']

            # Ensure only valid top-level fields exist
            valid_modal_fields = {'custom_id', 'title', 'components'}
            invalid_fields = set(original_dict.keys()) - valid_modal_fields
            for field in invalid_fields:
                logging.warning(f"Removing invalid top-level field: {field} = {original_dict[field]}")
                del original_dict[field]

            # Add top-level custom_id if missing
            if 'custom_id' not in original_dict or not original_dict['custom_id']:
                original_dict['custom_id'] = f"modal_{hash(original_dict.get('title', 'modal'))}"
                logging.warning(f"Added missing top-level custom_id: {original_dict['custom_id']}")

            # Fix component types in the payload (keep separate Action Rows)
            if 'components' in original_dict:
                for row_idx, component_row in enumerate(original_dict['components']):
                    if 'components' in component_row:
                        for comp_idx, component in enumerate(component_row['components']):
                            original_type = component.get('type')
                            original_style = component.get('style')

                            # Fix component type - force all to 4 (TextInput) if invalid
                            if isinstance(original_type, int) and original_type not in [4, 5, 6, 7, 10, 12]:
                                logging.warning(f"Fixing invalid component type {original_type} to 4 in payload")
                                component['type'] = 4
                            elif not isinstance(original_type, int):
                                logging.warning(f"Fixing non-integer component type {original_type} to 4 in payload")
                                component['type'] = 4
                            else:
                                # Even if type looks valid, ensure it's exactly 4 for TextInput
                                if original_type != 4:
                                    logging.warning(f"Normalizing component type {original_type} to 4 (TextInput) in payload")
                                    component['type'] = 4

                            # Fix component style - ensure it's 1 (short) or 2 (paragraph)
                            if isinstance(original_style, int):
                                if original_style not in [1, 2]:
                                    logging.warning(f"Fixing invalid style {original_style} to 1 (short) in payload")
                                    component['style'] = 1
                            else:
                                logging.warning(f"Fixing non-integer style {original_style} to 1 (short) in payload")
                                component['style'] = 1

                            # Ensure custom_id is present
                            if 'custom_id' not in component or not component['custom_id']:
                                component['custom_id'] = f"text_input_{row_idx}_{comp_idx}"
                                logging.warning(f"Added missing component custom_id: {component['custom_id']}")

                            logging.debug(f"Row {row_idx}, Component {comp_idx}: type={component.get('type')}, style={component.get('style')}, custom_id={component.get('custom_id')}, label={component.get('label')}")

            # Final validation
            logging.debug(f"Fixed payload: {original_dict}")
            logging.debug(f"Modal top-level fields: {list(original_dict.keys())}")
            logging.debug("=== END FIXING MODAL PAYLOAD ===")

            return original_dict

        except Exception as e:
            logging.error(f"Error fixing modal payload: {e}")
            # Return original payload if fixing fails
            return super().to_dict()

def test_fixed_modal_payload():
    """
    Test the FixedApplicationModal to ensure it properly fixes component types in payload.
    """
    try:
        logging.debug("=== TESTING FIXED MODAL PAYLOAD ===")

        # Create a test modal
        class TestFixedModal(FixedApplicationModal):
            def __init__(self):
                super().__init__(title="Test Fixed Modal")

                # Add a test TextInput
                test_input = discord.ui.TextInput(
                    label="Test Input",
                    placeholder="Test",
                    style=discord.TextStyle.short
                )
                self.add_item(test_input)

        test_modal = TestFixedModal()

        # Get the payload and check if it's fixed
        payload = test_modal.to_dict()

        # Verify the payload has correct component types
        if 'components' in payload:
            for row in payload['components']:
                if 'components' in row:
                    for component in row['components']:
                        comp_type = component.get('type')
                        comp_style = component.get('style')

                        if comp_type == 4 and comp_style in [1, 2]:
                            logging.debug(f"✅ Test modal payload has correct values: type={comp_type}, style={comp_style}")
                        else:
                            logging.error(f"❌ Test modal payload still has invalid values: type={comp_type}, style={comp_style}")

        logging.debug("=== END TESTING FIXED MODAL PAYLOAD ===")
        return test_modal

    except Exception as e:
        logging.error(f"Error testing fixed modal payload: {e}")
        return None

async def validate_modal_components(modal, context="modal"):
    """
    Validate that all components in a modal have correct types for Discord API.

    Args:
        modal: Discord modal object to validate
        context: Context description for logging

    Returns:
        bool: True if all components are valid, False otherwise
    """
    try:
        if not hasattr(modal, 'children'):
            logging.error(f"Modal {context} has no children attribute")
            return False

        for i, component in enumerate(modal.children):
            if not hasattr(component, 'type'):
                logging.error(f"Component {i} in {context} modal has no type attribute")
                return False

            component_type = component.type

            # Use helper function to validate component type
            if not is_valid_text_input_component_type(component_type):
                logging.error(f"Component {i} in {context} modal has invalid type: {get_component_type_info(component_type)}")
                logging.error(f"Valid types for modal components are: TextInput (ComponentType.text_input or values 4, 5)")
                return False

            logging.debug(f"Component {i} type validation passed: {get_component_type_info(component_type)}")

            # Additional validation for TextInput components
            if hasattr(component, 'style'):
                if component.style not in [discord.TextStyle.short, discord.TextStyle.paragraph]:
                    logging.error(f"Component {i} in {context} modal has invalid style: {component.style}")
                    return False

        logging.debug(f"Modal {context} validation passed - {len(modal.children)} components validated")
        return True

    except Exception as e:
        logging.error(f"Error validating {context} modal components: {e}")
        return False

async def start_application(user, application_name):
    """
    Handles the application process for the user with a professional interface.
    Includes fallback to modal-based system if DMs fail.
    """
    try:
        # Ensure application_forms is accessible
        if not application_forms:
            error_embed = discord.Embed(
                title="Application Error",
                description="No application forms are currently available. Please contact an administrator for assistance.",
                color=0xE74C3C  # Professional red color
            )
            error_embed.set_footer(text="Application System • Error")
            try:
                await user.send(embed=error_embed)
            except discord.errors.Forbidden:
                # If we can't send error DM, we can't help much here
                logging.warning(f"Could not send error DM to user {user.id} - DMs disabled")
            return

        # Get the questions for the selected application
        questions = application_forms.get(application_name)
        if not questions:
            error_embed = discord.Embed(
                title="Application Error",
                description=f"The application '{application_name}' could not be found. Please contact an administrator for assistance.",
                color=0xE74C3C  # Professional red color
            )
            error_embed.set_footer(text="Application System • Error")
            try:
                await user.send(embed=error_embed)
            except discord.errors.Forbidden:
                logging.warning(f"Could not send error DM to user {user.id} - DMs disabled")
            return

        # Try DM-based application first (existing system)
        try:
            await start_dm_application(user, application_name, questions)
            logging.info(f"Successfully started DM application for user {user.id}: {application_name}")
        except discord.errors.Forbidden:
            # DMs are disabled, use modal fallback
            logging.info(f"DMs disabled for user {user.id}, using modal fallback for application {application_name}")
            await start_modal_application(user, application_name, questions)
        except Exception as e:
            logging.error(f"Error in DM application for user {user.id}: {e}")
            # Try modal fallback as last resort
            try:
                await start_modal_application(user, application_name, questions)
            except Exception as fallback_error:
                logging.error(f"Modal fallback also failed for user {user.id}: {fallback_error}")
                raise e  # Re-raise original error

    except Exception as e:
        logging.error(f"Error in start_application: {e}")
        error_embed = discord.Embed(
            title="Application Error",
            description="An error occurred while processing your application. Please try again later or contact an administrator for assistance.",
            color=0xE74C3C  # Professional red color
        )
        error_embed.set_footer(text="Application System • Error")
        try:
            await user.send(embed=error_embed)
        except discord.errors.Forbidden:
            logging.warning(f"Could not send error DM to user {user.id} - DMs disabled")

async def start_dm_application(user, application_name, questions):
    """
    Handles the DM-based application process (original system).
    """
    answers = []

    # Send professional initial application start message
    embed = discord.Embed(
        title=f"{application_name} Application",
        description="Your application process has begun. Please answer each question thoroughly and professionally.",
        color=0x2b2d31  # Professional dark theme color
    )

    # Add application guidelines
    embed.add_field(
        name="Guidelines",
        value="• You have 60 minutes to complete this application\n• Answer each question as it appears\n• Be honest and detailed in your responses\n• You can cancel the application at any time",
        inline=False
    )

    # Add a divider
    embed.add_field(name="", value="━━━━━━━━━━━━━━━━━━━━━━━", inline=False)

    # Add information about the process
    embed.add_field(
        name="Process",
        value=f"• Total questions: **{len(questions)}**\n• Each question will appear one at a time\n• Type your answer and send it as a message",
        inline=False
    )

    embed.set_footer(text=f"© {datetime.now().year} Application System • Confidential")
    embed.timestamp = datetime.now()

    # Create a professional view with cancel button
    view = View()
    cancel_button = Button(label="Cancel Application", style=discord.ButtonStyle.secondary)

    async def cancel_callback(interaction):
        cancel_embed = discord.Embed(
            title="Application Canceled",
            description="Your application has been canceled. You can restart the process at any time by returning to the application portal.",
            color=0x95A5A6  # Professional gray color
        )
        cancel_embed.set_footer(text="Application System")
        await interaction.response.send_message(embed=cancel_embed)
        return

    cancel_button.callback = cancel_callback
    view.add_item(cancel_button)
    await user.send(embed=embed, view=view)

    # Send a starting message with error handling
    start_embed = discord.Embed(
        title="Application Started",
        description="Your first question will appear momentarily. Please be ready to respond.",
        color=0x2b2d31
    )
    try:
        await user.send(embed=start_embed)
    except discord.errors.Forbidden:
        # If we can't send this, the DM application won't work anyway
        logging.warning(f"Could not send start message to user {user.id} - DMs may be disabled")
        raise  # Re-raise to trigger modal fallback
    except Exception as dm_error:
        logging.error(f"Error sending start message to user {user.id}: {dm_error}")
        raise  # Re-raise to trigger modal fallback

    # Brief delay before first question
    await asyncio.sleep(2)

    # Ask questions one by one with professional formatting
    for i, question in enumerate(questions):
        question_embed = discord.Embed(
            title=f"Question {i+1} of {len(questions)}",
            description=question,
            color=0x3498DB  # Professional blue color
        )
        question_embed.set_footer(text=f"Type your answer below • {i+1}/{len(questions)}")

        try:
            await user.send(embed=question_embed)
        except discord.errors.Forbidden:
            logging.warning(f"Could not send question {i+1} to user {user.id} - DMs may be disabled")
            raise  # Re-raise to trigger modal fallback
        except Exception as dm_error:
            logging.error(f"Error sending question {i+1} to user {user.id}: {dm_error}")
            raise  # Re-raise to trigger modal fallback

        try:
            answer = await bot.wait_for('message', timeout=60.0, check=lambda m: m.author == user and m.channel.type == discord.ChannelType.private)
            answers.append(answer.content)

            # Send confirmation of answer received with error handling
            if i < len(questions) - 1:  # Don't send for the last question
                confirm_embed = discord.Embed(
                    description=f"✓ Answer recorded. Next question coming up...",
                    color=0x2ECC71  # Professional green color
                )
                try:
                    await user.send(embed=confirm_embed)
                except discord.errors.Forbidden:
                    logging.warning(f"Could not send confirmation to user {user.id} - DMs may be disabled")
                    # Don't raise here, just continue with next question
                except Exception as dm_error:
                    logging.error(f"Error sending confirmation to user {user.id}: {dm_error}")
                    # Don't raise here, just continue with next question
                await asyncio.sleep(1)  # Brief pause between questions

        except asyncio.TimeoutError:
            timeout_embed = discord.Embed(
                title="Application Timeout",
                description="You took too long to answer. Your application has been canceled. You can restart the process at any time.",
                color=0xE74C3C  # Professional red color
            )
            timeout_embed.set_footer(text="Application System • Timeout")

            # Send timeout message with proper error handling
            try:
                await user.send(embed=timeout_embed)
            except discord.errors.Forbidden:
                logging.warning(f"Could not send timeout DM to user {user.id} - DMs may be disabled")
            except Exception as dm_error:
                logging.error(f"Error sending timeout DM to user {user.id}: {dm_error}")
            return

    # FIXED: Send completion message ONLY after ALL questions are answered (outside the loop)
    complete_embed = discord.Embed(
        title="Application Submitted",
        description="Thank you for completing your application. Your responses have been recorded and will be reviewed by our staff team.",
        color=0x2ECC71  # Professional green color
    )
    complete_embed.add_field(
        name="Next Steps",
        value="• Your application will be reviewed by our staff team\n• You will be notified of the decision\n• This process typically takes 1-3 days",
        inline=False
    )
    complete_embed.set_footer(text=f"© {datetime.now().year} Application System • Application ID: {user.id}")
    complete_embed.timestamp = datetime.now()

    # Send completion message with proper error handling
    try:
        await user.send(embed=complete_embed)
    except discord.errors.Forbidden:
        logging.warning(f"Could not send completion DM to user {user.id} - DMs may be disabled")
    except Exception as dm_error:
        logging.error(f"Error sending completion DM to user {user.id}: {dm_error}")

    # Log the application
    await log_application(user, application_name, questions, answers, application_log_channel)

# Global storage for modal application state
modal_application_state = {}

async def cleanup_expired_modal_states():
    """
    Cleanup expired modal application states (older than 30 minutes).
    """
    try:
        current_time = datetime.now()
        expired_users = []

        for user_id, state in modal_application_state.items():
            if "created_at" in state:
                time_diff = current_time - state["created_at"]
                if time_diff.total_seconds() > 1800:  # 30 minutes
                    expired_users.append(user_id)

        for user_id in expired_users:
            del modal_application_state[user_id]
            logging.info(f"Cleaned up expired modal application state for user {user_id}")

    except Exception as e:
        logging.error(f"Error cleaning up expired modal states: {e}")

async def start_modal_application(user, application_name, questions):
    """
    Handles the modal-based application process (fallback system).
    """
    try:
        # Initialize application state
        user_id = str(user.id)
        modal_application_state[user_id] = {
            "application_name": application_name,
            "questions": questions,
            "answers": [],
            "current_question_index": 0,
            "total_questions": len(questions),
            "created_at": datetime.now()
        }

        # Send initial notification that we're using modal fallback
        # We need to find a way to notify the user since DMs are disabled
        # This will be handled by the interaction that triggered the application

        # Start the first modal
        await send_next_application_modal(user, user_id)

    except Exception as e:
        logging.error(f"Error in start_modal_application for user {user.id}: {e}")
        # Clean up state
        if user_id in modal_application_state:
            del modal_application_state[user_id]
        raise

async def send_next_application_modal(user, user_id, interaction=None):
    """
    Sends the next modal in the application chain.
    """
    try:
        if user_id not in modal_application_state:
            logging.error(f"No modal application state found for user {user_id}")
            return

        state = modal_application_state[user_id]
        current_index = state["current_question_index"]
        total_questions = state["total_questions"]
        application_name = state["application_name"]

        logging.debug(f"send_next_application_modal: user {user_id}, current_index: {current_index}, total: {total_questions}")

        # Check if we've completed all questions
        if current_index >= total_questions:
            logging.debug(f"All questions completed in send_next_application_modal, finishing application")
            await complete_modal_application(user, user_id, interaction)
            return

        # Get all questions from the application form
        if application_name in application_forms:
            all_questions = application_forms[application_name]
        else:
            logging.error(f"Application form '{application_name}' not found")
            return

        # Calculate how many questions to include in this modal (max 5)
        remaining_questions = total_questions - current_index
        questions_in_modal = min(5, remaining_questions)

        # Create the modal with the next batch of questions
        next_questions = all_questions[current_index:current_index + questions_in_modal]

        modal = ApplicationModal(
            user_id=user_id,
            questions=next_questions,
            start_index=current_index,
            total_questions=total_questions,
            application_name=application_name
        )

        if interaction:
            # Use safe modal sending for chained modals
            modal_sent = await safe_send_modal(interaction, modal, user_id, "application chain")

            if not modal_sent:
                # Modal sending failed, provide fallback message
                remaining_after_this = remaining_questions - questions_in_modal
                if remaining_after_this > 0:
                    fallback_msg = f"❌ Unable to continue with modal forms. You have {remaining_after_this} questions remaining. Please restart your application."
                else:
                    fallback_msg = "❌ Unable to continue with modal forms. Please restart your application to complete the final questions."

                if not interaction.response.is_done():
                    await interaction.response.send_message(fallback_msg, ephemeral=True)
                # Clean up state
                if user_id in modal_application_state:
                    del modal_application_state[user_id]
        else:
            # This is the first modal, we need to trigger it differently
            # We'll store it and let the confirm button callback handle it
            state["pending_modal"] = modal

    except Exception as e:
        logging.error(f"Error sending next application modal for user {user_id}: {e}")
        # Clean up state
        if user_id in modal_application_state:
            del modal_application_state[user_id]

async def send_continue_application_embed(interaction, user_id, state):
    """
    Send an embed with a continue button for the next part of the application.
    """
    try:
        current_index = state["current_question_index"]
        total_questions = state["total_questions"]
        application_name = state.get("application_name", "Application")

        # Calculate progress
        answered_questions = len(state.get("answers", []))
        remaining_questions = total_questions - answered_questions

        # Create embed
        embed = discord.Embed(
            title="📝 Application Progress",
            description=f"**{application_name}**\n\nYour responses have been saved!",
            color=0x00ff00
        )

        embed.add_field(
            name="Progress",
            value=f"✅ Completed: {answered_questions}/{total_questions} questions\n📋 Remaining: {remaining_questions} questions",
            inline=False
        )

        embed.add_field(
            name="Next Step",
            value="Click the button below to continue with the next set of questions.",
            inline=False
        )

        embed.set_footer(text="Your progress is automatically saved")

        # Create continue button
        class ContinueApplicationView(discord.ui.View):
            def __init__(self, target_user_id):
                super().__init__(timeout=300)  # 5 minute timeout
                self.target_user_id = target_user_id  # Store user ID as instance variable

            @discord.ui.button(
                label="Continue Application",
                style=discord.ButtonStyle.primary,
                emoji="▶️"
            )
            async def continue_application(self, button_interaction: discord.Interaction, button: discord.ui.Button):
                try:
                    # Debug logging
                    logging.debug(f"Continue button clicked by user {button_interaction.user.id}, target user: {self.target_user_id}")

                    # Verify this is the same user (convert both to int for comparison)
                    if int(button_interaction.user.id) != int(self.target_user_id):
                        logging.warning(f"User verification failed: {button_interaction.user.id} != {self.target_user_id}")
                        await button_interaction.response.send_message(
                            "❌ You can only continue your own application.",
                            ephemeral=True
                        )
                        return

                    # Check if state still exists (use string version for state lookup)
                    user_id_str = str(self.target_user_id)
                    if user_id_str not in modal_application_state:
                        logging.warning(f"Application state not found for user {user_id_str}")
                        await button_interaction.response.send_message(
                            "❌ Application session expired. Please start a new application.",
                            ephemeral=True
                        )
                        return

                    logging.debug(f"Sending next modal for user {user_id_str}")
                    # Send the next modal
                    await send_next_application_modal(button_interaction.user, user_id_str, button_interaction)

                except Exception as e:
                    logging.error(f"Error in continue application button: {e}")
                    import traceback
                    traceback.print_exc()
                    await button_interaction.response.send_message(
                        "❌ An error occurred. Please try again.",
                        ephemeral=True
                    )

        view = ContinueApplicationView(user_id)

        # Send the embed with button
        await interaction.response.send_message(
            embed=embed,
            view=view,
            ephemeral=True
        )

        logging.info(f"Sent continue application embed to user {user_id}")

    except Exception as e:
        logging.error(f"Error sending continue application embed: {e}")
        # Fallback to automatic modal sending
        try:
            await send_next_application_modal(interaction.user, user_id, interaction)
        except Exception as fallback_error:
            logging.error(f"Fallback modal sending also failed: {fallback_error}")
            await interaction.response.send_message(
                "❌ An error occurred. Please restart the application process.",
                ephemeral=True
            )

async def complete_modal_application(user, user_id, interaction):
    """
    Completes the modal-based application process.
    """
    try:
        logging.debug(f"complete_modal_application called for user {user_id}")

        if user_id not in modal_application_state:
            logging.error(f"No modal application state found for user {user_id}")
            return

        state = modal_application_state[user_id]
        application_name = state["application_name"]
        questions = state["questions"]
        answers = state["answers"]

        # Send completion message
        complete_embed = discord.Embed(
            title="Application Submitted",
            description="Thank you for completing your application. Your responses have been recorded and will be reviewed by our staff team.",
            color=0x2ECC71  # Professional green color
        )
        complete_embed.add_field(
            name="Next Steps",
            value="• Your application will be reviewed by our staff team\n• You will be notified of the decision\n• This process typically takes 1-3 days",
            inline=False
        )
        complete_embed.set_footer(text=f"© {datetime.now().year} Application System • Application ID: {user.id}")
        complete_embed.timestamp = datetime.now()

        if interaction:
            await interaction.response.send_message(embed=complete_embed, ephemeral=True)

        # Log the application using the same system as DM applications
        await log_application(user, application_name, questions, answers, application_log_channel)

        # Clean up state
        del modal_application_state[user_id]

        logging.info(f"Modal application completed for user {user_id}: {application_name}")

    except Exception as e:
        logging.error(f"Error completing modal application for user {user_id}: {e}")
        # Clean up state
        if user_id in modal_application_state:
            del modal_application_state[user_id]

class ApplicationModal(FixedApplicationModal):
    """
    Modal for handling application questions with support for chaining.
    Uses FixedApplicationModal to ensure correct component types in payload.
    """
    def __init__(self, user_id, questions, start_index, total_questions, application_name):
        try:
            self.user_id = user_id
            self.questions = questions
            self.start_index = start_index
            self.total_questions = total_questions
            self.application_name = application_name

            # Create title with progress indicator
            if len(questions) == 1:
                progress = f"({start_index + 1} of {total_questions})"
            else:
                progress = f"({start_index + 1}-{start_index + len(questions)} of {total_questions})"

            title = f"{application_name} {progress}"
            if len(title) > 45:  # Discord modal title limit
                title = f"Application {progress}"

            super().__init__(title=title, timeout=300)  # 5 minute timeout

            # Debug: Check modal's own type after initialization
            modal_type = getattr(self, 'type', 'No type attr')
            logging.debug(f"Modal type after initialization: {modal_type}")

            # Ensure modal has correct type if it has a type attribute
            if hasattr(self, 'type'):
                # Modal should not have a type attribute, but if it does, log it
                logging.warning(f"Modal unexpectedly has type attribute: {self.type}")
                # Don't modify it as modals shouldn't have component types

            logging.debug(f"Creating ApplicationModal for user {user_id}: {len(questions)} questions, start_index={start_index}")

            # Debug: Log discord.py version and enum values (with error handling)
            try:
                import discord
                logging.debug(f"Discord.py version: {discord.__version__}")

                if hasattr(discord, 'ComponentType'):
                    logging.debug(f"ComponentType enum available")
                    if hasattr(discord.ComponentType, 'text_input'):
                        text_input_type = discord.ComponentType.text_input
                        text_input_value = getattr(text_input_type, 'value', 'No value attr')
                        logging.debug(f"ComponentType.text_input = {text_input_type} (value={text_input_value})")
                    else:
                        logging.warning(f"ComponentType.text_input not available")
                else:
                    logging.warning(f"ComponentType enum not available")

                if hasattr(discord, 'TextStyle'):
                    logging.debug(f"TextStyle enum available")
                    short_style = discord.TextStyle.short
                    paragraph_style = discord.TextStyle.paragraph
                    short_value = getattr(short_style, 'value', 'No value attr')
                    paragraph_value = getattr(paragraph_style, 'value', 'No value attr')
                    logging.debug(f"TextStyle.short = {short_style} (value={short_value})")
                    logging.debug(f"TextStyle.paragraph = {paragraph_style} (value={paragraph_value})")
                else:
                    logging.warning(f"TextStyle enum not available")
            except Exception as enum_debug_error:
                logging.warning(f"Error debugging discord.py enums (non-critical): {enum_debug_error}")

            # Test TextInput creation to identify type issues (with error handling)
            try:
                test_isolated_textinput_creation()
                test_textinput_creation()
                # Also create a minimal test modal for comparison
                create_minimal_test_modal()
                # Test the fixed modal payload system
                test_fixed_modal_payload()
            except Exception as test_error:
                logging.warning(f"Error during TextInput creation test (non-critical): {test_error}")

            # Validate question count (Discord modal limit is 5 components)
            if len(questions) > 5:
                logging.error(f"Too many questions for modal: {len(questions)} (max 5)")
                questions = questions[:5]  # Truncate to 5 questions
                logging.warning(f"Truncated questions to 5 for modal compatibility")

            # Add text inputs for each question (max 5)
            for i, question in enumerate(questions):
                # Calculate question number first (needed for logging)
                question_num = start_index + i + 1

                # Truncate question if too long for label
                label = question[:45] if len(question) <= 45 else question[:42] + "..."

                # Use paragraph style for longer responses or if question suggests detailed answer
                style = discord.TextStyle.paragraph if len(question) > 50 or any(word in question.lower() for word in ['describe', 'explain', 'detail', 'why', 'how']) else discord.TextStyle.short

                # Debug: Check TextStyle values to ensure they're not being confused with component types
                try:
                    style_value = getattr(style, 'value', style)
                    logging.debug(f"Question {question_num}: Using TextStyle {style} (value={style_value})")

                    # Validate TextStyle values
                    if style_value not in [1, 2]:  # TextStyle.short=1, TextStyle.paragraph=2
                        logging.error(f"Invalid TextStyle value {style_value} for question {question_num}")
                        style = discord.TextStyle.short  # Fallback to short
                        logging.warning(f"Falling back to TextStyle.short for question {question_num}")
                except Exception as style_debug_error:
                    logging.warning(f"Error during TextStyle debugging for question {question_num} (non-critical): {style_debug_error}")
                    # Ensure we have a valid style even if debugging fails
                    style = discord.TextStyle.short

                # Create more descriptive placeholder
                if style == discord.TextStyle.paragraph:
                    placeholder = f"Please provide a detailed answer to question {question_num}..."
                else:
                    placeholder = f"Your answer to question {question_num}..."

                # Validate style before creating TextInput
                if style not in [discord.TextStyle.short, discord.TextStyle.paragraph]:
                    logging.warning(f"Invalid TextStyle {style} for question {question_num}, defaulting to short")
                    style = discord.TextStyle.short

                # Create TextInput with standard discord.ui.TextInput
                text_input = discord.ui.TextInput(
                    label=label,
                    placeholder=placeholder,
                    style=style,
                    required=True,
                    max_length=1000,
                    min_length=1
                )

                # Verify and potentially fix the component type after creation
                expected_type_value = 4  # TextInput components should always be type 4
                actual_type = getattr(text_input, 'type', None)

                if actual_type is not None:
                    actual_type_value = getattr(actual_type, 'value', actual_type)
                    if actual_type_value != expected_type_value:
                        logging.warning(f"TextInput component created with unexpected type: {actual_type_value}, expected: {expected_type_value}")

                        # Try to manually set the correct type if possible
                        if hasattr(discord, 'ComponentType') and hasattr(discord.ComponentType, 'text_input'):
                            try:
                                text_input.type = discord.ComponentType.text_input
                                logging.info(f"Manually corrected TextInput component type to ComponentType.text_input")
                            except Exception as type_fix_error:
                                logging.error(f"Could not manually fix component type: {type_fix_error}")
                        else:
                            # Fallback: try setting integer value directly
                            try:
                                text_input.type = 4
                                logging.info(f"Manually corrected TextInput component type to integer value 4")
                            except Exception as type_fix_error:
                                logging.error(f"Could not manually fix component type with integer: {type_fix_error}")
                else:
                    logging.error(f"TextInput component has no type attribute after creation!")

                # Validate the created TextInput component using helper function (with error handling)
                try:
                    component_type = getattr(text_input, 'type', None)

                    if component_type is None or not is_valid_text_input_component_type(component_type):
                        logging.error(f"Invalid TextInput component type: {get_component_type_info(component_type)}")
                        # Recreate with explicit short style as fallback
                        text_input = discord.ui.TextInput(
                            label=label,
                            placeholder=placeholder,
                            style=discord.TextStyle.short,
                            required=True,
                            max_length=1000,
                            min_length=1
                        )
                        logging.warning(f"Recreated TextInput component with fallback configuration")
                except Exception as validation_error:
                    logging.warning(f"Error during component validation for question {question_num} (non-critical): {validation_error}")
                    # Component is already created, continue with it

                self.add_item(text_input)

                # Debug logging with error handling
                try:
                    component_type = getattr(text_input, 'type', 'None')
                    logging.debug(f"Added TextInput for question {question_num}: type={get_component_type_info(component_type)}, style={style}")
                except Exception as debug_error:
                    logging.warning(f"Error during component debug logging (non-critical): {debug_error}")

            # Final validation of the constructed modal (with error handling)
            try:
                logging.debug(f"ApplicationModal construction complete: {len(self.children)} components added")
                for i, child in enumerate(self.children):
                    component_type = getattr(child, 'type', 'None')
                    logging.debug(f"Component {i}: type={get_component_type_info(component_type)}, label={getattr(child, 'label', 'None')}")
            except Exception as final_debug_error:
                logging.warning(f"Error during final modal debug logging (non-critical): {final_debug_error}")

        except Exception as modal_construction_error:
            logging.error(f"Critical error during ApplicationModal construction: {modal_construction_error}")
            # Try to create a minimal fallback modal
            try:
                super().__init__(title="Application Form", timeout=300)
                # Add a single basic text input as fallback
                fallback_input = discord.ui.TextInput(
                    label="Application Response",
                    placeholder="Please provide your application details...",
                    style=discord.TextStyle.paragraph,
                    required=True,
                    max_length=2000
                )
                self.add_item(fallback_input)
                logging.info("Created fallback ApplicationModal with single text input")
            except Exception as fallback_error:
                logging.error(f"Failed to create fallback modal: {fallback_error}")
                # Re-raise the original error if fallback also fails
                raise modal_construction_error

    async def on_submit(self, interaction: discord.Interaction):
        try:
            user_id = self.user_id

            if user_id not in modal_application_state:
                await interaction.response.send_message(
                    "❌ Application session expired. Please restart the application process.",
                    ephemeral=True
                )
                return

            state = modal_application_state[user_id]

            # Store the answers from this modal
            for i, item in enumerate(self.children):
                if isinstance(item, discord.ui.TextInput):
                    state["answers"].append(item.value)

            # Update the current question index
            state["current_question_index"] += len(self.questions)

            # Check if there are more questions
            current_index = state["current_question_index"]
            total_questions = state["total_questions"]

            logging.debug(f"Modal submission: user {user_id}, current_index: {current_index}, total: {total_questions}")

            if current_index < total_questions:
                # More questions remaining - send continue embed
                logging.debug(f"More questions remaining ({total_questions - current_index}), sending continue embed")
                await send_continue_application_embed(interaction, user_id, state)
            else:
                # All questions completed - finish application
                logging.debug(f"All questions completed, finishing application")
                await complete_modal_application(interaction.user, user_id, interaction)

        except Exception as e:
            logging.error(f"Error in ApplicationModal.on_submit for user {self.user_id}: {e}")
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        "❌ An error occurred while processing your application. Please try again.",
                        ephemeral=True
                    )
                else:
                    await interaction.followup.send(
                        "❌ An error occurred while processing your application. Please try again.",
                        ephemeral=True
                    )
            except:
                pass  # Fail silently if we can't send error message
            # Clean up state
            if self.user_id in modal_application_state:
                del modal_application_state[self.user_id]

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        logging.error(f"Error in ApplicationModal for user {self.user_id}: {error}")
        try:
            await interaction.response.send_message(
                "❌ An error occurred while processing your application. Please try again.",
                ephemeral=True
            )
        except:
            pass
        # Clean up state
        if self.user_id in modal_application_state:
            del modal_application_state[self.user_id]

        # Log the application
async def log_application(user, application_name, questions, answers, application_log_channel):
    """
    Logs the application details in the specified log channel with a professional format.
    """
    try:
        if not application_log_channel:
            logging.error("Application log channel not set.")
            return

        log_channel = bot.get_channel(application_log_channel)
        if not log_channel:
            logging.error(f"Log channel with ID {application_log_channel} not found.")
            return

        submission_time = datetime.now(timezone.utc)
        user_id_str = str(user.id)

        # Create a professional embed for the application log
        embed = discord.Embed(
            title=f"New Application Submission",
            description=f"A new application has been submitted and requires review.",
            color=0x2b2d31  # Professional dark theme color
        )

        # Add applicant information section
        embed.add_field(
            name="Applicant Information",
            value=f"**User:** {user.mention} ({user.name}#{user.discriminator})\n"
                  f"**User ID:** {user.id}\n"
                  f"**Application Type:** {application_name}\n"
                  f"**Submitted:** {submission_time.strftime('%Y-%m-%d %H:%M:%S')} UTC",
            inline=False
        )

        # Add a divider
        embed.add_field(name="", value="━━━━━━━━━━━━━━━━━━━━━━━", inline=False)

        # Add application responses section
        embed.add_field(
            name="Application Responses",
            value="Below are the questions and answers provided by the applicant:",
            inline=False
        )

        # Calculate total fields: 5 fixed fields + (2 × number of questions)
        # Discord limit is 25 fields, so we need to condense if we have more than 10 questions
        total_questions = len(questions)
        max_questions_for_separate_fields = 10  # 5 fixed + (2 × 10) = 25 fields

        if total_questions <= max_questions_for_separate_fields:
            # Use separate fields for each question/answer pair (original format)
            for i, (question, answer) in enumerate(zip(questions, answers)):
                # Add question with number
                embed.add_field(
                    name=f"Question {i+1}",
                    value=f"{question}",
                    inline=False
                )

                # Add answer with proper formatting
                # Truncate very long answers to prevent embed issues
                if len(answer) > 1000:
                    answer_display = answer[:997] + "..."
                else:
                    answer_display = answer

                embed.add_field(
                    name=f"Answer {i+1}",
                    value=f"```{answer_display}```",
                    inline=False
                )
        else:
            # Condense multiple Q&A pairs into fewer fields to stay within Discord's 25-field limit
            # Group questions into batches that fit within field value limits (1024 characters)
            questions_per_field = 3  # Conservative estimate to avoid hitting character limits

            for batch_start in range(0, total_questions, questions_per_field):
                batch_end = min(batch_start + questions_per_field, total_questions)
                batch_questions = []

                for i in range(batch_start, batch_end):
                    question = questions[i]
                    answer = answers[i]

                    # Truncate long answers for condensed view
                    if len(answer) > 200:
                        answer_display = answer[:197] + "..."
                    else:
                        answer_display = answer

                    # Format as Q&A pair
                    qa_text = f"**Q{i+1}:** {question}\n**A{i+1}:** {answer_display}"
                    batch_questions.append(qa_text)

                # Combine batch into single field
                batch_content = "\n\n".join(batch_questions)

                # Ensure field content doesn't exceed Discord's 1024 character limit
                if len(batch_content) > 1020:
                    batch_content = batch_content[:1017] + "..."

                # Determine field name based on batch
                if batch_start == 0 and batch_end >= total_questions:
                    field_name = f"Questions & Answers (1-{total_questions})"
                else:
                    field_name = f"Questions & Answers ({batch_start + 1}-{batch_end})"

                embed.add_field(
                    name=field_name,
                    value=batch_content,
                    inline=False
                )

        # Add a divider before actions section
        embed.add_field(name="", value="━━━━━━━━━━━━━━━━━━━━━━━", inline=False)

        # Add instructions for staff
        embed.add_field(
            name="Staff Actions",
            value="Please review this application and select an appropriate action below.",
            inline=False
        )

        # Set footer with application ID and timestamp
        embed.set_footer(text=f"Application ID: {user.id} • Submitted: {submission_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")
        embed.timestamp = submission_time

        # Add user avatar if available
        if user.avatar:
            embed.set_thumbnail(url=user.avatar.url)

        # Create professional buttons for response with persistent custom IDs
        view = View(timeout=None)  # Set timeout to None for persistent view

        # Create buttons with custom_ids to make them persistent
        accept_button = Button(
            label="Approve Application",
            style=discord.ButtonStyle.success,
            custom_id=f"app_accept_{user_id_str}"
        )

        reject_button = Button(
            label="Decline Application",
            style=discord.ButtonStyle.danger,
            custom_id=f"app_reject_{user_id_str}"
        )

        accept_reason_button = Button(
            label="Approve with Feedback",
            style=discord.ButtonStyle.primary,
            custom_id=f"app_accept_reason_{user_id_str}"
        )

        reject_reason_button = Button(
            label="Decline with Feedback",
            style=discord.ButtonStyle.secondary,
            custom_id=f"app_reject_reason_{user_id_str}"
        )

        # Add Create Ticket button for staff follow-up
        create_ticket_button = Button(
            label="Create Ticket",
            style=discord.ButtonStyle.primary,
            custom_id=f"app_create_ticket_{user_id_str}",
            emoji="🎫"
        )

        # Define callbacks for the buttons with administrator permission checks
        async def accept_callback(interaction):
            # Check if user has administrator permissions
            if not interaction.user.guild_permissions.administrator:
                await interaction.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                return
            await handle_application_response(interaction, user, "accepted", application_name)

        async def reject_callback(interaction):
            # Check if user has administrator permissions
            if not interaction.user.guild_permissions.administrator:
                await interaction.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                return
            await handle_application_response(interaction, user, "rejected", application_name)

        async def accept_with_reason_callback(interaction):
            # Check if user has administrator permissions
            if not interaction.user.guild_permissions.administrator:
                await interaction.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                return
            if applications_status.get(user_id_str, {}).get("responded"):
                await interaction.response.send_message("This application has already been processed.", ephemeral=True)
                return
            await interaction.response.send_modal(AcceptReasonModal(user, application_name))

        async def reject_with_reason_callback(interaction):
            # Check if user has administrator permissions
            if not interaction.user.guild_permissions.administrator:
                await interaction.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                return
            if applications_status.get(user_id_str, {}).get("responded"):
                await interaction.response.send_message("This application has already been processed.", ephemeral=True)
                return
            await interaction.response.send_modal(RejectReasonModal(user, application_name))

        # Assign callbacks to buttons (Create Ticket handled in main on_interaction event)
        accept_button.callback = accept_callback
        reject_button.callback = reject_callback
        accept_reason_button.callback = accept_with_reason_callback
        reject_reason_button.callback = reject_with_reason_callback
        # Note: create_ticket_button callback is handled in the main on_interaction event

        # Add buttons to view
        view.add_item(accept_button)
        view.add_item(reject_button)
        view.add_item(accept_reason_button)
        view.add_item(reject_reason_button)
        view.add_item(create_ticket_button)

        # Send the embed with buttons to the log channel
        log_message = await log_channel.send(embed=embed, view=view)

        # Store the message ID with the application status
        applications_status[user_id_str] = {
            "responded": False,
            "message_id": log_message.id,
            "application_name": application_name,
            "submission_time": submission_time.isoformat(),
            "questions": questions,
            "answers": answers
        }
        await save_data()
        logging.info(f"Application logged for user {user_id_str} with message ID {log_message.id}")

    except Exception as e:
        logging.error(f"Error in log_application: {e}")
        error_embed = discord.Embed(
            title="Application Error",
            description="An error occurred while processing your application. Our staff has been notified of this issue.",
            color=0xE74C3C  # Professional red color
        )
        error_embed.set_footer(text="Application System • Error")

        # Try to send error message to user, but handle DM failures gracefully
        try:
            await user.send(embed=error_embed)
        except discord.errors.Forbidden:
            logging.warning(f"Could not send error DM to user {user.id} - DMs may be disabled")
        except Exception as dm_error:
            logging.error(f"Error sending error DM to user {user.id}: {dm_error}")

class AcceptReasonModal(discord.ui.Modal):
    def __init__(self, user, application_name):
        super().__init__(title="Approve Application with Feedback")
        self.user = user
        self.application_name = application_name

        # Add professional text inputs
        self.add_item(discord.ui.TextInput(
            label="Feedback for Applicant",
            placeholder="Provide constructive feedback for the applicant...",
            style=discord.TextStyle.paragraph,
            min_length=10,
            max_length=1000,
            required=True
        ))

        # Add optional internal notes field
        self.add_item(discord.ui.TextInput(
            label="Internal Notes (Staff Only)",
            placeholder="Optional notes for staff records (not sent to applicant)",
            style=discord.TextStyle.paragraph,
            required=False
        ))

    async def on_submit(self, interaction):
        try:
            feedback = self.children[0].value
            internal_notes = self.children[1].value if len(self.children) > 1 else ""
            user_id_str = str(self.user.id)
            current_time = datetime.now(timezone.utc)

            # Check if application has already been responded to
            if applications_status.get(user_id_str, {}).get("responded"):
                try:
                    await interaction.response.send_message("This application has already been processed.", ephemeral=True)
                except discord.errors.InteractionResponded:
                    await interaction.followup.send("This application has already been processed.", ephemeral=True)
                return

            # Update application status with detailed information
            applications_status[user_id_str] = {
                "responded": True,
                "admin": interaction.user.id,
                "admin_name": f"{interaction.user.name}#{interaction.user.discriminator}",
                "status": "accepted",
                "feedback": feedback,
                "internal_notes": internal_notes,
                "response_time": current_time.isoformat()
            }
            await save_data()

            # Send professional response to admin
            admin_embed = discord.Embed(
                title="Application Approved",
                description=f"You have approved the application for {self.user.mention}.",
                color=0x2ECC71  # Professional green color
            )
            admin_embed.add_field(name="Application Type", value=self.application_name, inline=False)
            admin_embed.add_field(name="Feedback Provided", value=feedback, inline=False)
            if internal_notes:
                admin_embed.add_field(name="Internal Notes", value=internal_notes, inline=False)
            admin_embed.set_footer(text=f"Processed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")

            try:
                await interaction.response.send_message(embed=admin_embed, ephemeral=True)
            except discord.errors.InteractionResponded:
                await interaction.followup.send(embed=admin_embed, ephemeral=True)

            # Send professional notification to applicant
            try:
                user_embed = discord.Embed(
                    title="Application Approved",
                    description=f"Congratulations! Your application for **{self.application_name}** has been approved.",
                    color=0x2ECC71  # Professional green color
                )

                # Add feedback section
                user_embed.add_field(
                    name="Feedback from Staff",
                    value=feedback,
                    inline=False
                )

                # Add next steps section
                user_embed.add_field(
                    name="Next Steps",
                    value="• Please read all feedback carefully\n• Follow any instructions provided\n• Contact staff if you have any questions",
                    inline=False
                )

                # Add footer with timestamp
                user_embed.set_footer(text=f"© {datetime.now().year} Application System")
                user_embed.timestamp = current_time

                # Send notification using fallback system
                notification_sent = await send_application_notification(self.user, user_embed, self.application_name, "accepted")
                if notification_sent:
                    logging.info(f"Approval notification sent to user {self.user.id} for {self.application_name} application")
                else:
                    logging.warning(f"Failed to send approval notification to user {self.user.id} for {self.application_name} application")

                # Send application response notification to configured channel
                response_notification_sent = await send_application_response_notification(
                    self.user, self.application_name, "accepted", interaction.user, feedback=feedback
                )
                if response_notification_sent:
                    logging.info(f"Application response notification sent for {self.user.name} ({self.application_name} - accepted)")
                else:
                    logging.debug(f"Application response notification not sent (channel not configured or error occurred)")

                logging.info(f"Application for user {user_id_str} approved with feedback by {interaction.user.id}")

            except Exception as user_notification_error:
                logging.error(f"Error sending user notification: {user_notification_error}")

        except discord.errors.InteractionResponded:
            logging.warning("Interaction was already responded to in AcceptReasonModal")
            try:
                await interaction.followup.send("Processing your response...", ephemeral=True)
            except:
                pass
        except Exception as e:
            logging.error(f"Error in AcceptReasonModal.on_submit: {e}")
            try:
                await interaction.response.send_message("An error occurred while processing your response.", ephemeral=True)
            except discord.errors.InteractionResponded:
                try:
                    await interaction.followup.send("An error occurred while processing your response.", ephemeral=True)
                except:
                    pass

class RejectReasonModal(discord.ui.Modal):
    def __init__(self, user, application_name):
        super().__init__(title="Decline Application with Feedback")
        self.user = user
        self.application_name = application_name

        # Add professional text inputs
        self.add_item(discord.ui.TextInput(
            label="Feedback for Applicant",
            placeholder="Provide constructive feedback explaining the decision...",
            style=discord.TextStyle.paragraph,
            min_length=10,
            max_length=1000,
            required=True
        ))

        # Add optional improvement suggestions field
        self.add_item(discord.ui.TextInput(
            label="Improvement Suggestions",
            placeholder="Suggestions for how the applicant could improve for future applications",
            style=discord.TextStyle.paragraph,
            required=False
        ))

        # Add optional internal notes field
        self.add_item(discord.ui.TextInput(
            label="Internal Notes (Staff Only)",
            placeholder="Optional notes for staff records (not sent to applicant)",
            style=discord.TextStyle.paragraph,
            required=False
        ))

    async def on_submit(self, interaction):
        try:
            feedback = self.children[0].value
            improvement = self.children[1].value if len(self.children) > 1 else ""
            internal_notes = self.children[2].value if len(self.children) > 2 else ""
            user_id_str = str(self.user.id)
            current_time = datetime.now(timezone.utc)

            # Check if application has already been responded to
            if applications_status.get(user_id_str, {}).get("responded"):
                try:
                    await interaction.response.send_message("This application has already been processed.", ephemeral=True)
                except discord.errors.InteractionResponded:
                    await interaction.followup.send("This application has already been processed.", ephemeral=True)
                return

            # Update application status with detailed information
            applications_status[user_id_str] = {
                "responded": True,
                "admin": interaction.user.id,
                "admin_name": f"{interaction.user.name}#{interaction.user.discriminator}",
                "status": "rejected",
                "feedback": feedback,
                "improvement": improvement,
                "internal_notes": internal_notes,
                "response_time": current_time.isoformat()
            }
            await save_data()

            # Send professional response to admin
            admin_embed = discord.Embed(
                title="Application Declined",
                description=f"You have declined the application for {self.user.mention}.",
                color=0x95A5A6  # Professional gray color
            )
            admin_embed.add_field(name="Application Type", value=self.application_name, inline=False)
            admin_embed.add_field(name="Feedback Provided", value=feedback, inline=False)
            if improvement:
                admin_embed.add_field(name="Improvement Suggestions", value=improvement, inline=False)
            if internal_notes:
                admin_embed.add_field(name="Internal Notes", value=internal_notes, inline=False)
            admin_embed.set_footer(text=f"Processed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")

            try:
                await interaction.response.send_message(embed=admin_embed, ephemeral=True)
            except discord.errors.InteractionResponded:
                await interaction.followup.send(embed=admin_embed, ephemeral=True)

            # Send professional notification to applicant
            try:
                user_embed = discord.Embed(
                    title="Application Status Update",
                    description=f"Thank you for your interest in the **{self.application_name}** position. After careful review, we regret to inform you that your application has not been approved at this time.",
                    color=0x95A5A6  # Professional gray color
                )

                # Add feedback section
                user_embed.add_field(
                    name="Feedback from Our Team",
                    value=feedback,
                    inline=False
                )

                # Add improvement suggestions if provided
                if improvement:
                    user_embed.add_field(
                        name="Suggestions for Improvement",
                        value=improvement,
                        inline=False
                    )

                # Add encouragement section
                user_embed.add_field(
                    name="Next Steps",
                    value="• We encourage you to review the feedback provided\n• Consider applying again in the future\n• Feel free to reach out if you have any questions",
                    inline=False
                )

                # Add footer with timestamp
                user_embed.set_footer(text=f"© {datetime.now().year} Application System")
                user_embed.timestamp = current_time

                # Send notification using fallback system
                notification_sent = await send_application_notification(self.user, user_embed, self.application_name, "declined")
                if notification_sent:
                    logging.info(f"Decline notification sent to user {self.user.id} for {self.application_name} application")
                else:
                    logging.warning(f"Failed to send decline notification to user {self.user.id} for {self.application_name} application")

                # Send application response notification to configured channel
                response_notification_sent = await send_application_response_notification(
                    self.user, self.application_name, "rejected", interaction.user, feedback=feedback, reason=improvement
                )
                if response_notification_sent:
                    logging.info(f"Application response notification sent for {self.user.name} ({self.application_name} - rejected)")
                else:
                    logging.debug(f"Application response notification not sent (channel not configured or error occurred)")

                logging.info(f"Application for user {user_id_str} declined with feedback by {interaction.user.id}")

            except Exception as user_notification_error:
                logging.error(f"Error sending user notification: {user_notification_error}")

        except discord.errors.InteractionResponded:
            logging.warning("Interaction was already responded to in RejectReasonModal")
            try:
                await interaction.followup.send("Processing your response...", ephemeral=True)
            except:
                pass
        except Exception as e:
            logging.error(f"Error in RejectReasonModal.on_submit: {e}")
            try:
                await interaction.response.send_message("An error occurred while processing your response.", ephemeral=True)
            except discord.errors.InteractionResponded:
                try:
                    await interaction.followup.send("An error occurred while processing your response.", ephemeral=True)
                except:
                    pass
# Welcome Message System
@tree.command(name="set_welcome_channel", description="Set the welcome channel, message, and image/GIF")
@app_commands.default_permissions(administrator=True)
async def set_welcome_channel(interaction: discord.Interaction, channel: discord.TextChannel, message: str, image_url: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    global welcome_channel_id, welcome_message, welcome_image_url
    welcome_channel_id = channel.id
    welcome_message = message
    welcome_image_url = image_url
    await save_data()  # Save data after setting the welcome channel
    await interaction.response.send_message(f"Welcome channel set to {channel.mention} with message: {message}")

# This event handler has been consolidated with the main one defined later in the file





async def restore_ticket_panel():
    """Restore ticket panel with rate limit handling"""
    try:
        if not ticket_config["ticket_channel"]:
            return

        channel = bot.get_channel(ticket_config["ticket_channel"])
        if not channel:
            logging.warning("Ticket channel not found during panel restoration")
            return

        logging.info("Starting ticket panel restoration...")

        # Clear existing messages with rate limit handling
        async for message in channel.history(limit=100):
            if message.author == bot.bot:
                await rate_limiter.execute(
                    'delete_message',
                    message.delete
                )

        # Create new panel
        # TODO: Replace with create_modern_ticket_panel(channel)
        logging.info("Ticket panel restored successfully")

    except Exception as e:
        log_error_to_console(e, "ticket panel restoration")

async def restore_reaction_roles():
    """Restore reaction roles panel on bot startup with enhanced validation"""
    global reaction_message_id, reaction_channel_id
    try:
        logging.info("Starting reaction roles panel restoration...")

        # Enhanced validation with detailed logging
        config_issues = []

        if not reaction_channel_id:
            config_issues.append("reaction_channel_id is not set")
        if not reaction_message_id:
            config_issues.append("reaction_message_id is not set")

        # Check if reaction_roles data exists and is valid
        if not isinstance(reaction_roles, dict):
            config_issues.append("reaction_roles is not a valid dictionary")
        elif not reaction_roles.get("roles"):
            config_issues.append("no reaction roles configured")

        if config_issues:
            if len(config_issues) == len(["reaction_channel_id", "reaction_message_id", "reaction_roles"]):
                logging.info("Reaction roles system is intentionally not configured - skipping restoration")
            else:
                logging.warning(f"Reaction roles configuration issues detected: {', '.join(config_issues)}")
                logging.debug(f"Current values - channel_id: {reaction_channel_id}, message_id: {reaction_message_id}, roles: {type(reaction_roles)}")
            return False

        channel = bot.get_channel(reaction_channel_id)
        if not channel:
            logging.error(f"Reaction role channel not found: {reaction_channel_id}")
            return False

        logging.info(f"Found reaction role channel: {channel.name}")

        # Try to get the existing message
        try:
            message = await channel.fetch_message(reaction_message_id)
            logging.info(f"Found existing reaction role message: {message.id}")

            # Clear existing reactions
            await message.clear_reactions()
            logging.info("Cleared existing reactions")
        except discord.NotFound:
            logging.warning(f"Reaction role message {reaction_message_id} not found, creating a new one")

            # Create a new message if the old one doesn't exist
            embed = discord.Embed(
                title="Reaction Roles",
                description="React to get roles!",
                color=discord.Color.blue()
            )
            message = await channel.send(embed=embed)

            # Update the message ID
            reaction_message_id = message.id

            # Save the updated message ID
            await save_data()
            logging.info(f"Created new reaction role message with ID: {message.id}")

        # Make sure reaction_roles has the correct structure
        if not isinstance(reaction_roles, dict) or "roles" not in reaction_roles:
            logging.error(f"Invalid reaction_roles structure: {reaction_roles}")
            reaction_roles["roles"] = {}
            reaction_roles["config"] = {"allow_multiple": True}

        # Update the embed with role information
        embed = discord.Embed(
            title="Reaction Roles",
            description="React with the emojis below to get or remove roles!",
            color=discord.Color.blue()
        )

        # Add fields for each role and add reactions with improved error handling
        successful_emojis = []
        skipped_emojis = []

        for emoji_str, role_id in reaction_roles["roles"].items():
            try:
                # Convert role_id to int if it's a string
                if isinstance(role_id, str) and role_id.isdigit():
                    role_id = int(role_id)

                role = channel.guild.get_role(role_id)
                if role:
                    # Try to add the reaction safely
                    success, error_msg = await safe_add_reaction_with_skip(message, emoji_str, "reaction role restoration")
                    if success:
                        logging.info(f"Adding reaction role: {emoji_str} -> {role.name}")
                        embed.add_field(name=f"{emoji_str} {role.name}", value="React to get this role!", inline=False)
                        successful_emojis.append(emoji_str)
                    else:
                        # Skip this emoji but continue with others
                        skipped_emojis.append((emoji_str, role.name, error_msg))
                        logging.warning(f"Skipped emoji {emoji_str} for role {role.name}: {error_msg}")
                else:
                    logging.warning(f"Role not found for ID: {role_id}")
                    skipped_emojis.append((emoji_str, f"Role ID {role_id}", "Role not found"))
            except Exception as e:
                logging.error(f"Unexpected error processing emoji {emoji_str}: {e}")
                skipped_emojis.append((emoji_str, "Unknown", str(e)))

        # Log summary of results
        if successful_emojis:
            console_log(f"Restored {len(successful_emojis)} reaction roles successfully", "SUCCESS")
        if skipped_emojis:
            console_log(f"Skipped {len(skipped_emojis)} problematic emojis during restoration", "WARNING")

        # Update the message with the new embed
        await message.edit(embed=embed)
        logging.info("Reaction roles panel restored successfully")
        return True

    except Exception as e:
        logging.error(f"Error restoring reaction roles panel: {e}")
        import traceback
        traceback.print_exc()
        return False


# NOTE: Duplicate on_message handler removed - using optimized version below

# Handle Tebex webhook messages (moved to optimized handler)
# This section was moved to the unified on_message handler below
# NOTE: This duplicate handler content was moved to the optimized on_message handler below
# NOTE: End of removed duplicate handler


# Sticky Message System
@tree.command(name="set_sticky_message", description="Set a sticky message in a channel")
@app_commands.default_permissions(administrator=True)
async def set_sticky_message(interaction: discord.Interaction, channel: discord.TextChannel, message: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        global sticky_messages
        sticky_messages[channel.id] = message

        # Delete any existing sticky messages
        async for msg in channel.history(limit=10):
            if msg.author == bot.user and msg.content == message:
                try:
                    await msg.delete()
                    await asyncio.sleep(0.5)  # Add a small delay between operations
                except Exception as e:
                    print(f"Error deleting existing sticky message: {e}")

        # Post the new sticky message immediately
        try:
            await channel.send(message)
            await interaction.response.send_message(f"Sticky message set in {channel.mention}: {message}")
        except Exception as e:
            print(f"Error setting sticky message: {e}")
            await interaction.response.send_message("Failed to set sticky message. Please try again.")
    except Exception as e:
        print(f"Error in set_sticky_message: {e}")
        await interaction.response.send_message("An error occurred while setting the sticky message.")

# NOTE: Second duplicate on_message handler removed - using optimized version below

# NOTE: End of second duplicate handler removal


@tree.command(name="remove_sticky_message", description="Remove the sticky message from a channel")
@app_commands.default_permissions(administrator=True)
async def remove_sticky_message(interaction: discord.Interaction, channel: discord.TextChannel):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    print("Remove sticky message command invoked")  # Debugging output
    global sticky_messages
    if channel.id in sticky_messages:
        del sticky_messages[channel.id]
        await interaction.response.send_message(f"Sticky message removed from {channel.mention}.")
    else:
        await interaction.response.send_message(f"No sticky message found in {channel.mention}.")

# Load or initialize JSON files
def load_json(file_path):
    try:
        if not os.path.exists(file_path):
            # Create file with empty dictionary if it doesn't exist
            with open(file_path, 'w') as f:
                json.dump({}, f)
            return {}

        with open(file_path, 'r') as f:
            content = f.read()
            if not content:  # If file is empty
                return {}
            return json.loads(content)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return {}

def save_json(file_path, data):
    try:
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=4)
        return True
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        return False


# Log purchase events from webhook




# Reaction Role System
@tree.command(name="setup_reaction_roles", description="Set up reaction roles with a custom message")
@app_commands.default_permissions(administrator=True)
async def setup_reaction_roles(
    interaction: discord.Interaction,
    channel: discord.TextChannel,
    message: str,
    allow_multiple: bool = True
):
    if not log_permission_check(interaction, "setup_reaction_roles"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    """Set up reaction roles in a channel with a custom message"""
    global reaction_message_id, reaction_channel_id, reaction_roles

    try:
        # Create and send the initial embed
        embed = discord.Embed(
            title="Reaction Roles",
            description=message,
            color=discord.Color.blue()
        )
        msg = await channel.send(embed=embed)

        # Update global variables
        reaction_message_id = msg.id
        reaction_channel_id = channel.id

        logging.info(f"Created new reaction roles message with ID: {msg.id} in channel: {channel.id}")

        # Create a clean reaction_roles structure
        reaction_roles = {
            "config": {
                "allow_multiple": allow_multiple
            },
            "roles": {}
        }

        # Save the reaction roles data
        await save_reaction_roles_data()

        # Send instructions
        await interaction.response.send_message(
            "Now, let's add roles. Send messages in this format:\n"
            "`emoji @role`\n"
            "Example: 🎮 @Gamer\n"
            "Type 'done' when finished."
        )

        def message_check(m):
            return m.author == interaction.user and m.channel == interaction.channel and (m.content.lower() == 'done' or (len(m.content.split()) == 2 and len(m.role_mentions) > 0))

        # Wait for role additions
        while True:
            try:
                response = await bot.wait_for('message', timeout=300.0, check=message_check)

                if response.content.lower() == 'done':
                    # Final save before completing
                    await save_reaction_roles_data()
                    await interaction.followup.send("Reaction role setup complete!")
                    break

                # Parse emoji and role from response
                parts = response.content.split()
                if len(parts) != 2:
                    await interaction.followup.send("Invalid format. Please use: `emoji @role`")
                    continue

                emoji = parts[0]
                role_id = int(parts[1].strip('<@&>'))
                role = interaction.guild.get_role(role_id)

                if not role:
                    await interaction.followup.send("Invalid role. Please try again.")
                    continue

                # Try to add the reaction safely first
                success, error_msg = await safe_add_reaction_with_skip(msg, emoji, "reaction role setup")
                if success:
                    # Update embed with new role
                    embed.add_field(name=f"{emoji} {role.name}", value="React to get this role!", inline=False)
                    await msg.edit(embed=embed)

                    # Add to reaction_roles
                    reaction_roles["roles"][emoji] = role.id

                    logging.info(f"Adding reaction role: emoji={emoji}, role={role.name} (ID: {role.id})")

                    # Save after each role is added
                    await save_reaction_roles_data()

                    await response.add_reaction('✅')
                else:
                    # Emoji failed - inform user and skip
                    await interaction.followup.send(f"⚠️ Cannot use emoji {emoji}: {error_msg}\nPlease try a different emoji.")
                    continue

            except asyncio.TimeoutError:
                # Final save before timing out
                await save_reaction_roles_data()
                await interaction.followup.send("Setup timed out. The message has been created with any roles that were added.")
                break
            except Exception as e:
                await interaction.followup.send(f"Error adding role: {str(e)}")
                continue

    except discord.Forbidden:
        await interaction.response.send_message("I don't have the required permissions to set up reaction roles.", ephemeral=True)
    except Exception as e:
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)


@tree.command(name="remove_reaction_role", description="Remove a specific reaction role by emoji")
@app_commands.default_permissions(administrator=True)
async def remove_reaction_role(interaction: discord.Interaction, emoji: str):
    """Remove a specific reaction role by emoji"""
    if not log_permission_check(interaction, "remove_reaction_role"):
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return

    global reaction_roles, reaction_message_id, reaction_channel_id

    try:
        # Check if reaction roles system is set up
        if not reaction_roles or "roles" not in reaction_roles:
            await interaction.response.send_message("❌ No reaction roles system is currently set up. Use `/setup_reaction_roles` first.", ephemeral=True)
            return

        # Check if the emoji exists in reaction roles
        if emoji not in reaction_roles["roles"]:
            await interaction.response.send_message(f"❌ Emoji {emoji} is not currently assigned to any role.", ephemeral=True)
            return

        # Get role information before removing
        role_id = reaction_roles["roles"][emoji]
        if isinstance(role_id, str) and role_id.isdigit():
            role_id = int(role_id)

        role = interaction.guild.get_role(role_id)
        role_name = role.name if role else f"Role ID {role_id}"

        # Remove the emoji from reaction_roles
        del reaction_roles["roles"][emoji]

        # Update the reaction role message if it exists
        if reaction_channel_id and reaction_message_id:
            try:
                channel = bot.get_channel(reaction_channel_id)
                if channel:
                    message = await channel.fetch_message(reaction_message_id)
                    if message:
                        # Remove the reaction from the message
                        try:
                            await message.clear_reaction(emoji)
                            logging.info(f"Removed reaction {emoji} from message {reaction_message_id}")
                        except discord.NotFound:
                            logging.warning(f"Reaction {emoji} not found on message {reaction_message_id}")
                        except Exception as e:
                            logging.warning(f"Could not remove reaction {emoji}: {e}")

                        # Update the embed
                        embed = discord.Embed(
                            title="Reaction Roles",
                            description="React with the emojis below to get or remove roles!",
                            color=discord.Color.blue()
                        )

                        # Add fields for remaining roles
                        for remaining_emoji, remaining_role_id in reaction_roles["roles"].items():
                            if isinstance(remaining_role_id, str) and remaining_role_id.isdigit():
                                remaining_role_id = int(remaining_role_id)

                            remaining_role = interaction.guild.get_role(remaining_role_id)
                            if remaining_role:
                                embed.add_field(
                                    name=f"{remaining_emoji} {remaining_role.name}",
                                    value="React to get this role!",
                                    inline=False
                                )

                        await message.edit(embed=embed)
                        logging.info(f"Updated reaction role message after removing {emoji}")

            except discord.NotFound:
                logging.warning(f"Reaction role message {reaction_message_id} not found")
            except Exception as e:
                logging.error(f"Error updating reaction role message: {e}")

        # Save the updated data
        await save_reaction_roles_data()

        # Send success message
        await interaction.response.send_message(
            f"✅ Successfully removed reaction role: {emoji} → **{role_name}**\n"
            f"Remaining reaction roles: {len(reaction_roles['roles'])}"
        )

        log_command_execution(interaction, "remove_reaction_role")
        logging.info(f"Removed reaction role: {emoji} -> {role_name} (ID: {role_id})")

    except Exception as e:
        await interaction.response.send_message(f"❌ An error occurred while removing the reaction role: {str(e)}", ephemeral=True)
        log_command_execution(interaction, "remove_reaction_role", False)
        logging.error(f"Error removing reaction role {emoji}: {e}")





async def save_reaction_roles_data():
    """Helper function to save reaction roles data"""
    try:
        logging.info("Saving reaction roles data...")

        # Create a data structure to save
        reaction_data = {
            "message_id": reaction_message_id,
            "channel_id": reaction_channel_id,
            "roles": reaction_roles["roles"],
            "config": reaction_roles["config"]
        }

        # Connect to MongoDB directly
        try:
            client = pymongo.MongoClient("mongodb://localhost:27017/")
            db = client["missminutesbot"]
            collection = db["reaction_roles"]

            # Update the document
            result = collection.replace_one(
                {"_id": "reaction_roles"},
                {"_id": "reaction_roles", **reaction_data},
                upsert=True
            )

            if result.acknowledged:
                logging.info("Reaction roles data saved successfully to MongoDB")
                return True
            else:
                logging.error("MongoDB did not acknowledge the save operation")
                return False

        except Exception as mongo_error:
            logging.error(f"MongoDB error: {mongo_error}")

            # Fallback to the regular save method
            logging.info("Falling back to regular save method...")

            # Load current data
            data = await load_data() or {}

            # Update the reaction_roles data
            data["reaction_roles"] = reaction_data

            # Save the data
            success = await save_data()
            if success:
                logging.info("Reaction roles data saved successfully using fallback method")
            else:
                logging.error("Failed to save reaction roles data using fallback method")

            return success

    except Exception as e:
        logging.error(f"Error saving reaction roles data: {e}")
        import traceback
        traceback.print_exc()
        return False


@bot.event
async def on_raw_reaction_add(payload):
    # Ignore bot reactions
    if payload.user_id == bot.user.id:
        return

    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            role = guild.get_role(reaction_roles["roles"][emoji])
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                # If multiple roles are not allowed, remove other reaction roles first
                if "config" in reaction_roles and not reaction_roles["config"]["allow_multiple"]:
                    for role_id in reaction_roles["roles"].values():
                        if role_id != reaction_roles["roles"][emoji]:
                            old_role = guild.get_role(role_id)
                            if old_role and old_role in member.roles:
                                await member.remove_roles(old_role)

                await member.add_roles(role)
            except discord.HTTPException:
                pass

@bot.event
async def on_raw_reaction_remove(payload):
    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            role = guild.get_role(reaction_roles["roles"][emoji])
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                await member.remove_roles(role)
            except discord.HTTPException:
                pass

# Tebex-related commands
@tree.command(name="validate_purchase", description="Validate a purchase by transaction ID")
@app_commands.default_permissions(administrator=True)
async def validate_purchase(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return

    try:
        # Get transactions from database
        transactions = await get_transactions(limit=100, skip=0, sort_by="timestamp", sort_dir=-1)
        purchase_details = None

        # Find the transaction with matching ID
        for transaction in transactions:
            if transaction.get('transaction_id') == transaction_id:
                purchase_details = transaction
                break

        if purchase_details:
            # Add store information if missing (for backward compatibility)
            if 'store' not in purchase_details:
                purchase_details['store'] = 'Unknown Store'

            # Use current time for validation embed footer (not the original transaction time)
            current_validation_time = datetime.now(timezone.utc)

            # Check for existing claim verification before creating the view
            claim_verification = await get_claim_verification(transaction_id)

            # Create persistent interactive view for validation (no claim button for validation)
            view = PersistentTebexTransactionView(purchase_details, current_validation_time, "validation", False)

            # If transaction is already claimed, update the view's claim status for proper color handling
            if claim_verification:
                admin_name = claim_verification.get('admin_name', 'Unknown')
                admin_id = claim_verification.get('admin_id')
                claimed_at = claim_verification.get('claimed_at')

                # Set the view's claim status to match the database
                view.is_claimed = True
                view.claimed_by = type('User', (), {'name': admin_name, 'id': admin_id})()  # Mock user object
                view.claimed_at = ensure_utc_timezone(claimed_at)

            embed = view.create_compact_embed()

            # Override title for validation context
            is_chargeback = purchase_details.get('chargeback', False)
            if is_chargeback:
                embed.title = "Transaction Verification ⚠️ CHARGEBACK"
                embed.color = 0xFF0000  # Red for chargebacks
            else:
                embed.title = "Transaction Verification ✅"
                # Don't override color - let the view's claim-based color logic handle it

            # Add claim verification field to embed (for display purposes)
            if claim_verification:
                admin_name = claim_verification.get('admin_name', 'Unknown')
                embed.add_field(
                    name="🔍 Verification Status",
                    value=f"✅ Verified by **{admin_name}**",
                    inline=False
                )

            try:
                await interaction.response.send_message(embed=embed, view=view)
                # Get the message object for timeout handling
                if hasattr(interaction, 'original_response'):
                    view.message = await interaction.original_response()

                # Register the persistent view in the database
                await register_persistent_view(view, view.message.id if view.message else None, interaction.channel.id)

                # Add the view to the bot for persistent handling
                bot.add_view(view)

            except Exception as e:
                logging.error(f"Failed to send persistent interactive validation: {e}")
                # Fallback to basic embed
                await interaction.response.send_message(embed=embed)
        else:
            await interaction.response.send_message("Purchase not found.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error in validate_purchase: {e}")
        await interaction.response.send_message("An error occurred while validating the purchase.", ephemeral=True)

@tree.command(name="lookup_transaction", description="Lookup a transaction by ID")
@app_commands.default_permissions(administrator=True)
async def lookup_transaction(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    try:
        # Query transaction from MongoDB using our async function
        transactions = await get_transactions(limit=100, skip=0, sort_by="timestamp", sort_dir=-1)
        transaction_details = None

        # Find the transaction with matching ID
        for transaction in transactions:
            if transaction.get('transaction_id') == transaction_id:
                transaction_details = transaction
                break

        if not transaction_details:
            await interaction.response.send_message("Transaction not found.", ephemeral=True)
            return

        # Add store information if missing (for backward compatibility)
        if 'store' not in transaction_details:
            transaction_details['store'] = 'Unknown Store'

        # Use current time for lookup embed footer (not the original transaction time)
        current_lookup_time = datetime.now(timezone.utc)

        # Check for existing claim verification before creating the view
        claim_verification = await get_claim_verification(transaction_id)

        # Create persistent interactive view for lookup (with claim button for staff)
        view = PersistentTebexTransactionView(transaction_details, current_lookup_time, "lookup", True)

        # If transaction is already claimed, update the view's claim status
        if claim_verification:
            admin_name = claim_verification.get('admin_name', 'Unknown')
            admin_id = claim_verification.get('admin_id')
            claimed_at = claim_verification.get('claimed_at')

            # Set the view's claim status to match the database
            view.is_claimed = True
            view.claimed_by = type('User', (), {'name': admin_name, 'id': admin_id})()  # Mock user object
            view.claimed_at = ensure_utc_timezone(claimed_at)

            # Update the claim button to show the claimer's name
            for item in view.children:
                if hasattr(item, 'custom_id') and item.custom_id == "tebex_mark_claimed":
                    item.disabled = True
                    item.label = f"Verified by @{admin_name}"
                    item.style = discord.ButtonStyle.secondary
                    item.emoji = "✅"
                    break

        embed = view.create_compact_embed()

        # Override title for lookup context
        is_chargeback = transaction_details.get('chargeback', False)
        if is_chargeback:
            embed.title = "Transaction Lookup ⚠️ CHARGEBACK"
            embed.color = 0xFF0000  # Red for chargebacks
        else:
            embed.title = "Transaction Lookup 📋"
            # Don't override color - let the view's claim-based color logic handle it

        # Add claim verification field to embed (for display purposes)
        if claim_verification:
            admin_name = claim_verification.get('admin_name', 'Unknown')
            embed.add_field(
                name="🔍 Verification Status",
                value=f"✅ Verified by **{admin_name}**",
                inline=False
            )

        try:
            await interaction.response.send_message(embed=embed, view=view)
            # Get the message object for timeout handling
            if hasattr(interaction, 'original_response'):
                view.message = await interaction.original_response()

            # Register the persistent view in the database
            await register_persistent_view(view, view.message.id if view.message else None, interaction.channel.id)

            # Add the view to the bot for persistent handling
            bot.add_view(view)

        except Exception as e:
            logging.error(f"Failed to send persistent interactive lookup: {e}")
            # Fallback to basic embed
            await interaction.response.send_message(embed=embed)
    except Exception as e:
        logging.error(f"Error in lookup_transaction: {e}")
        await interaction.response.send_message("An error occurred while looking up the transaction.", ephemeral=True)

@tree.command(name="markchargeback", description="Mark a transaction as a chargeback")
@app_commands.default_permissions(administrator=True)
async def markchargeback(interaction: discord.Interaction, transaction_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return

    try:
        # First, get the transaction
        transactions = await get_transactions(limit=100, skip=0, sort_by="timestamp", sort_dir=-1)
        transaction = None

        # Find the transaction with matching ID
        for t in transactions:
            if t.get('transaction_id') == transaction_id:
                transaction = t
                break

        if not transaction:
            await interaction.response.send_message("Transaction not found.", ephemeral=True)
            return

        # Update the transaction with chargeback status
        transaction['chargeback'] = True

        # Save the updated transaction
        success = await save_transaction(transaction)

        if not success:
            await interaction.response.send_message("Failed to update transaction. Please try again.", ephemeral=True)
            return

        # Create response embed
        current_time = datetime.now(timezone.utc)
        embed = discord.Embed(
            title="Chargeback Status Updated",
            description="**Security Action Completed**\n\nThe transaction has been successfully flagged as a chargeback in our fraud prevention system.",
            color=0xE74C3C,  # Professional red
            timestamp=current_time
        )

        # Add structured transaction details
        embed.add_field(
            name="📋 Transaction Details",
            value=f"**Transaction ID:** `{transaction_id}`\n**Amount:** `{transaction.get('price', 'N/A')}`\n**Product:** `{transaction.get('item', 'N/A')}`",
            inline=True
        )

        embed.add_field(
            name="👤 Customer Information",
            value=f"**Name:** `{transaction.get('buyer', 'N/A')}`\n**Email:** `{transaction.get('email', 'Not provided') if 'email' in transaction else 'Not provided'}`",
            inline=True
        )

        embed.add_field(
            name="⚠️ Security Action",
            value=f"**Status:** 🚫 Chargeback Flagged\n**Updated:** {current_time.strftime('%B %d, %Y at %I:%M %p UTC')}",
            inline=False
        )

        # Set professional footer
        embed.set_footer(
            text=f"Fraud Prevention System • {current_time.strftime('%m/%d/%Y %I:%M %p')}"
        )

        await interaction.response.send_message(embed=embed)

    except Exception as e:
        logging.error(f"Error in markchargeback command: {e}")
        await interaction.response.send_message(
            "An error occurred while processing the command. Please try again later.",
            ephemeral=True
        )

# Set Tebex webhook channel
@tree.command(name="set_tebex_channel", description="Set up Tebex notification channels")
@app_commands.default_permissions(administrator=True)
async def set_tebex_channel(
    interaction: discord.Interaction,
    tebex_webhook: str,
    output_channel: discord.TextChannel
):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    """
    Set up Tebex channels:
    tebex_webhook: Discord webhook URL where Tebex will send notifications
    output_channel: Channel where formatted purchase messages will be sent
    """
    global tebex_channel, webhook_url

    try:
        # Validate webhook URL format
        if not tebex_webhook.startswith('https://discord.com/api/webhooks/'):
            await interaction.response.send_message(
                "Invalid webhook URL. Please provide a valid Discord webhook URL.",
                ephemeral=True
            )
            return

        # Store the webhook URL and channel ID
        webhook_url = tebex_webhook
        tebex_channel = output_channel.id

        # Create embed with setup information
        current_time = datetime.now(timezone.utc)
        embed = discord.Embed(
            title="Payment System Configuration",
            description="**Integration Setup Complete**\n\nYour Tebex payment system has been successfully configured and is ready for secure transaction processing.",
            color=0x2ECC71,  # Professional green
            timestamp=current_time
        )

        # Add structured configuration details
        embed.add_field(
            name="🔗 Integration Status",
            value=f"**Webhook:** ✅ Configured\n**Channel:** {output_channel.mention}\n**Status:** Active",
            inline=True
        )

        embed.add_field(
            name="🛡️ Security Features",
            value="**Encryption:** Enabled\n**Validation:** Active\n**Monitoring:** Real-time",
            inline=True
        )

        embed.add_field(
            name="📋 Next Steps",
            value=f"**Complete Setup:** Follow instructions below\n**Test Integration:** Send test transaction\n**Monitor:** Check {output_channel.mention}",
            inline=False
        )

        # Add Tebex setup instructions with professional formatting
        embed.add_field(
            name="🔧 Tebex Dashboard Configuration",
            value=(
                "**Step 1:** Access your Tebex merchant dashboard\n"
                "**Step 2:** Navigate to **Webhooks** → **Settings**\n"
                "**Step 3:** Create new webhook endpoint\n"
                "**Step 4:** Configure notification format (see below)\n"
                "**Step 5:** Test integration and verify functionality"
            ),
            inline=False
        )

        # Add notification structure with enhanced formatting
        embed.add_field(
            name="📝 Required Webhook Format",
            value=(
                "```yaml\n"
                "# Exact format required - do not modify\n"
                "{webstore} has received a payment ╽ "
                "From: {username} ╽ "
                "Price: {price} ╽ "
                "Package: {packagename} ╽ "
                "Transaction ID: {transactionid} ╽ "
                "Email: {email}\n"
                "```\n"
                "⚠️ **Critical:** Structure must match exactly, including `╽` separators"
            ),
            inline=False
        )

        # Set professional footer
        embed.set_footer(
            text=f"Payment Integration System • {current_time.strftime('%m/%d/%Y %I:%M %p')}"
        )

        # Save the settings
        await save_data()

        # Send the confirmation embed
        await interaction.response.send_message(embed=embed, ephemeral=True)

    except Exception as e:
        await interaction.response.send_message(
            f"An error occurred: {str(e)}",
            ephemeral=True
        )

# Add purchase with improved formatting and validation
@tree.command(name="add_purchase", description="Add a purchase to the database")
@app_commands.default_permissions(administrator=True)
async def add_purchase(interaction: discord.Interaction, transaction_id: str, buyer: str, item: str, price: str, email: str = None):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
        return
    if not tebex_channel:
        await interaction.response.send_message("Tebex system is not configured. Please use /set_tebex_channel first.", ephemeral=True)
        return

    # Create transaction document
    current_time = datetime.now(timezone.utc)
    transaction = {
        'transaction_id': transaction_id,
        'buyer': buyer,
        'item': item,
        'price': price,
        'email': email if email else 'No Email',
        'timestamp': current_time,
        'chargeback': False,
        'store': 'Manual Entry'
    }

    try:
        # Use our async save_transaction function
        success = await save_transaction(transaction)

        if not success:
            await interaction.response.send_message("Failed to save transaction. Please try again.", ephemeral=True)
            return

        # Create persistent interactive view for manual entry (with claim button)
        view = PersistentTebexTransactionView(transaction, current_time, "manual_entry", True)
        embed = view.create_compact_embed()

        # Override title and color for manual entry context
        embed.title = "Transaction Successfully Recorded ✅"
        embed.color = 0x00FF00  # Green for successful manual entry

        try:
            await interaction.response.send_message(embed=embed, view=view)
            # Get the message object for timeout handling
            if hasattr(interaction, 'original_response'):
                view.message = await interaction.original_response()

            # Register the persistent view in the database
            await register_persistent_view(view, view.message.id if view.message else None, interaction.channel.id)

            # Add the view to the bot for persistent handling
            bot.add_view(view)

        except Exception as e:
            logging.error(f"Failed to send persistent interactive manual entry: {e}")
            # Fallback to basic embed
            await interaction.response.send_message(embed=embed)
    except Exception as e:
        logging.error(f"Error adding purchase: {e}")
        await interaction.response.send_message("An error occurred while adding the purchase.", ephemeral=True)

# Function to save data

async def save_data():
    global gang_strikes, gang_roles, gang_members, gang_leaders, gang_invitations
    global application_forms, application_channel, application_log_channel, application_fallback_channel, sticky_messages
    global welcome_channel_id, welcome_message, welcome_image_url, vanity_url, role_name, notification_channel_id
    global join_role_id, reaction_message_id, reaction_channel_id, emoji_role_map, tebex_channel, webhook_url
    global applications_status

    try:
        # Convert numeric keys to strings in gang data
        processed_gang_roles = {}
        for gang_name, gang_data in gang_roles.items():
            processed_gang_data = gang_data.copy()
            processed_gang_data['leader'] = str(gang_data['leader'])
            processed_gang_data['leader_role'] = str(gang_data['leader_role'])
            processed_gang_data['members'] = [str(member_id) for member_id in gang_data['members']]
            processed_gang_roles[gang_name] = processed_gang_data

        processed_gang_leaders = {str(k): str(v) for k, v in gang_leaders.items()}
        processed_gang_members = {str(k): v for k, v in gang_members.items()}
        processed_gang_strikes = {str(k): v for k, v in gang_strikes.items()}

        # Log application channel data before saving
        logging.info(f"Saving application data - channel: {application_channel}, log_channel: {application_log_channel}")
        logging.debug(f"Application forms count: {len(application_forms)}, status count: {len(applications_status)}")

        data = {
            "gangs": {
                "roles": processed_gang_roles,
                "members": processed_gang_members,
                "leaders": processed_gang_leaders,
                "strikes": processed_gang_strikes,
                "invitations": gang_invitations  # Save pending gang invitations
            },
            "applications": {
                "forms": application_forms,
                "channels": {
                    "application_channel": application_channel,
                    "log_channel": application_log_channel,
                    "fallback_channel": application_fallback_channel,
                    "response_channel": application_response_channel
                },
                "status": applications_status
            },
            "settings": {
                "welcome": {
                    "channel_id": welcome_channel_id,
                    "message": welcome_message,
                    "image_url": welcome_image_url
                },
                "vanity": {
                    "url": vanity_url,
                    "role_name": role_name
                },
                "notifications": {
                    "channel_id": notification_channel_id
                },
                "join_role_id": join_role_id
            },
            "sticky_messages": sticky_messages,
            "tebex_settings": {
                "channel_id": tebex_channel,
                "webhook_url": webhook_url
            },
            "reaction_roles": {
                "roles": reaction_roles,
                "message_id": reaction_message_id,
                "channel_id": reaction_channel_id
            }
        }

        # Save data to MongoDB
        from database import save_data as db_save_data
        success = await db_save_data(data)

        if success:
            logging.info("Data saved successfully to MongoDB")
            logging.debug(f"Confirmed save - application_channel: {application_channel}, application_log_channel: {application_log_channel}")
        else:
            logging.error("Failed to save data to MongoDB")
            logging.error(f"Failed to save application channels - channel: {application_channel}, log_channel: {application_log_channel}")

    except Exception as e:
        logging.error(f"Error saving data: {e}")


# Function to load data on startup
async def load_data():
    global gang_strikes, gang_roles, gang_members, gang_leaders, gang_invitations
    global application_forms, application_channel, application_log_channel, sticky_messages
    global welcome_channel_id, welcome_message, welcome_image_url, vanity_url, role_name, notification_channel_id
    global join_role_id, reaction_message_id, reaction_channel_id, emoji_role_map, tebex_channel, webhook_url
    global applications_status

    logging.info("Loading bot data from MongoDB...")
    try:
        # Connect to MongoDB
        client = pymongo.MongoClient("mongodb://localhost:27017/")
        db = client["missminutesbot"]

        # Load data from MongoDB collections
        data = {}

        # Load gangs data
        gangs_data = db["gangs"].find_one({"_id": "gangs"})
        if gangs_data:
            data["gangs"] = {k: v for k, v in gangs_data.items() if k != "_id"}
            logging.debug(f"Raw gangs_data from database: {gangs_data}")  # Debug: Show raw database data

        # Load applications data
        applications_data = db["applications"].find_one({"_id": "applications"})
        if applications_data:
            data["applications"] = {k: v for k, v in applications_data.items() if k != "_id"}

        # Load settings data
        settings_data = db["settings"].find_one({"_id": "settings"})
        if settings_data:
            data["settings"] = {k: v for k, v in settings_data.items() if k != "_id"}

        # Load sticky messages
        sticky_data = db["sticky_messages"].find_one({"_id": "sticky_messages"})
        if sticky_data:
            data["sticky_messages"] = sticky_data.get("messages", {})

        # Load tebex settings
        tebex_data = db["tebex_settings"].find_one({"_id": "tebex_settings"})
        if tebex_data:
            data["tebex_settings"] = {k: v for k, v in tebex_data.items() if k != "_id"}

        # Load reaction roles
        reaction_data = db["reaction_roles"].find_one({"_id": "reaction_roles"})
        if reaction_data:
            data["reaction_roles"] = {k: v for k, v in reaction_data.items() if k != "_id"}

        logging.debug("Data loaded from MongoDB.")

        # Load gang data with proper type conversion
        gangs_data = data.get("gangs", {})
        gang_roles_data = gangs_data.get("roles", {})

        # Convert gang_roles data with new permission system
        gang_roles = {}
        for gang_name, gang_data in gang_roles_data.items():
            gang_roles[gang_name] = {
                "leader": int(gang_data["leader"]),
                "leader_role": int(gang_data["leader_role"]),
                "members": [int(m) if isinstance(m, str) and m.isdigit() else m for m in gang_data["members"]],
                "member_limit": gang_data.get("member_limit", 5),
                "current_members": gang_data.get("current_members", 0),
                "member_management_permissions": gang_data.get("member_management_permissions", "leader_only"),  # Default to leader_only for existing gangs
                "officers": gang_data.get("officers", [])  # Default to empty list for existing gangs
            }

        # Convert gang_leaders data
        gang_leaders_data = gangs_data.get("leaders", {})
        gang_leaders = {int(k): int(v) for k, v in gang_leaders_data.items() if k.isdigit() and str(v).isdigit()}

        gang_members = gangs_data.get("members", {})
        gang_strikes = gangs_data.get("strikes", {})
        gang_invitations = gangs_data.get("invitations", {})  # Load pending gang invitations

        logging.info(f"Loaded gang data: {len(gang_roles)} gangs, {len(gang_leaders)} leaders, {len(gang_invitations)} invitations")

        # Log invitation details to file only
        if gang_invitations:
            logging.debug("Gang invitations found:")
            for inv_id, inv_data in gang_invitations.items():
                logging.debug(f"  - {inv_id}: {inv_data.get('gang_name', 'Unknown')} (Target: {inv_data.get('target_id', 'Unknown')})")
        else:
            logging.debug("No gang invitations found in database")

        # Load applications with backward compatibility
        applications = data.get("applications", {})
        application_forms = applications.get("forms", {})

        # Handle both old and new data structure for channels
        channels_data = applications.get("channels", {})
        logging.debug(f"Loading application channels - channels_data: {channels_data}")

        if channels_data:
            # New structure
            application_channel = channels_data.get("application_channel")
            application_log_channel = channels_data.get("log_channel")
            application_fallback_channel = channels_data.get("fallback_channel")
            application_response_channel = channels_data.get("response_channel")
            logging.info(f"Loaded application channels (new structure) - channel: {application_channel}, log_channel: {application_log_channel}, fallback_channel: {application_fallback_channel}, response_channel: {application_response_channel}")
        else:
            # Old structure (backward compatibility)
            application_channel = applications.get("channel")
            application_log_channel = applications.get("log_channel")
            application_fallback_channel = None  # Not available in old structure
            application_response_channel = None  # Not available in old structure
            logging.info(f"Loaded application channels (old structure) - channel: {application_channel}, log_channel: {application_log_channel}")

        # Validate loaded channels
        if application_channel:
            logging.info(f"Application channel loaded successfully: {application_channel}")
        else:
            logging.warning("No application channel found in database")

        applications_status = applications.get("status", {})

        logging.info(f"Loaded applications: {len(application_forms)} forms, {len(applications_status)} status entries")

        # Load settings
        settings = data.get("settings", {})
        welcome = settings.get("welcome", {})
        welcome_channel_id = welcome.get("channel_id")
        welcome_message = welcome.get("message")
        welcome_image_url = welcome.get("image_url")

        logging.debug(f"Loaded welcome settings: ChannelID={welcome_channel_id}")

        vanity = settings.get("vanity", {})
        vanity_url = vanity.get("url")
        role_name = vanity.get("role_name")

        logging.debug(f"Loaded vanity settings: URL={vanity_url}, RoleName={role_name}")

        notifications = settings.get("notifications", {})
        notification_channel_id = notifications.get("channel_id")

        logging.debug(f"Loaded notification settings: ChannelID={notification_channel_id}")

        join_role_id = settings.get("join_role_id")

        # Load sticky messages
        sticky_messages = data.get("sticky_messages", {})

        logging.info(f"Loaded {len(sticky_messages)} sticky messages")

        # Load Tebex settings
        tebex_settings = data.get("tebex_settings", {})
        tebex_channel = tebex_settings.get("channel_id")
        webhook_url = tebex_settings.get("webhook_url")

        logging.debug(f"Loaded Tebex settings: ChannelID={tebex_channel}")

        # Load reaction roles
        reaction_data = data.get("reaction_roles", {})
        global reaction_roles

        # Ensure we have a clean, consistent structure
        roles_dict = {}

        # Extract roles from the data
        if "roles" in reaction_data and isinstance(reaction_data["roles"], dict):
            # Check if it's a flat structure (emoji -> role_id)
            has_flat_structure = all(
                isinstance(k, str) and isinstance(v, (int, str)) and k not in ('config', 'roles')
                for k, v in reaction_data["roles"].items()
            )

            if has_flat_structure:
                roles_dict = reaction_data["roles"]
            # If it's nested, try to extract the emoji->role_id mapping
            elif "roles" in reaction_data["roles"] and isinstance(reaction_data["roles"]["roles"], dict):
                roles_dict = reaction_data["roles"]["roles"]

        # Get config
        config_dict = {"allow_multiple": False}  # Default config
        if "config" in reaction_data:
            config_dict = reaction_data["config"]
        elif "roles" in reaction_data and "config" in reaction_data["roles"]:
            config_dict = reaction_data["roles"]["config"]

        # Set the clean structure
        reaction_roles = {
            "roles": roles_dict,
            "config": config_dict
        }

        global reaction_message_id, reaction_channel_id
        reaction_message_id = reaction_data.get("message_id")
        reaction_channel_id = reaction_data.get("channel_id")

        logging.info(f"Loaded reaction roles: {len(roles_dict)} roles, MessageID={reaction_message_id}, ChannelID={reaction_channel_id}")

        logging.info("Bot data loaded successfully from MongoDB")

    except json.JSONDecodeError as e:
        log_error_to_console(e, "JSON decoding")
    except Exception as e:
        log_error_to_console(e, "data loading")

# Event Handlers Section
@bot.event
async def on_ready():
    """
    Bot initialization with optimized startup sequence and error handling
    - Loads configuration data
    - Syncs commands with retry logic
    - Starts background tasks
    - Initializes memory management
    - Restores interactive components (buttons, reaction roles)
    """
    log_bot_status(f'Logged in as {bot.user} (ID: {bot.user.id})', "SUCCESS")
    logging.info(f'Bot logged in as {bot.user} (ID: {bot.user.id})')

    # Initialize ticket panel monitoring system
    try:
        from tickets import panel_monitor
        asyncio.create_task(panel_monitor.start_monitoring())
        logging.info("Ticket panel monitoring system started")
    except Exception as e:
        logging.error(f"Error starting ticket panel monitoring: {e}")

    # Initialize performance optimization systems
    if PERFORMANCE_OPTIMIZATIONS_ENABLED:
        try:
            logging.info("Initializing performance optimization systems...")

            # Start performance manager
            performance_manager = get_performance_manager()
            await performance_manager.start()

            # Start enhanced database manager
            enhanced_db = get_enhanced_db_manager()
            await enhanced_db.start()

            # Ensure database connection is established
            if not await enhanced_db.ensure_connection():
                logging.warning("Enhanced database connection failed, will use fallback")
            else:
                logging.info("Enhanced database connected successfully")

            # Start Discord API optimizer
            api_optimizer = get_discord_api_optimizer()
            await api_optimizer.start()

            # Register health checks
            performance_manager.register_health_check(
                "database_connection",
                lambda: enhanced_db.connection_pool.is_connected
            )
            performance_manager.register_health_check(
                "memory_usage",
                lambda: get_memory_manager().get_memory_usage() < 2048  # 2GB threshold
            )

            log_bot_status("Performance optimization systems initialized", "SUCCESS")

        except Exception as e:
            log_bot_status(f"Performance optimization initialization failed: {e}", "WARNING")
            logging.warning(f"Performance optimization initialization failed: {e}")

    try:
        # Start memory manager for optimized resource usage
        memory_manager = get_memory_manager()
        memory_manager.start()
        logging.info("Memory manager started")

        # Load ticket data first
        logging.info("Loading ticket configuration...")
        success = await load_ticket_data()
        if not success:
            logging.warning("Failed to load ticket configuration")

        # Auto-recreate ticket panels after loading configuration
        try:
            from tickets import auto_recreate_ticket_panels
            logging.info("Auto-recreating ticket panels...")
            await auto_recreate_ticket_panels()
            logging.info("Ticket panel auto-recreation completed")
        except Exception as e:
            logging.error(f"Error during ticket panel auto-recreation: {e}")

        # Initialize ticket command manager and register commands for guilds with active tickets
        try:
            from tickets import get_ticket_command_manager, active_tickets
            ticket_command_manager = get_ticket_command_manager()

            # Register ticket commands for guilds that have active tickets
            guilds_with_tickets = set()
            for channel_id_str, ticket_data in active_tickets.items():
                try:
                    channel_id = int(channel_id_str)
                    channel = bot.get_channel(channel_id)
                    if channel and channel.guild:
                        guilds_with_tickets.add(channel.guild)
                except (ValueError, AttributeError):
                    continue

            # Register commands for each guild with active tickets
            for guild in guilds_with_tickets:
                try:
                    await ticket_command_manager.register_ticket_commands_for_guild(guild)
                    logging.info(f"Registered ticket commands for guild: {guild.name}")
                except Exception as e:
                    logging.error(f"Failed to register ticket commands for guild {guild.name}: {e}")

            logging.info(f"Ticket command manager initialized for {len(guilds_with_tickets)} guilds")
        except Exception as e:
            logging.error(f"Error initializing ticket command manager: {e}")

        # Load rest of data with optimization
        logging.info("Loading bot data...")
        if PERFORMANCE_OPTIMIZATIONS_ENABLED:
            await optimized_load_data()
            logging.info("Bot data loaded successfully (optimized)")
        else:
            await load_data()
            logging.info("Bot data loaded successfully")

        # Initialize database manager before any validation
        logging.info("Initializing database manager...")
        db_init_success = await ensure_database_manager_initialized()
        if db_init_success:
            logging.info("Database manager initialization completed successfully")
        else:
            logging.error("Database manager initialization failed - some features may not work correctly")

        # Verify data integrity after loading
        logging.info("Verifying data integrity...")
        integrity_ok = await verify_data_integrity()
        if integrity_ok:
            logging.info("Data integrity verification passed")
        else:
            logging.warning("Data integrity issues were found and corrected")

        # Validate startup systems
        logging.info("Validating startup systems...")
        systems_ok = await validate_startup_systems()
        if systems_ok:
            logging.info("Startup system validation passed")
        else:
            logging.warning("Some startup system validations failed - check logs for details")

        # Implement retry logic for command sync
        max_retries = 3
        retry_delay = 5  # seconds

        for attempt in range(max_retries):
            try:
                logging.info(f"Starting command sync process (attempt {attempt + 1}/{max_retries})...")
                synced = await tree.sync()
                logging.info(f"Synced {len(synced)} command(s)!")
                break
            except discord.errors.DiscordServerError as e:
                if attempt < max_retries - 1:
                    logging.warning(f"Discord service temporarily unavailable. Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    logging.error(f"Failed to sync commands after all retries: {e}")

        # Start background tasks
        check_vanity_status.start()
        logging.info("Started vanity status check task")

        # Start modal state cleanup task
        cleanup_modal_states.start()
        logging.info("Started modal state cleanup task")

        # Start application data backup task
        backup_application_data_task.start()
        logging.info("Started application data backup task")

        # Register caches with memory manager for automatic cleanup
        memory_manager.register_cache("sticky_messages", max_size=100, default_ttl=3600)
        memory_manager.register_cache("guild_settings", max_size=500, default_ttl=1800)
        memory_manager.register_cache("transactions", max_size=1000, default_ttl=3600)

        # Restore application buttons
        await restore_application_buttons()
        logging.info("Restored application buttons")

        # Clean up old persistent views before restoration
        await cleanup_old_persistent_views(days_old=30)
        logging.info("Cleaned up old persistent views")

        # Restore persistent Tebx transaction views
        await restore_persistent_views()
        logging.info("Restored persistent transaction views")

        # Restore reaction roles panel
        await restore_reaction_roles()
        logging.info("Restored reaction roles panel")

        # Clean up expired gang invitations first
        await cleanup_expired_gang_invitations()
        logging.info("Cleaned up expired gang invitations")

        # Restore gang invitation views for persistent buttons
        logging.debug(f"About to restore gang invitation views. Current gang_invitations count: {len(gang_invitations)}")
        await restore_gang_invitation_views()
        logging.info("Restored gang invitation views")

        # Recreate application dropdown menu if channel exists
        await recreate_application_panel()
        logging.info("Application panel recreation completed")

        # Recreate reaction role message if channel exists
        # Make sure reaction_message_id and reaction_channel_id are defined
        if 'reaction_message_id' not in globals() or 'reaction_channel_id' not in globals():
            global reaction_message_id, reaction_channel_id
            reaction_message_id = None
            reaction_channel_id = None
            logging.warning("Reaction role variables were not properly initialized")

        # Debug logging for already loaded reaction role data
        logging.info(f"Using already loaded reaction role data: message_id={reaction_message_id}, channel_id={reaction_channel_id}")
        logging.info(f"Reaction roles structure: {reaction_roles}")

        if reaction_channel_id and reaction_message_id:
            try:
                channel = bot.get_channel(reaction_channel_id)
                if channel:
                    logging.info(f"Found channel for reaction roles: {channel.name}")

                    # Try to get the existing message
                    try:
                        message = await channel.fetch_message(reaction_message_id)
                        logging.info(f"Found existing reaction message: {message.id}")
                        # If message exists, clear reactions and recreate
                        await message.clear_reactions()
                    except discord.NotFound:
                        logging.info("Reaction message not found, creating a new one")
                        # If message doesn't exist, create a new one
                        embed = discord.Embed(
                            title="Reaction Roles",
                            description="React to get roles!",
                            color=discord.Color.blue()
                        )
                        message = await channel.send(embed=embed)
                        reaction_message_id = message.id
                        await save_data()

                    # Add fields and reactions for each role
                    logging.info(f"Reaction roles content: {reaction_roles}")

                    # Create a new reaction_roles structure with the correct format
                    # We'll manually extract the emoji and role IDs from the nested structure
                    fixed_roles = {}

                    # Use the already loaded reaction_roles data
                    logging.info(f"Using loaded reaction_roles: {reaction_roles}")

                    # Use the roles from the already loaded reaction_roles
                    fixed_roles = reaction_roles.get("roles", {})
                    logging.info(f"Using reaction roles: {fixed_roles}")

                    # Now proceed with the embed creation
                    embed = message.embeds[0]
                    embed.clear_fields()

                    valid_emojis = []
                    skipped_count = 0

                    for emoji_str, role_id in fixed_roles.items():
                        logging.info(f"Processing role: emoji={emoji_str}, role_id={role_id}, type={type(role_id)}")

                        # Convert role_id to int if it's a string
                        if isinstance(role_id, str) and role_id.isdigit():
                            role_id = int(role_id)

                        role = safe_get_role(channel.guild, role_id)
                        if role:
                            # Use the improved validation and reaction adding
                            success, error_msg = await safe_add_reaction_with_skip(message, emoji_str, "reaction role restoration")
                            if success:
                                logging.info(f"Found role: {role.name}")
                                embed.add_field(name=f"{emoji_str} {role.name}", value="React to get this role!", inline=False)
                                valid_emojis.append(emoji_str)
                                logging.debug(f"Successfully validated and added reaction {emoji_str} for role {role.name}")
                            else:
                                console_log(f"Skipping emoji {emoji_str} for role {role.name}: {error_msg}", "WARNING")
                                logging.warning(f"Skipping emoji {emoji_str} for role {role.name}: {error_msg}")
                                skipped_count += 1
                        else:
                            logging.warning(f"Role not found for ID: {role_id}")
                            skipped_count += 1

                    # Log summary
                    if valid_emojis:
                        console_log(f"Restored {len(valid_emojis)} reaction roles", "SUCCESS")
                    if skipped_count > 0:
                        console_log(f"Skipped {skipped_count} problematic reaction roles", "WARNING")

                    # Clean up any invalid reactions that might exist
                    await clean_invalid_reactions(message, valid_emojis)

                    await message.edit(embed=embed)
                    logging.info("Recreated reaction role message")
                else:
                    logging.warning(f"Channel not found for reaction roles: {reaction_channel_id}")
            except Exception as e:
                logging.error(f"Error recreating reaction role message: {e}")
                import traceback
                traceback.print_exc()

        log_bot_status("Bot initialization completed!", "SUCCESS")
        logging.info("Bot initialization completed!")

    except Exception as e:
        log_error_to_console(e, "bot initialization")
        logging.error(f"Error during bot initialization: {e}")
        import traceback
        traceback.print_exc()

# App command error handler for ticket commands
@bot.tree.error
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Global error handler for app commands, specifically for ticket commands used outside ticket channels"""
    try:
        # Better command name detection
        command_name = "unknown"
        attempted_command = None

        # Try to get command name from interaction
        if interaction.command:
            command_name = interaction.command.name
        else:
            # Try to extract command name from interaction data if available
            try:
                if hasattr(interaction, 'data') and interaction.data:
                    if 'name' in interaction.data:
                        attempted_command = interaction.data['name']
                        command_name = attempted_command
                    elif 'options' in interaction.data and interaction.data['options']:
                        # Sometimes the command name is in options
                        for option in interaction.data['options']:
                            if 'name' in option:
                                attempted_command = option['name']
                                command_name = attempted_command
                                break
            except Exception as e:
                logging.debug(f"Could not extract command name from interaction data: {e}")

        ticket_commands = ["close", "add", "remove", "claim", "unclaim", "transcript", "rename", "lock", "unlock"]

        # Handle CommandNotFound errors specifically
        if isinstance(error, app_commands.CommandNotFound):
            # Enhanced logging with more context
            guild_info = f"Guild: {interaction.guild.name} ({interaction.guild.id})" if interaction.guild else "DM"
            channel_info = f"Channel: #{interaction.channel.name} ({interaction.channel.id})" if hasattr(interaction.channel, 'name') else f"Channel: {interaction.channel.id}"
            user_info = f"User: {interaction.user.name}#{interaction.user.discriminator} ({interaction.user.id})"

            logging.error(f"CommandNotFound - Attempted: '{command_name}' | {guild_info} | {channel_info} | {user_info}")

            # Check if this might be a ticket command that should be available
            if command_name in ticket_commands:
                # Check if we're in a ticket channel
                from tickets import is_ticket_channel
                if hasattr(interaction.channel, 'name') and is_ticket_channel(interaction.channel):
                    # This is a ticket channel but command not found - registration issue
                    logging.warning(f"Ticket command '{command_name}' not found in ticket channel {interaction.channel.name}")
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            f"❌ **Command Registration Issue**\n"
                            f"The `/{command_name}` command should be available in this ticket channel but wasn't found.\n"
                            f"This may be a temporary issue. Please try again in a moment or contact an administrator.",
                            ephemeral=True
                        )
                else:
                    # Not in a ticket channel
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            f"❌ **Command Not Available Here**\n"
                            f"The `/{command_name}` command is only available in ticket channels.\n"
                            f"Please use this command inside a ticket channel.",
                            ephemeral=True
                        )
            elif command_name == "ticket":
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        "❌ **Command Not Available**\n"
                        "The `/ticket` command should be available. This may be a temporary sync issue.\n"
                        "Please try again in a moment or contact an administrator.",
                        ephemeral=True
                    )
            elif command_name == "unknown":
                # We couldn't determine what command was attempted
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        "❌ **Command Not Found**\n"
                        "The command you tried to use is not available. Please check the command name and try again.\n"
                        "Use `/` to see available commands.",
                        ephemeral=True
                    )
            else:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        f"❌ **Command Not Found**\n"
                        f"The command `/{command_name}` is not available. Please check the command name and try again.",
                        ephemeral=True
                    )
            return

        if command_name in ticket_commands:
            # Validate if this is being used in a ticket channel
            is_valid, error_msg = validate_ticket_channel_context(interaction)
            if not is_valid:
                if not interaction.response.is_done():
                    await interaction.response.send_message(error_msg, ephemeral=True)
                return

        # For other errors, log them and send a generic message
        logging.error(f"App command error in {command_name}: {error}")
        if not interaction.response.is_done():
            await interaction.response.send_message(
                "An error occurred while processing this command. Please try again later.",
                ephemeral=True
            )
    except Exception as e:
        logging.error(f"Error in app command error handler: {e}")

# Optimized message handler with sticky message caching
# Cache for sticky messages to avoid repeated DB lookups
sticky_message_cache = {}
sticky_message_last_check = {}
STICKY_CHECK_INTERVAL = 60  # seconds between cache refreshes

@bot.event
async def on_message(message):
    """
    Unified message handler with optimized performance
    - Handles commands
    - Manages sticky messages with caching
    - Processes webhook messages
    """
    try:
        # Ignore messages from the bot itself
        if message.author == bot.user:
            return

        # Process commands first
        await bot.process_commands(message)

        # Handle sticky messages with optimized performance
        channel_id = message.channel.id
        current_time = time.time()

        # Check if we need to refresh the sticky message cache for this channel
        if (channel_id not in sticky_message_last_check or
            current_time - sticky_message_last_check.get(channel_id, 0) > STICKY_CHECK_INTERVAL):
            # Update cache from the global sticky_messages
            if channel_id in sticky_messages:
                sticky_message_cache[channel_id] = sticky_messages[channel_id]
                sticky_message_last_check[channel_id] = current_time
            else:
                # Remove from cache if no longer in sticky_messages
                if channel_id in sticky_message_cache:
                    del sticky_message_cache[channel_id]
                if channel_id in sticky_message_last_check:
                    del sticky_message_last_check[channel_id]

        # Check if this channel has a sticky message
        if channel_id in sticky_message_cache:
            sticky_message = sticky_message_cache[channel_id]

            # Use a more efficient approach to check for existing sticky messages
            # Only check the last 2 messages for better performance
            found_sticky = False
            async for msg in message.channel.history(limit=2):
                if msg.author == bot.user and msg.content == sticky_message:
                    found_sticky = True
                    break

            # Post sticky message if not found in recent messages
            if not found_sticky:
                try:
                    await message.channel.send(sticky_message)
                    logging.info(f"Reposted sticky message in {message.channel.name}")
                except discord.HTTPException as e:
                    logging.error(f"Failed to post sticky message: {e}")

        # Handle Tebex webhook messages
        if message.webhook_id and message.channel.id == tebex_channel:
            try:
                # Parse the webhook message
                content = message.content
                logging.debug(f"Received webhook content: {content[:100]}...")  # Log only first 100 chars

                if "has received a payment" in content:
                    # Process payment notification
                    await process_payment_notification(message, content)

            except Exception as e:
                logging.error(f"Error processing webhook message: {e}")
                traceback.print_exc()

    except Exception as e:
        logging.error(f"Error in on_message event: {e}")
        traceback.print_exc()

class TebexTransactionView(discord.ui.View):
    """Reusable interactive view for all Tebex transaction displays with robust error handling"""

    def __init__(self, transaction_data, current_time, view_type="notification", show_claim_button=True):
        super().__init__(timeout=300)  # 5 minute timeout
        self.transaction_data = transaction_data
        self.current_time = current_time
        self.view_type = view_type  # "notification", "lookup", "validation", etc.
        self.show_claim_button = show_claim_button
        self.is_expanded = False
        self.is_claimed = False
        self.claimed_by = None
        self.claimed_at = None
        self.message = None  # Will be set when message is sent

        # Remove claim button if not needed for this view type
        if not show_claim_button:
            self._remove_claim_button()

    def _remove_claim_button(self):
        """Remove the claim button from the view"""
        for item in self.children[:]:
            if hasattr(item, 'label') and 'Mark as Claimed' in str(item.label):
                self.remove_item(item)
                break

    async def on_timeout(self):
        """Handle view timeout - disable all buttons"""
        try:
            for item in self.children:
                item.disabled = True

            if self.message:
                try:
                    embed = self.create_compact_embed() if not self.is_expanded else self.create_expanded_embed()
                    embed.set_footer(text="⏰ Interactive buttons have expired")
                    await self.message.edit(embed=embed, view=self)
                except discord.HTTPException:
                    pass  # Message might be deleted or inaccessible

            logging.info(f"Tebex transaction view timed out for transaction {self.transaction_data.get('transaction_id', 'unknown')}")
        except Exception as e:
            logging.error(f"Error handling view timeout: {e}")

    async def _safe_interaction_response(self, interaction, embed, view=None):
        """Safely respond to interaction with proper error handling"""
        try:
            # Check if interaction is already responded to
            if interaction.response.is_done():
                logging.warning("Interaction already responded to")
                return False

            # Try to respond
            if view is None:
                view = self
            await interaction.response.edit_message(embed=embed, view=view)
            return True

        except discord.NotFound:
            logging.warning("Interaction not found - likely expired")
            return False
        except discord.HTTPException as e:
            if e.code == 10062:  # Unknown interaction
                logging.warning("Interaction expired (Unknown interaction error)")
            elif e.code == 10008:  # Unknown message
                logging.warning("Message not found - likely deleted")
            else:
                logging.error(f"Discord HTTP error: {e}")
            return False
        except Exception as e:
            logging.error(f"Unexpected error in interaction response: {e}")
            return False

    async def _safe_error_response(self, interaction, message):
        """Safely send error message to user"""
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(message, ephemeral=True)
            else:
                await interaction.followup.send(message, ephemeral=True)
        except:
            pass  # Fail silently if we can't send error message

    @discord.ui.button(label="View Details", style=discord.ButtonStyle.primary, emoji="📋", custom_id="tebex_view_details")
    async def view_details_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Toggle between compact and expanded view with robust error handling"""
        try:
            # Check permissions
            if not (interaction.user.guild_permissions.manage_messages or
                   interaction.user.guild_permissions.administrator or
                   any(role.name.lower() in ['staff', 'admin', 'moderator', 'support'] for role in interaction.user.roles)):
                await self._safe_error_response(interaction, "You don't have permission to view detailed transaction information.")
                return

            # Toggle the expanded state
            self.is_expanded = not self.is_expanded

            # Create the appropriate embed
            if self.is_expanded:
                embed = self.create_expanded_embed()
                button.label = "Hide Details"
                button.emoji = "📄"
                button.style = discord.ButtonStyle.secondary
            else:
                embed = self.create_compact_embed()
                button.label = "View Details"
                button.emoji = "📋"
                button.style = discord.ButtonStyle.primary

            # Try to respond safely
            success = await self._safe_interaction_response(interaction, embed)
            if success:
                logging.info(f"Transaction details {'expanded' if self.is_expanded else 'collapsed'} by {interaction.user.name}")
            else:
                # Revert state change if response failed
                self.is_expanded = not self.is_expanded

        except Exception as e:
            logging.error(f"Error in view_details_button: {e}")
            await self._safe_error_response(interaction, "An error occurred while updating the transaction details.")

    @discord.ui.button(label="Mark as Claimed", style=discord.ButtonStyle.success, emoji="✅", custom_id="tebex_mark_claimed")
    async def mark_claimed_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Mark the transaction as claimed with robust error handling"""
        try:
            # Check permissions
            if not (interaction.user.guild_permissions.manage_messages or
                   interaction.user.guild_permissions.administrator or
                   any(role.name.lower() in ['staff', 'admin', 'moderator', 'support'] for role in interaction.user.roles)):
                await self._safe_error_response(interaction, "You don't have permission to mark transactions as claimed.")
                return

            # Set claimed status
            self.is_claimed = True
            self.claimed_by = interaction.user
            self.claimed_at = datetime.now(timezone.utc)

            # Update embed to "Transaction Verified" state
            embed = self.create_verified_embed()

            # Update button to show claimer's name directly on the button
            button.disabled = True
            button.label = f"Verified by @{interaction.user.name}"
            button.style = discord.ButtonStyle.secondary
            button.emoji = "✅"  # Keep the checkmark emoji

            # Try to respond safely
            success = await self._safe_interaction_response(interaction, embed)
            if success:
                # Save claim verification to database
                transaction_id = self.transaction_data.get('transaction_id')
                if transaction_id:
                    db_success = await save_claim_verification(
                        transaction_id=transaction_id,
                        admin_id=interaction.user.id,
                        admin_name=interaction.user.name,
                        timestamp=self.claimed_at
                    )
                    if db_success:
                        logging.info(f"Transaction {transaction_id} claimed by {interaction.user.name} ({interaction.user.id}) and saved to database")
                    else:
                        logging.warning(f"Transaction {transaction_id} claimed by {interaction.user.name} but failed to save to database")
                else:
                    logging.warning("Transaction claimed but no transaction_id found for database save")
            else:
                # Revert state changes if response failed
                self.is_claimed = False
                self.claimed_by = None
                self.claimed_at = None
                button.disabled = False
                button.label = "Mark as Claimed"
                button.style = discord.ButtonStyle.success

        except Exception as e:
            logging.error(f"Error in mark_claimed_button: {e}")
            await self._safe_error_response(interaction, "An error occurred while marking the transaction as claimed.")

    def create_compact_embed(self):
        """Create the compact version of the transaction embed"""
        # Determine color based on claim status and chargeback status
        is_chargeback = self.transaction_data.get('chargeback', False)
        if is_chargeback:
            color = 0xFF0000  # Red for chargebacks
            title = "Transaction Flagged ⚠️"
        elif self.is_claimed:
            color = 0xFF0080  # Purple/magenta for claimed transactions
            title = "Transaction Notification" if self.view_type == "notification" else "Transaction Details"
        else:
            color = 0x00FF00  # Green for unclaimed transactions
            title = "Transaction Notification" if self.view_type == "notification" else "Transaction Details"

        # Create description in the new format
        store = self.transaction_data.get('store', 'Unknown Store')
        buyer = self.transaction_data.get('buyer', 'Unknown')
        price = self.transaction_data.get('price', '$0')
        item = self.transaction_data.get('item', 'Unknown')
        transaction_id = self.transaction_data.get('transaction_id', '0')
        email = self.transaction_data.get('email', 'No Email')

        description = f"{store} has received a payment | From: {buyer} | Price: {price} | Package: {item} | Transaction ID: {transaction_id} | Email: {email}"

        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=self.current_time
        )

        embed.set_footer(text="Crimson")
        return embed

    def create_expanded_embed(self):
        """Create the expanded version of the transaction embed"""
        # Determine color and title based on claim status
        is_chargeback = self.transaction_data.get('chargeback', False)
        if is_chargeback:
            color = 0xFF0000  # Red for chargebacks
            title = "Transaction Flagged ⚠️"
        elif self.is_claimed:
            color = 0xFF0080  # Purple/magenta for claimed transactions
            title = "Transaction Verified"
        else:
            color = 0x00FF00  # Green for unclaimed transactions
            title = "Transaction Verified"

        # Create description in the new format
        store = self.transaction_data.get('store', 'Unknown Store')
        buyer = self.transaction_data.get('buyer', 'Unknown')
        price = self.transaction_data.get('price', '$0')
        item = self.transaction_data.get('item', 'Unknown')
        transaction_id = self.transaction_data.get('transaction_id', '0')
        email = self.transaction_data.get('email', 'No Email')

        description = f"{store} has received a payment | From: {buyer} | Price: {price} | Package: {item} | Transaction ID: {transaction_id} | Email: {email}"

        embed = discord.Embed(
            title=title,
            description=description,
            color=color,
            timestamp=self.current_time
        )

        # Add detailed transaction information
        embed.add_field(
            name="📋 Transaction Details",
            value=f"**Transaction ID:** `{transaction_id}`\n**Amount:** `{price}`\n**Product:** `{item}`",
            inline=True
        )

        embed.add_field(
            name="👤 Customer Information",
            value=f"**Name:** `{buyer}`\n**Email:** `{email}`",
            inline=True
        )

        embed.add_field(
            name="⏰ Processing Time",
            value=f"**Completed:** {self.current_time.strftime('%B %d, %Y')}\n**Time:** {self.current_time.strftime('%I:%M %p UTC')}",
            inline=False
        )

        # Add security status if chargeback
        if is_chargeback:
            embed.add_field(
                name="🔒 Security Status",
                value="**Status:** 🚫 Chargeback Flagged\n**Risk Level:** High",
                inline=False
            )

        embed.set_footer(text="Crimson")
        return embed

    def create_verified_embed(self):
        """Create the verified embed when transaction is claimed"""
        embed = discord.Embed(
            title="Transaction Verified",
            description=f"{self.transaction_data.get('store', 'Unknown Store')} has received a payment | From: {self.transaction_data.get('buyer', 'Unknown')} | Price: {self.transaction_data.get('price', '$0')} | Package: {self.transaction_data.get('item', 'Unknown')} | Transaction ID: {self.transaction_data.get('transaction_id', '0')} | Email: {self.transaction_data.get('email', 'No Email')}",
            color=0xFF0080,  # Pink/magenta color to match reference image
            timestamp=self.current_time
        )

        # If expanded view was active, include detailed fields
        if self.is_expanded:
            embed.add_field(
                name="📋 Transaction Details",
                value=f"**Transaction ID:** `{self.transaction_data.get('transaction_id', '0')}`\n**Amount:** `{self.transaction_data.get('price', '$0')}`\n**Product:** `{self.transaction_data.get('item', 'Unknown')}`",
                inline=True
            )

            embed.add_field(
                name="👤 Customer Information",
                value=f"**Name:** `{self.transaction_data.get('buyer', 'Unknown')}`\n**Email:** `{self.transaction_data.get('email', 'No Email')}`",
                inline=True
            )

            embed.add_field(
                name="⏰ Processing Time",
                value=f"**Completed:** {self.current_time.strftime('%B %d, %Y')}\n**Time:** {self.current_time.strftime('%I:%M %p UTC')}",
                inline=False
            )

        embed.set_footer(text="Crimson")
        return embed


class PersistentTebexTransactionView(TebexTransactionView):
    """Persistent version of TebexTransactionView that never times out and persists across bot restarts"""

    def __init__(self, transaction_data, current_time, view_type="notification", show_claim_button=True, view_id=None):
        # Initialize parent class first
        super().__init__(transaction_data, current_time, view_type, show_claim_button)

        # CRITICAL: Override timeout to None for persistence - this must be done AFTER super().__init__
        self.timeout = None

        # Generate unique view ID if not provided
        if view_id:
            self.view_id = view_id
        else:
            import uuid
            self.view_id = str(uuid.uuid4())

    async def on_timeout(self):
        """This should never be called since timeout=None, but handle it just in case"""
        logging.warning(f"Persistent Tebx transaction view {self.view_id} timed out unexpectedly")
        # Don't disable the view - keep it active
        pass

    async def on_error(self, interaction: discord.Interaction, error: Exception, item):
        """Handle any errors that occur in the persistent view"""
        logging.error(f"Error in persistent Tebx transaction view {self.view_id}: {error}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "❌ An error occurred. Please try again or contact an administrator.",
                    ephemeral=True
                )
        except:
            pass


async def process_payment_notification(message, content):
    """Process Tebex payment notification webhook with interactive embed system"""
    try:
        parts = content.split("╽")
        data = {}

        # Parse the first part (store name)
        data['store'] = parts[0].split("has received a payment")[0].strip()

        # Parse remaining parts
        for part in parts[1:]:
            if ":" in part:
                key, value = part.split(":", 1)
                key = key.strip().lower()
                value = value.strip()
                data[key] = value

        # Get current time
        current_time = datetime.now(timezone.utc)

        # Store transaction in MongoDB
        transaction_data = {
            'buyer': data.get('from', 'Unknown'),
            'item': data.get('package', 'Unknown'),
            'price': data.get('price', '$0'),
            'email': data.get('email', 'No Email'),
            'timestamp': current_time,
            'transaction_id': data.get('transaction id', '0'),
            'chargeback': False,
            'store': data.get('store', 'Unknown Store')
        }

        # Save transaction to MongoDB using the imported function
        await save_transaction(transaction_data)

        # Create persistent interactive view for notification (with claim button)
        view = PersistentTebexTransactionView(transaction_data, current_time, "notification", True)
        embed = view.create_compact_embed()

        # Override embed to use simple compact format matching reference image
        embed.title = "Transaction Notification"
        # Don't override color - let the view's natural color logic handle it based on claim status

        # Clear any existing fields
        embed.clear_fields()

        # Use simple single-line description format matching reference image
        embed.description = f"Crimson has received a payment | From: {data.get('from', 'Unknown')} | Price: {data.get('price', '$0')} | Package: {data.get('package', 'Unknown')} | Transaction ID: {data.get('transaction id', '0')} | Email: {data.get('email', 'No Email')}"

        embed.set_footer(text="Crimson")

        # Try to delete the webhook message after sending the formatted one
        try:
            await message.delete()
        except discord.NotFound:
            logging.warning("Webhook message already deleted or not found")
        except Exception as e:
            logging.error(f"Failed to delete webhook message: {e}")

        # Send the formatted message with interactive view
        channel = bot.get_channel(tebex_channel)
        if channel:
            try:
                sent_message = await channel.send(embed=embed, view=view)
                view.message = sent_message

                # Register the persistent view in the database
                await register_persistent_view(view, sent_message.id, channel.id)

                # Add the view to the bot for persistent handling
                bot.add_view(view)

                console_log("Payment notification processed with interactive buttons", "SUCCESS")
            except Exception as e:
                logging.error(f"Failed to send interactive notification: {e}")
                # Fallback to basic embed
                await channel.send(embed=embed)
                console_log("Payment notification processed (fallback)", "WARNING")
        else:
            logging.error(f"Could not find channel with ID {tebex_channel}")

    except Exception as e:
        logging.error(f"Error processing payment notification: {e}")
        traceback.print_exc()

@bot.event
async def on_member_join(member_obj):
    """Handle new member joins with welcome message and auto-role"""
    try:
        # Send welcome message if configured
        if welcome_channel_id:
            channel = bot.get_channel(welcome_channel_id)
            if channel:
                embed = discord.Embed(
                    title="Welcome!",
                    description=welcome_message,
                    color=discord.Color.blue()
                )
                if welcome_image_url:
                    embed.set_image(url=welcome_image_url)
                await channel.send(content=f"Welcome {member_obj.mention}!", embed=embed)
                logging.debug(f"Sent welcome message for {member_obj.name}")

        # Assign auto-role if configured
        if join_role_id:
            role = safe_get_role(member_obj.guild, join_role_id)
            if role:
                if await validate_role_permissions(member_obj.guild, role):
                    success = await safe_add_role(member_obj, role, "Auto-role assignment")
                    if success:
                        console_log(f"Auto-role assigned: {role.name} → {member_obj.name}", "SUCCESS")
                    else:
                        logging.warning(f"Failed to assign auto-role {role.name} to {member_obj.name}")
                else:
                    logging.warning(f"Bot lacks permission to assign auto-role {role.name} in {member_obj.guild.name}")
            else:
                logging.warning(f"Join role with ID {join_role_id} not found in guild {member_obj.guild.name}")
    except Exception as e:
        logging.error(f"Error in on_member_join: {e}")
        traceback.print_exc()

@bot.event
async def on_raw_reaction_add(payload):
    # Ignore bot reactions
    if payload.user_id == bot.user.id:
        return

    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            # Get role ID and convert to int if it's a string
            role_id = reaction_roles["roles"][emoji]
            if isinstance(role_id, str) and role_id.isdigit():
                role_id = int(role_id)

            role = guild.get_role(role_id)
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                # If multiple roles are not allowed, remove other reaction roles first
                if "config" in reaction_roles and not reaction_roles["config"]["allow_multiple"]:
                    current_role_id = reaction_roles["roles"][emoji]

                    for emoji_key, other_role_id in reaction_roles["roles"].items():
                        # Skip the current role
                        if emoji_key == emoji:
                            continue

                        # Convert role IDs to strings for comparison if needed
                        if isinstance(current_role_id, int) and isinstance(other_role_id, str) and other_role_id.isdigit():
                            other_role_id_int = int(other_role_id)
                        elif isinstance(current_role_id, str) and current_role_id.isdigit() and isinstance(other_role_id, int):
                            current_role_id = int(current_role_id)
                            other_role_id_int = other_role_id
                        elif isinstance(other_role_id, str) and other_role_id.isdigit():
                            other_role_id_int = int(other_role_id)
                        else:
                            other_role_id_int = other_role_id

                        # Remove other roles
                        if current_role_id != other_role_id_int:
                            old_role = guild.get_role(other_role_id_int)
                            if old_role and old_role in member.roles:
                                await member.remove_roles(old_role)

                await member.add_roles(role)
            except discord.HTTPException:
                pass

@bot.event
async def on_raw_reaction_remove(payload):
    # Check if this is a reaction role message
    if payload.message_id == reaction_message_id:
        emoji = str(payload.emoji)
        # Check if reaction_roles has the expected structure
        if "roles" in reaction_roles and emoji in reaction_roles["roles"]:
            guild = bot.get_guild(payload.guild_id)
            if not guild:
                return

            # Get role ID and convert to int if it's a string
            role_id = reaction_roles["roles"][emoji]
            if isinstance(role_id, str) and role_id.isdigit():
                role_id = int(role_id)

            role = guild.get_role(role_id)
            if not role:
                return

            member = guild.get_member(payload.user_id)
            if not member:
                return

            try:
                await member.remove_roles(role)
            except discord.HTTPException:
                pass

async def register_persistent_view(view, message_id=None, channel_id=None):
    """Register a persistent view in the database"""
    try:
        success = await save_persistent_view(
            view.view_id,
            view.transaction_data.get('transaction_id'),
            view.view_type,
            view.show_claim_button,
            message_id,
            channel_id
        )
        if success:
            logging.info(f"Registered persistent view {view.view_id}")
        return success
    except Exception as e:
        logging.error(f"Error registering persistent view: {e}")
        return False

async def restore_persistent_views():
    """Restore all persistent views after bot restart"""
    try:
        logging.info("Restoring persistent Tebex transaction views...")

        # Get all active persistent views from database
        active_views = await get_all_active_persistent_views()

        if not active_views:
            logging.info("No persistent views to restore")
            return

        restored_count = 0
        failed_count = 0

        # Load all transactions once for efficiency
        logging.info("Loading transaction data for view restoration...")
        all_transactions = await get_transactions(limit=1000)
        transactions_by_id = {t.get('transaction_id'): t for t in all_transactions if t.get('transaction_id')}
        logging.info(f"Loaded {len(transactions_by_id)} transactions for view restoration")

        for view_data in active_views:
            try:
                # Get transaction data
                transaction_id = view_data.get('transaction_id')
                if not transaction_id:
                    continue

                # Get transaction data from pre-loaded cache
                transaction_data = transactions_by_id.get(transaction_id)

                if not transaction_data:
                    logging.warning(f"Transaction {transaction_id} not found for view {view_data.get('view_id')}, deactivating view")
                    await deactivate_persistent_view(view_data.get('view_id'))
                    failed_count += 1
                    continue

                # Validate required view data
                view_id = view_data.get('view_id')
                if not view_id:
                    logging.warning(f"View missing view_id, skipping restoration")
                    failed_count += 1
                    continue

                # Create persistent view
                view = PersistentTebexTransactionView(
                    transaction_data,
                    ensure_utc_timezone(view_data.get('created_at', datetime.now(timezone.utc))),
                    view_data.get('view_type', 'notification'),
                    view_data.get('show_claim_button', True),
                    view_id
                )

                # Check if this transaction has been claimed and restore claim state
                claim_verification = await get_claim_verification(transaction_id)
                if claim_verification:
                    view.is_claimed = True
                    view.claimed_by = None  # We don't have the user object, just the name
                    view.claimed_at = ensure_utc_timezone(claim_verification.get('claimed_at'))

                    # Update the claim button to show claimed state
                    for item in view.children:
                        if hasattr(item, 'custom_id') and item.custom_id == 'tebex_mark_claimed':
                            item.disabled = True
                            item.label = f"Verified by {claim_verification.get('admin_name', 'Unknown')}"
                            item.style = discord.ButtonStyle.secondary
                            item.emoji = "✅"
                            break

                # Register the view with the bot
                bot.add_view(view)
                restored_count += 1

                logging.debug(f"Restored persistent view {view.view_id} for transaction {transaction_id}")

            except Exception as e:
                logging.error(f"Error restoring persistent view {view_data.get('view_id', 'unknown')}: {e}")
                failed_count += 1

        logging.info(f"Persistent view restoration complete: {restored_count} restored, {failed_count} failed")

    except Exception as e:
        logging.error(f"Error during persistent view restoration: {e}")

async def restore_application_buttons():
    """Restore application buttons after bot restart"""
    try:
        logging.debug("Attempting to restore application buttons...")
        if application_log_channel:
            channel = bot.get_channel(application_log_channel)
            if channel:
                logging.debug(f"Found application log channel: {channel.name}")

                # Convert applications_status to use string keys if needed
                string_keyed_status = {}
                for user_id, status in applications_status.items():
                    string_keyed_status[str(user_id)] = status

                for user_id_str, status in string_keyed_status.items():
                    if not status.get("responded", False):
                        try:
                            user_id_int = int(user_id_str)
                            message_id = status.get("message_id")

                            if not message_id:
                                logging.warning(f"No message_id for application from user {user_id_str}")
                                continue

                            logging.debug(f"Restoring buttons for message {message_id} from user {user_id_str}")

                            try:
                                message = await channel.fetch_message(message_id)
                            except discord.NotFound:
                                logging.warning(f"Could not find message {message_id} for application")
                                continue

                            if message:
                                # Create a persistent view with a timeout of None
                                view = View(timeout=None)

                                # Create professional buttons with custom_ids to make them persistent
                                accept_button = Button(
                                    label="Approve Application",
                                    style=discord.ButtonStyle.success,
                                    custom_id=f"app_accept_{user_id_str}_{message_id}"
                                )

                                reject_button = Button(
                                    label="Decline Application",
                                    style=discord.ButtonStyle.danger,
                                    custom_id=f"app_reject_{user_id_str}_{message_id}"
                                )

                                accept_reason_button = Button(
                                    label="Approve with Feedback",
                                    style=discord.ButtonStyle.primary,
                                    custom_id=f"app_accept_reason_{user_id_str}_{message_id}"
                                )

                                reject_reason_button = Button(
                                    label="Decline with Feedback",
                                    style=discord.ButtonStyle.secondary,
                                    custom_id=f"app_reject_reason_{user_id_str}_{message_id}"
                                )

                                user = bot.get_user(user_id_int)
                                app_name = status["application_name"]

                                if not user:
                                    logging.warning(f"Could not find user with ID {user_id_int}")
                                    # Try to fetch the user if not in cache
                                    try:
                                        user = await bot.fetch_user(user_id_int)
                                    except:
                                        logging.error(f"Failed to fetch user with ID {user_id_int}")
                                        continue

                                # Define callbacks for each button with administrator permission checks
                                async def accept_callback(i):
                                    # Check if user has administrator permissions
                                    if not i.user.guild_permissions.administrator:
                                        await i.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                                        return
                                    await handle_application_response(i, user, "accepted", app_name)

                                async def reject_callback(i):
                                    # Check if user has administrator permissions
                                    if not i.user.guild_permissions.administrator:
                                        await i.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                                        return
                                    await handle_application_response(i, user, "rejected", app_name)

                                async def accept_with_reason_callback(i):
                                    # Check if user has administrator permissions
                                    if not i.user.guild_permissions.administrator:
                                        await i.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                                        return
                                    if applications_status.get(str(user.id), {}).get("responded"):
                                        await i.response.send_message("This application has already been responded to.", ephemeral=True)
                                        return
                                    await i.response.send_modal(AcceptReasonModal(user, app_name))

                                async def reject_with_reason_callback(i):
                                    # Check if user has administrator permissions
                                    if not i.user.guild_permissions.administrator:
                                        await i.response.send_message("❌ Only administrators can respond to applications.", ephemeral=True)
                                        return
                                    if applications_status.get(str(user.id), {}).get("responded"):
                                        await i.response.send_message("This application has already been responded to.", ephemeral=True)
                                        return
                                    await i.response.send_modal(RejectReasonModal(user, app_name))

                                accept_button.callback = accept_callback
                                reject_button.callback = reject_callback
                                accept_reason_button.callback = accept_with_reason_callback
                                reject_reason_button.callback = reject_with_reason_callback

                                view.add_item(accept_button)
                                view.add_item(reject_button)
                                view.add_item(accept_reason_button)
                                view.add_item(reject_reason_button)

                                await message.edit(view=view)
                                logging.debug(f"Successfully restored buttons for message {message_id}")
                        except Exception as e:
                            logging.error(f"Error restoring application buttons for user {user_id_str}: {e}")
                            import traceback
                            traceback.print_exc()
            else:
                logging.warning(f"Application log channel not found: {application_log_channel}")
        else:
            logging.debug("No application log channel configured")
    except Exception as e:
        logging.error(f"Error in restore_application_buttons: {e}")
        import traceback
        traceback.print_exc()

async def send_application_response_notification(user, app_name, status, staff_member, feedback=None, reason=None):
    """
    Send application response notification to the configured application response channel.
    This is sent in addition to the DM notification to the applicant.
    """
    global application_response_channel

    if not application_response_channel:
        logging.debug("No application response channel configured - skipping response notification")
        return False

    try:
        response_channel = bot.get_channel(application_response_channel)
        if not response_channel:
            logging.error(f"Application response channel {application_response_channel} not found")
            return False

        # Create response notification embed
        if status == "accepted":
            embed = discord.Embed(
                title="✅ Application Approved",
                color=0x2ECC71  # Green
            )
            status_emoji = "✅"
        else:
            embed = discord.Embed(
                title="❌ Application Declined",
                color=0xE74C3C  # Red
            )
            status_emoji = "❌"

        # Add applicant information
        embed.add_field(
            name="👤 Applicant",
            value=f"{user.mention}\n**Username:** {user.name}#{user.discriminator}\n**ID:** {user.id}",
            inline=True
        )

        # Add application details
        embed.add_field(
            name="📋 Application",
            value=f"**Type:** {app_name}\n**Status:** {status_emoji} {status.title()}",
            inline=True
        )

        # Add staff information
        embed.add_field(
            name="👨‍💼 Processed By",
            value=f"{staff_member.mention}\n**Staff:** {staff_member.name}#{staff_member.discriminator}",
            inline=True
        )

        # Add feedback/reason if provided
        if feedback:
            embed.add_field(
                name="💬 Feedback",
                value=feedback[:1024],  # Discord field limit
                inline=False
            )

        if reason:
            embed.add_field(
                name="📝 Reason",
                value=reason[:1024],  # Discord field limit
                inline=False
            )

        # Add timestamp
        embed.set_footer(text=f"© {datetime.now().year} Application System • Response Notification")
        embed.timestamp = datetime.now()

        # Send notification
        await response_channel.send(embed=embed)
        logging.info(f"Application response notification sent to channel {response_channel.name} for user {user.id} ({app_name} - {status})")
        return True

    except Exception as e:
        logging.error(f"Error sending application response notification: {e}")
        return False

async def send_application_notification(user, embed, app_name, status):
    """
    Send application notification to user via DM with fallback to configured channel.
    Returns True if notification was sent successfully (either DM or fallback).
    """
    global application_fallback_channel

    # Try to send DM first
    try:
        await user.send(embed=embed)
        logging.info(f"Application {status} notification sent via DM to user {user.id} for {app_name}")
        return True
    except discord.errors.Forbidden:
        logging.warning(f"Could not send DM to user {user.id} - DMs may be disabled")

        # Try fallback channel if configured
        if application_fallback_channel:
            try:
                fallback_channel = bot.get_channel(application_fallback_channel)
                if fallback_channel:
                    # Create fallback message with user mention
                    fallback_embed = discord.Embed(
                        title=f"📬 Application Notification",
                        description=f"{user.mention} Your application for **{app_name}** has been **{status}**.",
                        color=embed.color
                    )

                    # Copy the original embed fields
                    for field in embed.fields:
                        fallback_embed.add_field(
                            name=field.name,
                            value=field.value,
                            inline=field.inline
                        )

                    # Add fallback notice
                    fallback_embed.add_field(
                        name="📋 Notification Method",
                        value="This notification was sent here because your DMs are disabled. Please enable DMs for future notifications.",
                        inline=False
                    )

                    fallback_embed.set_footer(text=f"© {datetime.now().year} Application System • Fallback Notification")
                    fallback_embed.timestamp = datetime.now()

                    await fallback_channel.send(embed=fallback_embed)
                    logging.info(f"Sent application response to fallback channel for user {user.id} - DMs disabled")
                    return True
                else:
                    logging.error(f"Fallback channel {application_fallback_channel} not found")
            except Exception as fallback_error:
                logging.error(f"Error sending to fallback channel: {fallback_error}")
        else:
            logging.warning(f"No fallback channel configured for user {user.id} DM failure")

        return False
    except Exception as dm_error:
        logging.error(f"Error sending DM to user {user.id}: {dm_error}")
        return False

async def handle_application_response(interaction, user, status, app_name):
    """Handle application response (accept/reject) with professional formatting"""
    try:
        user_id_str = str(user.id)
        current_time = datetime.now(timezone.utc)

        # Check if application has already been responded to
        if applications_status.get(user_id_str, {}).get("responded"):
            try:
                await interaction.response.send_message("This application has already been processed.", ephemeral=True)
            except discord.errors.InteractionResponded:
                await interaction.followup.send("This application has already been processed.", ephemeral=True)
            return

        # Update application status with detailed information
        applications_status[user_id_str] = {
            "responded": True,
            "admin": interaction.user.id,
            "admin_name": f"{interaction.user.name}#{interaction.user.discriminator}",
            "status": status,
            "response_time": current_time.isoformat(),
            "application_name": app_name
        }
        await save_data()

        # Create professional response for admin
        if status == "accepted":
            admin_embed = discord.Embed(
                title="Application Approved",
                description=f"You have approved the application for {user.mention}.",
                color=0x2ECC71  # Professional green color
            )
        else:
            admin_embed = discord.Embed(
                title="Application Declined",
                description=f"You have declined the application for {user.mention}.",
                color=0x95A5A6  # Professional gray color
            )

        admin_embed.add_field(name="Application Type", value=app_name, inline=False)
        admin_embed.add_field(
            name="Note",
            value="For more detailed feedback, use the 'Approve with Feedback' or 'Decline with Feedback' options.",
            inline=False
        )
        admin_embed.set_footer(text=f"Processed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} UTC")

        # Send response to admin
        try:
            await interaction.response.send_message(embed=admin_embed, ephemeral=True)
        except discord.errors.InteractionResponded:
            await interaction.followup.send(embed=admin_embed, ephemeral=True)

        # Create professional notification for applicant
        if status == "accepted":
            user_embed = discord.Embed(
                title="Application Approved",
                description=f"Congratulations! Your application for **{app_name}** has been approved.",
                color=0x2ECC71  # Professional green color
            )

            # Add next steps section
            user_embed.add_field(
                name="Next Steps",
                value="• A staff member will contact you with further instructions\n• Please ensure your DMs are open\n• Contact staff if you have any questions",
                inline=False
            )
        else:
            user_embed = discord.Embed(
                title="Application Status Update",
                description=f"Thank you for your interest in the **{app_name}** position. After review, we regret to inform you that your application has not been approved at this time.",
                color=0x95A5A6  # Professional gray color
            )

            # Add encouragement section
            user_embed.add_field(
                name="Next Steps",
                value="• We encourage you to apply again in the future\n• Consider requesting feedback to improve your next application\n• Thank you for your interest in our community",
                inline=False
            )

        # Add footer with timestamp
        user_embed.set_footer(text=f"© {datetime.now().year} Application System")
        user_embed.timestamp = current_time

        # Send notification to applicant using fallback system
        notification_sent = await send_application_notification(user, user_embed, app_name, status)
        if notification_sent:
            console_log(f"Application {status}: {user.name} ({app_name})", "SUCCESS")
        else:
            console_log(f"Application {status} notification failed: {user.name} ({app_name})", "WARNING")

        # Send application response notification to configured channel
        response_notification_sent = await send_application_response_notification(
            user, app_name, status, interaction.user
        )
        if response_notification_sent:
            logging.info(f"Application response notification sent for {user.name} ({app_name} - {status})")
        else:
            logging.debug(f"Application response notification not sent (channel not configured or error occurred)")

    except discord.errors.InteractionResponded:
        # This is a fallback in case the interaction was already responded to
        logging.warning("Interaction was already responded to")
        try:
            await interaction.followup.send("Processing your response...", ephemeral=True)
        except:
            pass
    except Exception as e:
        logging.error(f"Error handling application response: {e}")
        try:
            await interaction.response.send_message("An error occurred while processing your response.", ephemeral=True)
        except discord.errors.InteractionResponded:
            try:
                await interaction.followup.send("An error occurred while processing your response.", ephemeral=True)
            except:
                pass


async def create_backup():
    try:
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        backup_path = f"backups/backup_{timestamp}.json"

        # Create backups directory if it doesn't exist
        os.makedirs("backups", exist_ok=True)

        # Check if the data file exists
        if not os.path.exists(DATA_FILE_PATH):
            # Create an empty data file
            with open(DATA_FILE_PATH, 'w') as f:
                json.dump({}, f)
            logging.info(f"Created empty data file at {DATA_FILE_PATH}")
            data = {}
        else:
            # Load data from existing file
            with open(DATA_FILE_PATH, 'r') as source:
                data = json.load(source)

        # Create the backup
        with open(backup_path, 'w') as backup:
            json.dump(data, backup, indent=4)

        logging.info(f"Backup created: {backup_path}")
    except Exception as e:
        logging.error(f"Backup failed: {e}")

@bot.event
async def on_interaction(interaction: discord.Interaction):
    if interaction.type == discord.InteractionType.component:
        try:
            custom_id = interaction.data.get("custom_id", "")

            # Handle basic ticket creation button
            if custom_id == "create_ticket":
                # Create category selection buttons
                view = View()

                # Get available categories
                categories = ticket_config.get("categories", {})
                if not categories:
                    await interaction.response.send_message("No ticket categories are configured!", ephemeral=True)
                    return

                # Create a button for each category
                for category_id, category_info in categories.items():
                    button = Button(
                        label=category_info["name"],
                        custom_id=f"ticket_category_{category_id}",
                        style=discord.ButtonStyle.primary
                    )
                    view.add_item(button)

                # Send category selection message
                await interaction.response.send_message(
                    "Please select a ticket category:",
                    view=view,
                    ephemeral=True
                )
                return

            # NOTE: persistent_ticket_ buttons are handled by their own callback methods in PersistentTicketCategoryButton
            # Removed global handler to prevent duplicate interaction responses

            # Handle legacy ticket category button selections (fallback)
            elif custom_id.startswith("ticket_category_"):
                try:
                    # Extract category_id and keep it as string
                    category_id = custom_id.split("_")[-1]

                    # Debug prints
                    print(f"Received ticket category interaction for category: {category_id}")
                    print(f"Available categories: {ticket_config.get('categories', {})}")

                    # Verify category exists using string comparison
                    if category_id not in ticket_config.get("categories", {}):
                        await interaction.response.send_message("Invalid ticket category! Please contact an administrator.", ephemeral=True)
                        return

                    # Convert to int only when passing to create_ticket
                    await create_ticket(interaction, int(category_id))
                    return
                except ValueError as e:
                    print(f"Error parsing category ID: {e}")
                    await interaction.response.send_message("Invalid ticket category format.", ephemeral=True)
                    return
                except Exception as e:
                    print(f"Error creating ticket: {e}")
                    await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                    return

            # Handle dropdown menu selections for tickets
            elif custom_id in ["ticket_category_select", "ticket_category_dropdown_v2", "modern_ticket_dropdown_v3"] and "values" in interaction.data:
                try:
                    # Extract category_id from the dropdown selection
                    category_id = interaction.data["values"][0]

                    # Debug logging
                    print(f"Processing ticket category from dropdown: {category_id}")
                    print(f"Available categories: {ticket_config.get('categories', {})}")

                    # Convert both to strings for comparison
                    categories = {str(k): v for k, v in ticket_config.get("categories", {}).items()}

                    # Verify category exists
                    if category_id not in categories:
                        print(f"Category {category_id} not found in config")
                        await interaction.response.send_message(
                            "This ticket category no longer exists. Please contact an administrator.",
                            ephemeral=True
                        )
                        return

                    # Convert to int for create_ticket function
                    await create_ticket(interaction, int(category_id))
                    return
                except ValueError as e:
                    print(f"Error parsing category ID from dropdown: {e}")
                    await interaction.response.send_message("Invalid ticket category format.", ephemeral=True)
                    return
                except Exception as e:
                    print(f"Error creating ticket from dropdown: {e}")
                    traceback.print_exc()
                    await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                    return

            # NOTE: Application button interactions are now handled by their button callbacks in log_application
            # Removed duplicate handlers to prevent "Interaction has already been acknowledged" errors
            # Only handle Create Ticket button here since it doesn't have a callback assigned


            elif custom_id.startswith("app_create_ticket_"):
                parts = custom_id.split("_")
                if len(parts) >= 4:
                    user_id = parts[3]
                    try:
                        user = await bot.fetch_user(int(user_id))
                        if user:
                            app_name = applications_status.get(str(user_id), {}).get("application_name", "Unknown")

                            # Create category selection buttons for ticket creation
                            view = View()
                            categories = ticket_config.get("categories", {})

                            if not categories:
                                await interaction.response.send_message(
                                    "No ticket categories are configured! Please contact an administrator.",
                                    ephemeral=True
                                )
                                return

                            # Create a button for each category with professional styling
                            for category_id, category_info in categories.items():
                                button = Button(
                                    label=category_info["name"],
                                    custom_id=f"app_ticket_cat_{user_id}_{category_id}",
                                    style=discord.ButtonStyle.primary,
                                    emoji="🎫"
                                )
                                view.add_item(button)

                            # Create professional embed for category selection matching main ticket system
                            embed = discord.Embed(
                                title="🎫 Create Application Ticket",
                                description=(
                                    f"Creating a support ticket for **{user.display_name}**'s {app_name} application.\n\n"
                                    f"Please select the appropriate category below to proceed with ticket creation."
                                ),
                                color=0x000000  # Professional black theme matching main system
                            )

                            # Add elegant separator for visual hierarchy (matching main system)
                            embed.add_field(
                                name="\u200b",  # Invisible field name for spacing
                                value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
                                inline=False
                            )

                            # Add application context section with professional formatting
                            embed.add_field(
                                name="**Application Context**",
                                value=f"**Applicant:** {user.display_name} (@{user.name})\n**Application Type:** {app_name}",
                                inline=False
                            )

                            # Add elegant separator before categories (matching main system)
                            embed.add_field(
                                name="\u200b",  # Invisible field name for spacing
                                value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
                                inline=False
                            )

                            # Add category descriptions with professional formatting
                            embed.add_field(
                                name="**Available Categories**",
                                value="Select the most appropriate category for this application follow-up:",
                                inline=False
                            )

                            for category_id, category_info in categories.items():
                                embed.add_field(
                                    name=f"🎫 **{category_info['name']}**",
                                    value=f"```{category_info.get('description', 'No description available')}```",
                                    inline=False
                                )

                            # Add elegant separator at the bottom (matching main system)
                            embed.add_field(
                                name="\u200b",  # Invisible field name for spacing
                                value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
                                inline=False
                            )

                            # Add professional footer with timestamp
                            current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
                            embed.set_footer(text=f"Application Ticket Creation • {current_time}")

                            await interaction.response.send_message(
                                embed=embed,
                                view=view,
                                ephemeral=True
                            )
                        else:
                            await interaction.response.send_message("Could not find user for this application.", ephemeral=True)
                    except Exception as e:
                        logging.error(f"Error creating ticket for application: {e}")
                        await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                return

            # Handle application ticket category selection
            elif custom_id.startswith("app_ticket_cat_"):
                parts = custom_id.split("_")
                if len(parts) >= 5:
                    user_id = parts[3]
                    category_id = parts[4]
                    try:
                        user = await bot.fetch_user(int(user_id))
                        app_name = applications_status.get(str(user_id), {}).get("application_name", "Unknown")

                        # Create the ticket with application context
                        from tickets import create_application_ticket
                        success = await create_application_ticket(interaction, int(category_id), user, app_name)

                        if success:
                            await interaction.response.send_message(
                                f"✅ Ticket created successfully for {user.display_name}'s {app_name} application!",
                                ephemeral=True
                            )
                        else:
                            await interaction.response.send_message(
                                "❌ Failed to create ticket. Please try again.",
                                ephemeral=True
                            )
                    except Exception as e:
                        logging.error(f"Error creating application ticket: {e}")
                        await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                return

            # Handle ticket interactions
            elif custom_id == "create_ticket":
                try:
                    # Create category selection buttons
                    view = View()

                    # Get available categories and ensure it exists
                    categories = ticket_config.get("categories", {})
                    if not categories:
                        await interaction.response.send_message(
                            "No ticket categories are configured! Please contact an administrator.",
                            ephemeral=True
                        )
                        return

                    # Debug log
                    print(f"Available categories when creating buttons: {categories}")

                    # Create a button for each category
                    for category_id, category_info in categories.items():
                        button = Button(
                            label=category_info["name"],
                            # Store category_id as string in custom_id
                            custom_id=f"ticket_category_{str(category_id)}",
                            style=discord.ButtonStyle.primary
                        )
                        view.add_item(button)

                    # Create an embed for category selection
                    embed = discord.Embed(
                        title="🎫 Create a Ticket",
                        description="Please select a category for your ticket:",
                        color=discord.Color.blue()
                    )

                    # Add category descriptions to embed
                    for category_id, category_info in categories.items():
                        embed.add_field(
                            name=category_info["name"],
                            value=category_info.get("description", "No description available"),
                            inline=False
                        )

                    await interaction.response.send_message(
                        embed=embed,
                        view=view,
                        ephemeral=True
                    )

                except Exception as e:
                    print(f"Error creating ticket selection: {e}")
                    await interaction.response.send_message(
                        "An error occurred while creating the ticket selection. Please try again later.",
                        ephemeral=True
                    )

            elif custom_id.startswith("ticket_category_predefined_"):
                try:
                    # Handle predefined GTA RP category buttons
                    predefined_id = custom_id.split("ticket_category_predefined_")[-1]

                    # Map predefined categories to their names for ticket creation
                    predefined_categories = {
                        "1": "Report a Player",
                        "2": "Refund Request",
                        "3": "Bug Report",
                        "4": "Apply for Whitelist",
                        "5": "General Help"
                    }

                    if predefined_id in predefined_categories:
                        category_name = predefined_categories[predefined_id]

                        # Create a temporary category for the ticket system
                        # Use negative IDs to distinguish from regular categories
                        temp_category_id = -int(predefined_id)

                        # Add to ticket_config temporarily if not exists
                        if str(temp_category_id) not in ticket_config.get("categories", {}):
                            if "categories" not in ticket_config:
                                ticket_config["categories"] = {}
                            ticket_config["categories"][str(temp_category_id)] = {
                                "name": category_name,
                                "description": f"GTA RP {category_name} support"
                            }

                        await create_ticket(interaction, temp_category_id)
                        return
                    else:
                        await interaction.response.send_message(
                            "Invalid ticket category! Please contact an administrator.",
                            ephemeral=True
                        )
                        return

                except Exception as e:
                    print(f"Error handling predefined ticket category: {e}")
                    await interaction.response.send_message(
                        "An error occurred while creating the ticket.",
                        ephemeral=True
                    )
                    return

            # NOTE: Removed duplicate persistent_ticket_ handler to prevent interaction conflicts

            elif custom_id.startswith("ticket_category_"):
                try:
                    # Extract category_id as string
                    category_id = custom_id.split("ticket_category_")[-1]

                    # Debug logging
                    print(f"Processing ticket category: {category_id}")
                    print(f"Available categories: {ticket_config.get('categories', {})}")

                    # Convert both to strings for comparison
                    categories = {str(k): v for k, v in ticket_config.get("categories", {}).items()}

                    # Verify category exists
                    if category_id not in categories:
                        print(f"Category {category_id} not found in config")
                        await interaction.response.send_message(
                            "This ticket category no longer exists. Please contact an administrator.",
                            ephemeral=True
                        )
                        return

                    # Convert to int for create_ticket function
                    await create_ticket(interaction, int(category_id))
                    return

                except ValueError as e:
                    print(f"Error parsing category ID: {e}")
                    await interaction.response.send_message("Invalid ticket category format.", ephemeral=True)
                    return
                except Exception as e:
                    print(f"Error creating ticket: {e}")
                    await interaction.response.send_message("An error occurred while creating the ticket.", ephemeral=True)
                    return

            elif custom_id == "close_ticket":
                try:
                    await interaction.response.defer(ephemeral=True)
                    success, error = await close_ticket(interaction.channel_id, closer=interaction.user)
                    if not success:
                        await interaction.followup.send(f"Error closing ticket: {error}", ephemeral=True)
                except Exception as e:
                    print(f"Error closing ticket: {e}")
                    try:
                        await interaction.followup.send(
                            "An error occurred while closing the ticket. Please try again.",
                            ephemeral=True
                        )
                    except discord.NotFound:
                        pass

            elif custom_id == "close_with_reason":
                try:
                    # Check if user has staff role
                    has_staff_role = False
                    for role_id in ticket_config.get("staff_roles", []):
                        role = interaction.guild.get_role(role_id)
                        if role and role in interaction.user.roles:
                            has_staff_role = True
                            break

                    if not has_staff_role:
                        await interaction.response.send_message(
                            "Only staff members can close tickets with reasons.",
                            ephemeral=True
                        )
                        return

                    # Import and use the new CloseWithReasonModal
                    from tickets import CloseWithReasonModal
                    modal = CloseWithReasonModal()
                    await interaction.response.send_modal(modal)

                except Exception as e:
                    print(f"Error handling close with reason: {e}")
                    await interaction.response.send_message(
                        "An error occurred while processing your request.",
                        ephemeral=True
                    )

            elif custom_id == "reopen_ticket":
                try:
                    await interaction.response.defer(ephemeral=True)

                    # Check if user has staff role
                    has_staff_role = False
                    for role_id in ticket_config["staff_roles"]:
                        role = interaction.guild.get_role(role_id)
                        if role and role in interaction.user.roles:
                            has_staff_role = True
                            break

                    if not has_staff_role:
                        await interaction.followup.send("You don't have permission to reopen tickets.", ephemeral=True)
                        return

                    success, error = await reopen_ticket(interaction.channel_id, interaction.user)
                    if not success:
                        await interaction.followup.send(f"Error reopening ticket: {error}", ephemeral=True)


                except Exception as e:
                    print(f"Error reopening ticket: {e}")
                    try:
                        await interaction.followup.send(
                            "An error occurred while reopening the ticket. Please try again.",
                            ephemeral=True
                        )
                    except discord.NotFound:
                        pass

            elif custom_id == "view_transcript_channel":
                try:
                    # Import and use the new channel transcript handler
                    from tickets import handle_transcript_button_channel
                    await handle_transcript_button_channel(interaction)
                except Exception as e:
                    print(f"Error viewing transcript in channel: {e}")
                    try:
                        if not interaction.response.is_done():
                            await interaction.response.send_message(
                                "An error occurred while viewing the transcript.",
                                ephemeral=True
                            )
                        else:
                            await interaction.followup.send(
                                "An error occurred while viewing the transcript.",
                                ephemeral=True
                            )
                    except discord.NotFound:
                        pass

            elif custom_id == "claim_ticket":
                try:
                    # Check if user has staff role
                    has_staff_role = False
                    for role_id in ticket_config["staff_roles"]:
                        role = interaction.guild.get_role(role_id)
                        if role and role in interaction.user.roles:
                            has_staff_role = True
                            break

                    if not has_staff_role:
                        await interaction.response.send_message("You don't have permission to claim tickets.", ephemeral=True)
                        return

                    success, error = await claim_ticket(interaction.channel_id, interaction.user)
                    if not success:
                        await interaction.response.send_message(f"Error claiming ticket: {error}", ephemeral=True)
                    else:
                        await interaction.response.send_message("Ticket claimed successfully!", ephemeral=True)
                except Exception as e:
                    print(f"Error claiming ticket: {e}")
                    await interaction.response.send_message("An error occurred while claiming the ticket.", ephemeral=True)

        except Exception as e:
            print(f"Error in interaction handler: {e}")
            try:
                await interaction.response.send_message("An error occurred while processing your request.", ephemeral=True)
            except:
                pass

if __name__ == "__main__":
    bot.run(token)