#!/usr/bin/env python3
"""
Test script to verify the Discord bot application fixes:
1. Embed fields limit fix (25 field limit)
2. DM error handling for disabled DMs

This script tests the fixes without requiring a live Discord bot.
"""

import asyncio
import discord
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

class MockUser:
    """Mock Discord user for testing"""
    def __init__(self, user_id=12345, name="TestUser", discriminator="0001", dm_enabled=True):
        self.id = user_id
        self.name = name
        self.discriminator = discriminator
        self.mention = f"<@{user_id}>"
        self.avatar = Mock()
        self.avatar.url = "https://example.com/avatar.png"
        self._dm_enabled = dm_enabled
    
    async def send(self, *args, **kwargs):
        """Mock send method that can simulate DM failures"""
        if not self._dm_enabled:
            raise discord.errors.Forbidden(Mock(), "Cannot send messages to this user")
        print(f"[MOCK DM] Sent message to {self.name}: {args[0] if args else kwargs}")
        return Mock()

class MockChannel:
    """Mock Discord channel for testing"""
    def __init__(self, channel_id=67890):
        self.id = channel_id
    
    async def send(self, *args, **kwargs):
        """Mock send method for channel"""
        print(f"[MOCK CHANNEL] Sent message: {args[0] if args else kwargs}")
        return Mock()

def create_test_application_data(num_questions=15):
    """Create test application data with specified number of questions"""
    questions = [f"Test question {i+1}: What is your experience with topic {i+1}?" for i in range(num_questions)]
    answers = [f"Test answer {i+1}: I have extensive experience with topic {i+1} and can provide detailed examples." for i in range(num_questions)]
    return questions, answers

async def test_embed_fields_limit():
    """Test that the embed fields limit fix works correctly"""
    print("\n=== Testing Embed Fields Limit Fix ===")
    
    # Import the log_application function
    import sys
    sys.path.append('.')
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.applications_status', {}) as mock_status, \
         patch('bot.save_data', AsyncMock()) as mock_save:
        
        mock_bot.get_channel.return_value = MockChannel()
        
        # Import after patching
        from bot import log_application
        
        # Test with many questions (should trigger condensed format)
        print("Testing with 15 questions (should condense)...")
        user = MockUser()
        questions, answers = create_test_application_data(15)
        
        try:
            await log_application(user, "Test Application", questions, answers, 67890)
            print("✅ Successfully handled 15 questions without exceeding field limit")
        except Exception as e:
            print(f"❌ Failed with 15 questions: {e}")
        
        # Test with few questions (should use original format)
        print("Testing with 5 questions (should use original format)...")
        questions, answers = create_test_application_data(5)
        
        try:
            await log_application(user, "Test Application", questions, answers, 67890)
            print("✅ Successfully handled 5 questions with original format")
        except Exception as e:
            print(f"❌ Failed with 5 questions: {e}")

async def test_dm_error_handling():
    """Test that DM error handling works correctly"""
    print("\n=== Testing DM Error Handling ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.applications_status', {}) as mock_status, \
         patch('bot.save_data', AsyncMock()) as mock_save:
        
        mock_bot.get_channel.return_value = MockChannel()
        
        # Import after patching
        from bot import log_application
        
        # Test with user who has DMs disabled
        print("Testing with DMs disabled...")
        user_no_dm = MockUser(dm_enabled=False)
        questions, answers = create_test_application_data(3)
        
        try:
            await log_application(user_no_dm, "Test Application", questions, answers, 67890)
            print("✅ Successfully handled DM failure gracefully")
        except discord.errors.Forbidden:
            print("❌ DM error was not handled properly")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        # Test with user who has DMs enabled
        print("Testing with DMs enabled...")
        user_with_dm = MockUser(dm_enabled=True)
        
        try:
            await log_application(user_with_dm, "Test Application", questions, answers, 67890)
            print("✅ Successfully handled normal DM flow")
        except Exception as e:
            print(f"❌ Failed with DMs enabled: {e}")

async def test_field_count_calculation():
    """Test that field count calculation is correct"""
    print("\n=== Testing Field Count Calculation ===")
    
    # Test the logic for determining when to condense
    test_cases = [
        (5, False, "5 questions should use original format"),
        (10, False, "10 questions should use original format (exactly at limit)"),
        (11, True, "11 questions should use condensed format"),
        (20, True, "20 questions should use condensed format"),
    ]
    
    for num_questions, should_condense, description in test_cases:
        # Calculate fields: 5 fixed + (2 × questions)
        total_fields = 5 + (2 * num_questions)
        will_condense = num_questions > 10
        
        if will_condense == should_condense:
            print(f"✅ {description} - {total_fields} total fields")
        else:
            print(f"❌ {description} - Logic error!")

async def main():
    """Run all tests"""
    print("Starting Discord Bot Application Fixes Tests")
    print("=" * 50)
    
    await test_field_count_calculation()
    await test_embed_fields_limit()
    await test_dm_error_handling()
    
    print("\n" + "=" * 50)
    print("Tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
