#!/usr/bin/env python3
"""
Test script for application response notification system.
This script tests the new dual notification system for application responses.
"""

import asyncio
import discord
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

class MockUser:
    def __init__(self, user_id=12345, name="TestUser", discriminator="0001", dm_enabled=True):
        self.id = user_id
        self.name = name
        self.discriminator = discriminator
        self.mention = f"<@{user_id}>"
        self.dm_enabled = dm_enabled
    
    async def send(self, embed=None, content=None):
        if not self.dm_enabled:
            raise discord.errors.Forbidden(MagicMock(), "Cannot send messages to this user")
        print(f"✅ DM sent to {self.name}: {embed.title if embed else content}")
        return MagicMock()

class MockChannel:
    def __init__(self, channel_id=67890, name="test-channel"):
        self.id = channel_id
        self.name = name
        self.mention = f"<#{channel_id}>"
    
    async def send(self, embed=None, content=None):
        print(f"✅ Channel message sent to #{self.name}: {embed.title if embed else content}")
        return MagicMock()

class MockInteraction:
    def __init__(self, user, is_admin=True):
        self.user = user
        self.guild_permissions = MagicMock()
        self.guild_permissions.administrator = is_admin
        self.response = MagicMock()
        self.followup = MagicMock()
        
        # Mock response methods
        self.response.send_message = AsyncMock()
        self.followup.send = AsyncMock()

async def test_application_response_notification():
    """Test the new application response notification function"""
    print("\n=== Testing Application Response Notification Function ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890) as mock_response_channel:
        
        response_channel = MockChannel(67890, "application-responses")
        mock_bot.get_channel.return_value = response_channel
        
        # Import after patching
        from bot import send_application_response_notification
        
        # Test successful notification
        print("Testing successful response notification...")
        user = MockUser()
        staff_member = MockUser(54321, "StaffMember", "0002")
        
        try:
            result = await send_application_response_notification(
                user, "Test Application", "accepted", staff_member, feedback="Great application!"
            )
            if result:
                print("✅ Response notification sent successfully")
            else:
                print("❌ Response notification failed unexpectedly")
        except Exception as e:
            print(f"❌ Response notification test failed: {e}")
        
        # Test with rejection
        print("Testing rejection notification...")
        try:
            result = await send_application_response_notification(
                user, "Test Application", "rejected", staff_member, 
                feedback="Needs improvement", reason="Insufficient experience"
            )
            if result:
                print("✅ Rejection notification sent successfully")
            else:
                print("❌ Rejection notification failed unexpectedly")
        except Exception as e:
            print(f"❌ Rejection notification test failed: {e}")
        
        # Test without configured channel
        print("Testing without configured response channel...")
        with patch('bot.application_response_channel', None):
            try:
                result = await send_application_response_notification(
                    user, "Test Application", "accepted", staff_member
                )
                if not result:
                    print("✅ Gracefully handled missing response channel")
                else:
                    print("❌ Should have failed without response channel")
            except Exception as e:
                print(f"❌ No response channel test failed: {e}")

async def test_dual_notification_system():
    """Test that both DM and response channel notifications work together"""
    print("\n=== Testing Dual Notification System ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890), \
         patch('bot.applications_status', {}) as mock_status, \
         patch('bot.save_data', AsyncMock()) as mock_save:
        
        response_channel = MockChannel(67890, "application-responses")
        mock_bot.get_channel.return_value = response_channel
        
        # Import after patching
        from bot import handle_application_response
        
        # Test normal flow with both notifications
        print("Testing dual notification flow...")
        user = MockUser()
        staff_member = MockUser(54321, "StaffMember", "0002")
        interaction = MockInteraction(staff_member, is_admin=True)
        
        try:
            await handle_application_response(interaction, user, "accepted", "Test Application")
            print("✅ Dual notification system handled successfully")
        except Exception as e:
            print(f"❌ Dual notification test failed: {e}")

async def test_modal_integration():
    """Test that modal responses also send response notifications"""
    print("\n=== Testing Modal Integration ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_response_channel', 67890), \
         patch('bot.applications_status', {}) as mock_status, \
         patch('bot.save_data', AsyncMock()) as mock_save:
        
        response_channel = MockChannel(67890, "application-responses")
        mock_bot.get_channel.return_value = response_channel
        
        # Import after patching
        from bot import AcceptReasonModal, RejectReasonModal
        
        # Test AcceptReasonModal
        print("Testing AcceptReasonModal integration...")
        user = MockUser()
        staff_member = MockUser(54321, "StaffMember", "0002")
        interaction = MockInteraction(staff_member, is_admin=True)
        
        try:
            modal = AcceptReasonModal(user, "Test Application")
            # Simulate form submission
            modal.children = [MagicMock(value="Great work!"), MagicMock(value="Internal note")]
            await modal.on_submit(interaction)
            print("✅ AcceptReasonModal integration successful")
        except Exception as e:
            print(f"❌ AcceptReasonModal test failed: {e}")
        
        # Test RejectReasonModal
        print("Testing RejectReasonModal integration...")
        try:
            modal = RejectReasonModal(user, "Test Application")
            # Simulate form submission
            modal.children = [
                MagicMock(value="Needs improvement"), 
                MagicMock(value="More experience needed"),
                MagicMock(value="Internal note")
            ]
            await modal.on_submit(interaction)
            print("✅ RejectReasonModal integration successful")
        except Exception as e:
            print(f"❌ RejectReasonModal test failed: {e}")

async def test_configuration_commands():
    """Test that configuration commands work correctly"""
    print("\n=== Testing Configuration Commands ===")
    
    # Mock the bot and required globals
    with patch('bot.bot') as mock_bot, \
         patch('bot.application_log_channel', None), \
         patch('bot.application_response_channel', None), \
         patch('bot.save_data_optimized', AsyncMock()) as mock_save:
        
        log_channel = MockChannel(12345, "application-logs")
        response_channel = MockChannel(67890, "application-responses")
        mock_bot.get_channel.side_effect = lambda cid: {
            12345: log_channel,
            67890: response_channel
        }.get(cid)
        
        # Import after patching
        from bot import set_application_log_channel, check_application_config
        
        # Test setting channels
        print("Testing channel configuration...")
        staff_member = MockUser(54321, "StaffMember", "0002")
        interaction = MockInteraction(staff_member, is_admin=True)
        
        try:
            await set_application_log_channel(interaction, log_channel, response_channel)
            print("✅ Channel configuration successful")
        except Exception as e:
            print(f"❌ Channel configuration test failed: {e}")
        
        # Test configuration check
        print("Testing configuration check...")
        try:
            await check_application_config(interaction)
            print("✅ Configuration check successful")
        except Exception as e:
            print(f"❌ Configuration check test failed: {e}")

async def main():
    """Run all tests"""
    print("🧪 Application Response Notification System Tests")
    print("=" * 60)
    
    await test_application_response_notification()
    await test_dual_notification_system()
    await test_modal_integration()
    await test_configuration_commands()
    
    print("\n📊 Test Summary")
    print("=" * 50)
    print("✅ All tests completed. Check output above for any failures.")
    print("\n💡 Next Steps:")
    print("1. Start the bot and test with real Discord interactions")
    print("2. Configure application response channel using /set_application_log_channel")
    print("3. Test application approval/rejection to verify notifications")

if __name__ == "__main__":
    asyncio.run(main())
