import discord
from discord import app_commands
from discord.ui import Button, View, Modal, TextInput
import discord.ui
import asyncio
import json
import os
import traceback
import logging
from datetime import datetime, timezone
from collections import defaultdict, deque
import asyncio
import time
import random
import pymongo
from bson import ObjectId
import re
import aiohttp
from typing import Dict, Any, Optional, Tuple, List
import io

from bot_instance import bot
from panel_customization_manager import PanelCustomizationManager
from ticket_error_handler import ticket_error_handler, ValidationResult, ErrorSeverity

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ticket_system')

# MongoDB setup
try:
    mongo_client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = mongo_client['missminutesbot']
    transcript_collection = db['transcripts']
    ticket_config_collection = db['ticket_config']
    print("Successfully connected to MongoDB")
except Exception as e:
    print(f"Error connecting to MongoDB: {e}")
    traceback.print_exc()

class TicketRateLimitHandler:
    def __init__(self):
        self.rate_limits = {}
        self.queues = {}
        self.processing = {}
        self.max_retries = 15  # Increased retry limit
        self.base_delay = 1.0  # Start with 1 second delay
        self.max_delay = 600.0  # Maximum delay of 10 minutes
        self.bulk_queue = asyncio.Queue()
        self.bulk_processing = False
        self.batch_sizes = defaultdict(lambda: 10)  # Default batch size of 10
        self.success_threshold = 5  # Number of successful operations before increasing batch size
        self.success_counts = defaultdict(int)

    async def execute(self, key, coroutine, *args, **kwargs):
        """Execute a coroutine with enhanced rate limit handling"""
        if key not in self.queues:
            self.queues[key] = asyncio.Queue()
            self.processing[key] = False

        await self.queues[key].put((coroutine, args, kwargs))

        if not self.processing[key]:
            self.processing[key] = True
            asyncio.create_task(self._process_queue(key))

    async def execute_bulk(self, key, operations):
        """Handle bulk operations with dynamic batch sizing"""
        for op in operations:
            await self.bulk_queue.put((key, op))

        if not self.bulk_processing:
            self.bulk_processing = True
            asyncio.create_task(self._process_bulk_queue())

    async def _process_bulk_queue(self):
        """Process bulk operations with smart batching"""
        try:
            while not self.bulk_queue.empty():
                key = None
                batch = []
                batch_size = self.batch_sizes[key]

                # Gather batch of operations
                while len(batch) < batch_size and not self.bulk_queue.empty():
                    key, op = await self.bulk_queue.get()
                    batch.append(op)

                if batch:
                    try:
                        # Execute batch with retry logic
                        success = await self._execute_with_backoff(key, batch)

                        # Adjust batch size based on success/failure
                        if success:
                            self.success_counts[key] += 1
                            if self.success_counts[key] >= self.success_threshold:
                                self.batch_sizes[key] = min(50, self.batch_sizes[key] + 5)
                                self.success_counts[key] = 0
                        else:
                            self.batch_sizes[key] = max(1, self.batch_sizes[key] // 2)
                            self.success_counts[key] = 0

                    except Exception as e:
                        print(f"Error in bulk processing: {e}")
                        self.batch_sizes[key] = max(1, self.batch_sizes[key] // 2)

                    # Add delay between batches with jitter
                    jitter = random.uniform(0, 0.1)
                    await asyncio.sleep(1.0 + jitter)

        finally:
            self.bulk_processing = False

    async def _process_queue(self, key):
        """Process queued items with enhanced rate limiting"""
        try:
            while not self.queues[key].empty():
                if key in self.rate_limits:
                    wait_time = self.rate_limits[key] - time.time()
                    if wait_time > 0:
                        jitter = random.uniform(0, 0.1 * wait_time)
                        await asyncio.sleep(wait_time + jitter)

                coroutine, args, kwargs = await self.queues[key].get()

                success = False
                for attempt in range(self.max_retries):
                    try:
                        await coroutine(*args, **kwargs)
                        success = True
                        break
                    except discord.HTTPException as e:
                        if e.status == 429:  # Rate limit
                            retry_after = e.retry_after if hasattr(e, 'retry_after') else None
                            if retry_after is None:
                                retry_after = min(self.max_delay, self.base_delay * (2 ** attempt))

                            jitter = random.uniform(0, 0.1 * retry_after)
                            total_delay = retry_after + jitter

                            self.rate_limits[key] = time.time() + total_delay
                            print(f"Rate limited on {key}, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{self.max_retries})")
                            await asyncio.sleep(total_delay)
                            continue
                        raise
                    except AttributeError as e:
                        if "'str' object has no attribute 'to_dict'" in str(e):
                            # Skip this error - it's a known issue with Discord object serialization
                            logger.debug(f"Skipping serialization error in operation: {e}")
                            break  # Exit retry loop for this specific error
                        else:
                            logger.error(f"AttributeError in operation: {e}")
                            if attempt == self.max_retries - 1:
                                raise
                    except Exception as e:
                        logger.error(f"Error in operation: {e}")
                        if attempt == self.max_retries - 1:
                            raise

                        backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
                        await asyncio.sleep(backoff)

                if success:
                    # Add successful operation delay with jitter
                    jitter = random.uniform(0, 0.1)
                    await asyncio.sleep(0.5 + jitter)

        finally:
            self.processing[key] = False

    async def _execute_with_backoff(self, key, operations):
        """Execute operations with exponential backoff"""
        for attempt in range(self.max_retries):
            try:
                for op in operations:
                    await op()
                return True
            except discord.HTTPException as e:
                if e.status == 429:
                    retry_after = e.retry_after if hasattr(e, 'retry_after') else None
                    if retry_after is None:
                        retry_after = min(self.max_delay, self.base_delay * (2 ** attempt))

                    jitter = random.uniform(0, 0.1 * retry_after)
                    total_delay = retry_after + jitter

                    print(f"Rate limited in bulk operation, waiting {total_delay:.2f}s")
                    await asyncio.sleep(total_delay)
                    continue
                raise
        return False

# Create global instance
ticket_rate_limiter = TicketRateLimitHandler()

# Enhanced Transcript System Classes
class AdvancedTranscriptFormatter:
    """Advanced transcript formatter with rich Discord embed support and professional styling"""

    def __init__(self):
        self.timezone = timezone.utc
        self.max_embed_length = 4096
        self.max_field_length = 1024
        self.max_embed_fields = 25

        # Color scheme for professional black theme
        self.colors = {
            'primary': 0x000000,      # Professional black
            'staff': 0x5865F2,        # Discord blurple for staff
            'user': 0x2b2d31,         # Dark gray for users
            'system': 0x99aab5,       # Light gray for system messages
            'success': 0x57f287,      # Green for success
            'warning': 0xfee75c,      # Yellow for warnings
            'error': 0xed4245         # Red for errors
        }

        # File type icons for attachments
        self.file_icons = {
            'image': '🖼️',
            'video': '🎥',
            'audio': '🎵',
            'document': '📄',
            'archive': '📦',
            'code': '💻',
            'default': '📎'
        }

        # Message type indicators
        self.message_types = {
            'join': '➡️',
            'leave': '⬅️',
            'edit': '✏️',
            'delete': '🗑️',
            'reaction': '👍',
            'command': '⚡',
            'system': '🤖'
        }

    def get_file_icon(self, filename: str) -> str:
        """Get appropriate icon for file type"""
        if not filename:
            return self.file_icons['default']

        ext = filename.lower().split('.')[-1] if '.' in filename else ''

        if ext in ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'bmp']:
            return self.file_icons['image']
        elif ext in ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']:
            return self.file_icons['video']
        elif ext in ['mp3', 'wav', 'flac', 'aac', 'ogg']:
            return self.file_icons['audio']
        elif ext in ['pdf', 'doc', 'docx', 'txt', 'rtf']:
            return self.file_icons['document']
        elif ext in ['zip', 'rar', '7z', 'tar', 'gz']:
            return self.file_icons['archive']
        elif ext in ['py', 'js', 'html', 'css', 'java', 'cpp', 'c']:
            return self.file_icons['code']
        else:
            return self.file_icons['default']

    def format_timestamp(self, timestamp_str: str, include_timezone: bool = True) -> str:
        """Format timestamp with timezone support"""
        try:
            if not timestamp_str:
                return "Unknown"

            # Parse ISO format timestamp
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))

            # Convert to UTC if not already
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            if include_timezone:
                return dt.strftime('%Y-%m-%d %H:%M:%S UTC')
            else:
                return dt.strftime('%Y-%m-%d %H:%M:%S')

        except Exception as e:
            logger.error(f"Error formatting timestamp {timestamp_str}: {e}")
            return timestamp_str or "Unknown"

    def calculate_resolution_time(self, created_at: str, closed_at: str) -> str:
        """Calculate and format resolution time with proper timezone handling"""
        try:
            if not created_at or not closed_at:
                return "N/A"

            # Parse timestamps ensuring timezone consistency
            created = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            closed = datetime.fromisoformat(closed_at.replace('Z', '+00:00'))

            # Ensure both datetimes are timezone-aware
            if created.tzinfo is None:
                created = created.replace(tzinfo=timezone.utc)
            if closed.tzinfo is None:
                closed = closed.replace(tzinfo=timezone.utc)

            delta = closed - created
            total_seconds = int(delta.total_seconds())

            if total_seconds < 0:
                return "Invalid"
            elif total_seconds < 60:
                return f"{total_seconds}s"
            elif total_seconds < 3600:
                minutes = total_seconds // 60
                seconds = total_seconds % 60
                return f"{minutes}m {seconds}s"
            elif total_seconds < 86400:
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                return f"{hours}h {minutes}m"
            else:
                days = total_seconds // 86400
                hours = (total_seconds % 86400) // 3600
                return f"{days}d {hours}h"

        except Exception as e:
            logger.error(f"Error calculating resolution time: {e}")
            return "N/A"

    def get_user_role_color(self, user_roles: List[int], staff_roles: List[int]) -> int:
        """Get color based on user's highest role"""
        if not user_roles or not staff_roles:
            return self.colors['user']

        # Check if user has any staff role
        for role_id in user_roles:
            if role_id in staff_roles:
                return self.colors['staff']

        return self.colors['user']

    def truncate_content(self, content: str, max_length: int = 1000) -> str:
        """Truncate content with ellipsis for embed limits"""
        if not content:
            return ""

        content = str(content).strip()
        if len(content) <= max_length:
            return content

        return content[:max_length-3] + "..."

    def format_message_content(self, content: str, message_type: str = 'default') -> str:
        """Format message content with syntax highlighting and special formatting"""
        if not content:
            return "*No content*"

        # Handle code blocks with syntax highlighting indicators
        if '```' in content:
            # Add code block indicator
            content = f"💻 {content}"

        # Handle mentions and channels
        content = re.sub(r'<@!?(\d+)>', r'@User(\1)', content)
        content = re.sub(r'<#(\d+)>', r'#Channel(\1)', content)
        content = re.sub(r'<@&(\d+)>', r'@Role(\1)', content)

        # Add message type indicator
        if message_type in self.message_types:
            content = f"{self.message_types[message_type]} {content}"

        return self.truncate_content(content, 1000)

    def group_messages_by_user(self, messages: List[Dict[str, Any]], time_threshold: int = 300) -> List[List[Dict[str, Any]]]:
        """Group consecutive messages by the same user within time threshold (seconds)"""
        if not messages:
            return []

        grouped = []
        current_group = []
        last_author = None
        last_timestamp = None

        for message in messages:
            author_id = message.get('author_id')
            timestamp_str = message.get('timestamp', '')

            try:
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            except:
                timestamp = datetime.now(timezone.utc)

            # Check if we should start a new group
            should_group = (
                last_author == author_id and
                last_timestamp and
                (timestamp - last_timestamp).total_seconds() <= time_threshold
            )

            if should_group and current_group:
                current_group.append(message)
            else:
                if current_group:
                    grouped.append(current_group)
                current_group = [message]

            last_author = author_id
            last_timestamp = timestamp

        if current_group:
            grouped.append(current_group)

        return grouped

    async def create_enhanced_transcript_embeds(self, transcript_data: Dict[str, Any], guild: discord.Guild) -> List[discord.Embed]:
        """Create comprehensive Discord embeds for transcript with chronological order and reply context"""
        embeds = []

        try:
            # Extract basic information
            ticket_id = transcript_data.get('ticket_id', 'Unknown')
            messages = transcript_data.get('messages', [])
            staff_roles = ticket_config.get('staff_roles', [])

            # Create header embed with ticket information
            header_embed = discord.Embed(
                title=f"📜 Ticket Transcript - #{ticket_id}",
                description="Professional ticket transcript with comprehensive formatting and analytics",
                color=self.colors['primary'],
                timestamp=datetime.now(timezone.utc)
            )

            # Add ticket metadata
            created_at = self.format_timestamp(transcript_data.get('created_at', ''))
            closed_at = self.format_timestamp(transcript_data.get('closed_at', ''))
            resolution_time = self.calculate_resolution_time(
                transcript_data.get('created_at', ''),
                transcript_data.get('closed_at', '')
            )

            header_embed.add_field(
                name="📊 Ticket Analytics",
                value=(
                    f"**Created:** {created_at}\n"
                    f"**Closed:** {closed_at}\n"
                    f"**Resolution Time:** {resolution_time}\n"
                    f"**Total Messages:** {len(messages)}"
                ),
                inline=True
            )

            # Calculate participant statistics
            participants = set()
            staff_participants = set()
            for msg in messages:
                author_id = msg.get('author_id')
                if author_id:
                    participants.add(author_id)
                    author_roles = msg.get('author_roles', [])
                    if any(role in staff_roles for role in author_roles):
                        staff_participants.add(author_id)

            header_embed.add_field(
                name="👥 Participants",
                value=(
                    f"**Total:** {len(participants)}\n"
                    f"**Staff:** {len(staff_participants)}\n"
                    f"**Users:** {len(participants) - len(staff_participants)}\n"
                    f"**Category:** {transcript_data.get('category', 'Support')}"
                ),
                inline=True
            )

            # Add closure information
            closer_info = transcript_data.get('closed_by', 'Unknown')
            close_reason = transcript_data.get('close_reason', 'No reason provided')

            header_embed.add_field(
                name="🔒 Closure Details",
                value=(
                    f"**Closed By:** {closer_info}\n"
                    f"**Reason:** {self.truncate_content(close_reason, 100)}\n"
                    f"**Server:** {guild.name if guild else 'Unknown'}"
                ),
                inline=False
            )

            # Add footer with dynamic branding
            footer_text = get_dynamic_footer_text(guild)
            header_embed.set_footer(
                text=f"{footer_text} • Transcript ID: {transcript_data.get('_id', 'Unknown')}"
            )

            embeds.append(header_embed)

            # Group messages for better readability
            message_groups = self.group_messages_by_user(messages)

            # Create message embeds with advanced formatting
            current_embed = None
            field_count = 0
            embed_count = 1

            for group in message_groups:
                if not group:
                    continue

                # Get group information
                first_msg = group[0]
                author_name = first_msg.get('author_name', 'Unknown')
                author_id = first_msg.get('author_id')
                author_roles = first_msg.get('author_roles', [])

                # Determine if author is staff
                is_staff = any(role in staff_roles for role in author_roles)

                # Create new embed if needed
                if current_embed is None or field_count >= self.max_embed_fields - 1:
                    if current_embed:
                        embeds.append(current_embed)

                    embed_count += 1
                    current_embed = discord.Embed(
                        title=f"📝 Messages - Part {embed_count - 1}",
                        color=self.colors['staff'] if is_staff else self.colors['user'],
                        timestamp=datetime.now(timezone.utc)
                    )
                    field_count = 0

                # Format message group
                group_content = []
                for msg in group:
                    timestamp = self.format_timestamp(msg.get('timestamp', ''), include_timezone=False)
                    content = self.format_message_content(msg.get('content', ''))

                    # Add attachments
                    attachments = msg.get('attachments', [])
                    if attachments:
                        for attachment in attachments:
                            filename = attachment.split('/')[-1] if '/' in attachment else attachment
                            icon = self.get_file_icon(filename)
                            group_content.append(f"{icon} [{filename}]({attachment})")

                    # Add reactions if any
                    reactions = msg.get('reactions', [])
                    if reactions:
                        reaction_str = " ".join([f"{r.get('emoji', '👍')}{r.get('count', 1)}" for r in reactions])
                        group_content.append(f"👍 {reaction_str}")

                    group_content.append(f"`{timestamp}` {content}")

                # Create field for this message group
                field_name = f"{'👑' if is_staff else '👤'} {author_name}"
                if is_staff:
                    field_name += " (Staff)"

                field_value = "\n".join(group_content)
                field_value = self.truncate_content(field_value, self.max_field_length)

                current_embed.add_field(
                    name=field_name,
                    value=field_value,
                    inline=False
                )

                field_count += 1

            # Add the last embed if it has content
            if current_embed and field_count > 0:
                embeds.append(current_embed)

            # Create summary embed
            if len(embeds) > 1:
                summary_embed = discord.Embed(
                    title="📋 Transcript Summary",
                    description="This transcript has been split into multiple embeds for optimal readability.",
                    color=self.colors['primary']
                )

                summary_embed.add_field(
                    name="📊 Statistics",
                    value=(
                        f"**Total Embeds:** {len(embeds)}\n"
                        f"**Message Groups:** {len(message_groups)}\n"
                        f"**Processing Time:** {datetime.now(timezone.utc).strftime('%H:%M:%S UTC')}"
                    ),
                    inline=True
                )

                summary_embed.add_field(
                    name="🔍 Search & Filter",
                    value=(
                        "Use Discord's search function to find specific messages.\n"
                        "Filter by user mentions or keywords for quick navigation."
                    ),
                    inline=True
                )

                embeds.append(summary_embed)

            return embeds

        except Exception as e:
            logger.error(f"Error creating transcript embeds: {e}")
            traceback.print_exc()

            # Return error embed
            error_embed = discord.Embed(
                title="❌ Transcript Generation Error",
                description=f"An error occurred while generating the transcript: {str(e)}",
                color=self.colors['error']
            )
            return [error_embed]

    async def create_enhanced_text_transcript(self, transcript_data: Dict[str, Any], guild: discord.Guild) -> str:
        """Create comprehensive text transcript with chronological order and reply context"""
        try:
            lines = []

            # Header section
            ticket_id = transcript_data.get('ticket_id', 'Unknown')
            lines.append("=" * 80)
            lines.append(f"TICKET TRANSCRIPT - #{ticket_id}")
            lines.append("=" * 80)
            lines.append("")

            # Metadata section
            lines.append("TICKET INFORMATION:")
            lines.append("-" * 40)
            lines.append(f"Server: {guild.name if guild else 'Unknown'}")
            lines.append(f"Category: {transcript_data.get('category', 'Support')}")
            lines.append(f"Created: {self.format_timestamp(transcript_data.get('created_at', ''))}")
            lines.append(f"Closed: {self.format_timestamp(transcript_data.get('closed_at', ''))}")
            lines.append(f"Resolution Time: {self.calculate_resolution_time(transcript_data.get('created_at', ''), transcript_data.get('closed_at', ''))}")
            lines.append(f"Closed By: {transcript_data.get('closed_by', 'Unknown')}")
            lines.append(f"Close Reason: {transcript_data.get('close_reason', 'No reason provided')}")
            lines.append("")

            # Messages section - preserve chronological order
            messages = transcript_data.get('messages', [])
            staff_roles = ticket_config.get('staff_roles', [])

            lines.append("CONVERSATION HISTORY:")
            lines.append("-" * 40)
            lines.append("")

            # Sort messages by timestamp to ensure chronological order
            sorted_messages = sorted(messages, key=lambda x: x.get('timestamp', ''))

            # Create a lookup for reply context
            message_lookup = {msg.get('message_id'): msg for msg in sorted_messages if msg.get('message_id')}

            for msg_idx, msg in enumerate(sorted_messages):
                if not msg:
                    continue

                author_name = msg.get('author_name', 'Unknown')
                author_roles = msg.get('author_roles', [])
                is_staff = any(role in staff_roles for role in author_roles)
                timestamp = self.format_timestamp(msg.get('timestamp', ''))
                content_raw = msg.get('content', '')
                content = (content_raw or '').strip()  # Handle None values safely

                # Message header with role indicator
                role_indicator = "[STAFF]" if is_staff else "[USER]"
                lines.append(f"{role_indicator} {author_name} - [{timestamp}]")

                # Check for reply context
                reply_to_id = msg.get('reply_to')
                if reply_to_id and reply_to_id in message_lookup:
                    replied_msg = message_lookup[reply_to_id]
                    replied_author = replied_msg.get('author_name', 'Unknown')
                    replied_content = replied_msg.get('content', '')

                    # Truncate replied content for readability
                    if len(replied_content) > 100:
                        replied_content = replied_content[:97] + "..."

                    lines.append(f"  ↳ Replying to {replied_author}: \"{replied_content}\"")

                # Message content
                if content:
                    # Handle multi-line content
                    for line in content.split('\n'):
                        lines.append(f"  {line}")
                else:
                    lines.append("  *No text content*")

                # Add all attachments with direct URLs
                attachments = msg.get('attachments', [])
                if attachments:
                    lines.append("  📎 Attachments:")
                    for attachment in attachments:
                        filename = attachment.split('/')[-1] if '/' in attachment else attachment
                        icon = self.get_file_icon(filename)
                        lines.append(f"    {icon} {filename}")
                        lines.append(f"    🔗 Direct Link: {attachment}")

                # Add reactions
                reactions = msg.get('reactions', [])
                if reactions:
                    reaction_strs = [f"{r.get('emoji', '👍')}({r.get('count', 1)})" for r in reactions]
                    lines.append(f"  👍 Reactions: {', '.join(reaction_strs)}")

                lines.append("")

            # Footer section
            lines.append("=" * 80)
            lines.append(f"Generated: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
            lines.append(f"Transcript ID: {transcript_data.get('_id', 'Unknown')}")
            lines.append(f"Total Messages: {len(sorted_messages)}")
            lines.append(f"Total Participants: {len(set(msg.get('author_id') for msg in sorted_messages if msg.get('author_id')))}")
            lines.append("=" * 80)

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"Error creating text transcript: {e}")
            traceback.print_exc()
            return f"Error generating transcript: {str(e)}"

# Create global formatter instance
transcript_formatter = AdvancedTranscriptFormatter()

class ModernEmbedBuilder:
    """
    Modern embed builder for professional ticket panel creation.
    Provides clean, customizable embeds with validation and professional styling defaults.
    """
    
    def __init__(self, customization_config: dict = None):
        """
        Initialize the ModernEmbedBuilder with optional customization configuration.
        
        Args:
            customization_config (dict): Configuration for customizing embed appearance
        """
        self.customization_config = customization_config or {}
        
        # Professional styling defaults for wide layout
        self.default_config = {
            "embed": {
                "title": "🎫 Support Tickets",
                "description": "Need help? Create a support ticket using the dropdown menu below and our team will assist you promptly. Select the category that best matches your issue for faster resolution.",
                "color": 0x2b2d31,  # Modern dark theme
                "footer_text": "Use the dropdown menu below to select a category and create your ticket",
                "image_url": None
            },
            "layout": {
                "button_style": "modern",
                "show_categories": True,
                "compact_mode": False
            },
            "branding": {
                "server_name": None,
                "support_team": "Support Team",
                "contact_info": None
            }
        }
        
        # Discord embed limits for validation
        self.EMBED_LIMITS = {
            "title": 256,
            "description": 4096,
            "footer": 2048,
            "field_name": 256,
            "field_value": 1024,
            "total_characters": 6000
        }
        
        # Professional color palette
        self.COLOR_PALETTE = {
            "primary": 0x2b2d31,      # Modern dark
            "secondary": 0x5865f2,    # Discord blurple
            "success": 0x57f287,      # Green
            "warning": 0xfee75c,      # Yellow
            "error": 0xed4245,        # Red
            "info": 0x00d4ff,         # Cyan
            "neutral": 0x99aab5       # Light gray
        }
    
    def _merge_config(self) -> dict:
        """
        Merge default configuration with user customization.
        
        Returns:
            dict: Merged configuration with user customizations applied
        """
        merged = self.default_config.copy()
        
        if self.customization_config:
            for section, values in self.customization_config.items():
                if section in merged and isinstance(values, dict):
                    merged[section].update(values)
                else:
                    merged[section] = values
        
        return merged
    
    def _validate_content(self, content: str, limit: int, field_name: str) -> str:
        """
        Validate and truncate content to Discord embed limits.
        
        Args:
            content (str): Content to validate
            limit (int): Character limit for this field
            field_name (str): Name of the field for logging
            
        Returns:
            str: Validated and potentially truncated content
        """
        if not content:
            return ""
        
        content = str(content).strip()
        
        if len(content) > limit:
            logger.warning(f"ModernEmbedBuilder: {field_name} truncated from {len(content)} to {limit} characters")
            return content[:limit-3] + "..."
        
        return content
    
    def _validate_color(self, color) -> int:
        """
        Validate and convert color to proper Discord color format.
        
        Args:
            color: Color value (int, hex string, or color name)
            
        Returns:
            int: Valid Discord color integer
        """
        if color is None:
            return self.COLOR_PALETTE["primary"]
        
        # If it's already an integer, validate range
        if isinstance(color, int):
            if 0 <= color <= 0xFFFFFF:
                return color
            else:
                logger.warning(f"ModernEmbedBuilder: Invalid color value {color}, using default")
                return self.COLOR_PALETTE["primary"]
        
        # If it's a string, try to parse as hex or color name
        if isinstance(color, str):
            color = color.strip().lower()
            
            # Check if it's a named color from our palette
            if color in self.COLOR_PALETTE:
                return self.COLOR_PALETTE[color]
            
            # Try to parse as hex
            if color.startswith('#'):
                color = color[1:]
            
            try:
                return int(color, 16)
            except ValueError:
                logger.warning(f"ModernEmbedBuilder: Invalid color string '{color}', using default")
                return self.COLOR_PALETTE["primary"]
        
        logger.warning(f"ModernEmbedBuilder: Invalid color type {type(color)}, using default")
        return self.COLOR_PALETTE["primary"]
    
    async def _validate_image_url(self, url: str) -> Tuple[Optional[str], List[str]]:
        """
        Enhanced image URL validation with comprehensive accessibility checking and format validation.
        
        Args:
            url (str): Image URL to validate
            
        Returns:
            Tuple[Optional[str], List[str]]: (validated_url, warnings)
        """
        if not url:
            return None, []
        
        url = str(url).strip()
        warnings = []
        
        # Enhanced URL format validation
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            
            if not parsed.scheme or not parsed.netloc:
                logger.warning(f"ModernEmbedBuilder: Invalid image URL format: {url}")
                return None, ["Invalid image URL format - must be a complete URL with protocol and domain"]
            
            if parsed.scheme not in ['http', 'https']:
                logger.warning(f"ModernEmbedBuilder: Unsupported URL scheme: {parsed.scheme}")
                return None, ["URL must use HTTP or HTTPS protocol"]
                
        except Exception as e:
            logger.warning(f"ModernEmbedBuilder: Error parsing URL {url}: {e}")
            return None, ["Invalid URL format"]
        
        # Security recommendations
        if url.startswith('http://'):
            warnings.append("⚠️ Consider using HTTPS for better security and reliability")
        
        # Enhanced image format validation
        supported_formats = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp', '.tiff', '.ico']
        query_params_removed = url.split('?')[0].split('#')[0]  # Remove query params and fragments
        has_valid_extension = any(query_params_removed.lower().endswith(ext) for ext in supported_formats)
        
        if not has_valid_extension:
            warnings.append("⚠️ URL may not be a direct image link - ensure it points directly to an image file")
        
        # Enhanced accessibility and metadata checking
        try:
            timeout = aiohttp.ClientTimeout(total=15)  # Increased timeout for better reliability
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.head(url, allow_redirects=True) as response:
                    # Enhanced status code handling
                    if response.status >= 400:
                        status_messages = {
                            404: "Image not found (404) - check if the URL is correct",
                            403: "Access forbidden (403) - image may require authentication",
                            500: "Server error (500) - image host may be experiencing issues",
                            503: "Service unavailable (503) - image host may be temporarily down"
                        }
                        error_msg = status_messages.get(response.status, f"Image URL not accessible (HTTP {response.status})")
                        logger.warning(f"ModernEmbedBuilder: {error_msg}: {url}")
                        return None, [error_msg]
                    
                    # Enhanced content type validation
                    content_type = response.headers.get('content-type', '').lower()
                    if not content_type.startswith('image/'):
                        logger.warning(f"ModernEmbedBuilder: URL does not point to an image (content-type: {content_type}): {url}")
                        return None, [f"URL does not point to an image (content-type: {content_type})"]
                    
                    # Enhanced file size validation and recommendations
                    content_length = response.headers.get('content-length')
                    if content_length:
                        try:
                            size_bytes = int(content_length)
                            size_mb = size_bytes / (1024 * 1024)
                            
                            # Discord's 8MB limit enforcement
                            if size_mb > 8:
                                return None, [f"⚠️ Image size ({size_mb:.1f}MB) exceeds Discord's 8MB limit"]
                            
                            # Size-based recommendations for optimal performance
                            if size_mb > 4:
                                warnings.append(f"⚠️ Large image size ({size_mb:.1f}MB) may slow loading - consider optimizing to under 2MB")
                            elif size_mb > 2:
                                warnings.append(f"💡 Image size ({size_mb:.1f}MB) is acceptable but could be optimized for faster loading")
                            elif size_mb < 0.05:  # 50KB
                                warnings.append("💡 Very small image may not display clearly - ensure minimum 200x100px dimensions")
                            
                        except ValueError:
                            warnings.append("💡 Could not determine image size - ensure image is accessible")
                    else:
                        warnings.append("💡 Image size could not be determined - this may indicate a dynamic or processed image")
                    
                    # Enhanced format-specific recommendations
                    if 'gif' in content_type:
                        warnings.append("💡 Animated GIFs may distract from ticket content and impact performance")
                        warnings.append("💡 Consider using static PNG or JPEG for better user experience")
                    elif 'svg' in content_type:
                        warnings.append("💡 SVG images may not render consistently across all Discord clients")
                        warnings.append("💡 Consider converting to PNG for guaranteed compatibility")
                    elif 'webp' in content_type:
                        warnings.append("💡 WebP format may not be supported on all devices - PNG/JPEG recommended")
                    elif 'bmp' in content_type or 'tiff' in content_type:
                        warnings.append("💡 Consider converting to PNG or JPEG for better compression and compatibility")
                    
                    # Cache and CDN recommendations
                    cache_control = response.headers.get('cache-control', '').lower()
                    if 'no-cache' in cache_control or 'no-store' in cache_control:
                        warnings.append("💡 Image has no-cache headers - may load slowly for users")
                    
                    # Add layout preservation recommendations
                    warnings.append("✅ Image URL is accessible and will be positioned to preserve embed layout")
                    
        except asyncio.TimeoutError:
            logger.warning(f"ModernEmbedBuilder: Image URL validation timed out: {url}")
            # Still return the URL but with timeout warning
            warnings.append("⚠️ Image URL validation timed out - URL may be slow to load for users")
            warnings.append("💡 Consider using a faster image host or CDN for better performance")
            
        except aiohttp.ClientError as e:
            logger.warning(f"ModernEmbedBuilder: Client error validating image URL {url}: {e}")
            # Still return the URL but with client error warning
            warnings.append(f"⚠️ Could not validate image URL: {str(e)}")
            warnings.append("💡 Test the image URL manually to ensure it works")
            
        except Exception as e:
            logger.warning(f"ModernEmbedBuilder: Unexpected error validating image URL {url}: {e}")
            # Still return the URL but with generic error warning
            warnings.append(f"⚠️ Unexpected error during validation: {str(e)}")
            warnings.append("💡 Image may still work - test manually to verify")
        
        return url, warnings
    
    def apply_customizations(self, config: dict) -> None:
        """
        Apply new customization configuration to the builder.
        
        Args:
            config (dict): New customization configuration to apply
        """
        if not isinstance(config, dict):
            logger.warning("ModernEmbedBuilder: Invalid customization config type, ignoring")
            return
        
        self.customization_config = config
        logger.info("ModernEmbedBuilder: Customization configuration updated")
    
    async def build_panel_embed(self) -> Tuple[discord.Embed, List[str]]:
        """
        Build a professional ticket panel embed with current configuration and comprehensive error handling.
        
        Returns:
            Tuple[discord.Embed, List[str]]: (configured embed, warnings/errors)
        """
        warnings = []
        
        try:
            config = self._merge_config()
            embed_config = config.get("embed", {})
            branding_config = config.get("branding", {})
            layout_config = config.get("layout", {})
            
            # Use error handler for comprehensive validation
            validation_result = await ticket_error_handler.validate_customization_update(0, config)  # Guild ID not needed for embed validation
            
            # Collect validation warnings
            if validation_result.warnings:
                warnings.extend(validation_result.warnings)
            
            # Handle validation errors with graceful degradation
            if not validation_result.is_valid:
                logger.warning("Embed validation failed, applying error recovery")
                for error in validation_result.errors:
                    # Safely extract error message - error can be dict or string
                    if isinstance(error, dict):
                        error_msg = error.get('message', str(error))
                    else:
                        error_msg = str(error)
                    warnings.append(f"⚠️ {error_msg}")
                
                # Use error recovery system to get fallback config
                recovery_config = await ticket_error_handler.recovery_system.recover_from_embed_error(config, Exception("Validation failed"))
                config = recovery_config
                embed_config = config.get("embed", {})
                branding_config = config.get("branding", {})
                warnings.append("🔄 Applied fallback configuration due to validation errors")
            
            # Validate and prepare content with error handling
            title_result = ticket_error_handler.content_validator.validate_text_content(
                embed_config.get("title", self.default_config["embed"]["title"]),
                "title",
                self.EMBED_LIMITS["title"]
            )
            title = title_result.data if title_result.is_valid else self.default_config["embed"]["title"]
            if title_result.warnings:
                warnings.extend(title_result.warnings)

            description_result = ticket_error_handler.content_validator.validate_text_content(
                embed_config.get("description", self.default_config["embed"]["description"]),
                "description",
                self.EMBED_LIMITS["description"]
            )
            description = description_result.data if description_result.is_valid else self.default_config["embed"]["description"]
            if description_result.warnings:
                warnings.extend(description_result.warnings)
            
            footer_result = ticket_error_handler.content_validator.validate_text_content(
                embed_config.get("footer_text", self.default_config["embed"]["footer_text"]),
                "footer_text",
                self.EMBED_LIMITS["footer"]
            )
            footer_text = footer_result.data if footer_result.is_valid else self.default_config["embed"]["footer_text"]
            if footer_result.warnings:
                warnings.extend(footer_result.warnings)
            
            # Validate color with error handling
            color_result = ticket_error_handler.content_validator.validate_color(embed_config.get("color"))
            color = color_result.data if color_result.is_valid else self.COLOR_PALETTE["primary"]
            if color_result.warnings:
                warnings.extend(color_result.warnings)
            
            # Create the embed with error-safe content
            embed = discord.Embed(
                title=title,
                description=description,
                color=color,
                timestamp=datetime.now(timezone.utc)
            )
            
            # Enhanced layout for wider panel with better space utilization
            fields_added = 0
            max_fields = 6  # Increased field limit for wider layout

            # Add branding information with validation in wider layout
            server_name = branding_config.get("server_name")
            support_team = branding_config.get("support_team", "Support Team")
            contact_info = branding_config.get("contact_info")

            # Create a wider layout with more informative fields
            # Row 1: Support Team and Server (if available)
            support_team_result = ticket_error_handler.content_validator.validate_text_content(
                support_team, "support_team", self.EMBED_LIMITS["field_value"]
            )
            embed.add_field(
                name="👥 Support Team",
                value=support_team_result.data if support_team_result.is_valid else "Support Team",
                inline=True
            )
            fields_added += 1

            if server_name:
                server_name_result = ticket_error_handler.content_validator.validate_text_content(
                    server_name, "server_name", self.EMBED_LIMITS["field_value"]
                )
                if server_name_result.is_valid:
                    embed.add_field(
                        name="🏢 Server",
                        value=server_name_result.data,
                        inline=True
                    )
                    fields_added += 1

            # Add contact info in the same row if available
            if contact_info and fields_added < 3:  # Keep first row to 3 fields max
                contact_info_result = ticket_error_handler.content_validator.validate_text_content(
                    contact_info, "contact_info", self.EMBED_LIMITS["field_value"]
                )
                if contact_info_result.is_valid:
                    embed.add_field(
                        name="📞 Contact",
                        value=contact_info_result.data,
                        inline=True
                    )
                    fields_added += 1

            # Row 2: Additional information fields for wider layout
            # Add response time information
            embed.add_field(
                name="⏱️ Response Time",
                value="We typically respond within 24 hours",
                inline=True
            )
            fields_added += 1

            # Add ticket status information
            embed.add_field(
                name="📊 Ticket Status",
                value="Track your ticket progress in real-time",
                inline=True
            )
            fields_added += 1

            # Add help resources if space permits
            if fields_added < max_fields:
                embed.add_field(
                    name="📚 Resources",
                    value="Check our FAQ before creating a ticket",
                    inline=True
                )
                fields_added += 1
            
            # Enhanced image handling with comprehensive error handling
            image_url = embed_config.get("image_url")
            if image_url:
                try:
                    # Use error handler for image validation
                    image_result = await ticket_error_handler.content_validator.validate_image_url(image_url)
                    
                    if image_result.is_valid and image_result.data:
                        # Set image with layout preservation logic
                        # image_result.data is the validated URL string, not a dictionary
                        if isinstance(image_result.data, str):
                            embed.set_image(url=image_result.data)
                            warnings.append("✅ Image successfully integrated with layout preservation")
                        else:
                            logger.warning(f"Unexpected image data type: {type(image_result.data)}")
                            warnings.append("⚠️ Image data format unexpected - skipping image")
                        
                        # Add image metadata warnings
                        if image_result.warnings:
                            warnings.extend(image_result.warnings)
                    else:
                        # Image validation failed
                        for error in image_result.errors:
                            # Safely extract error message - error can be dict or string
                            if isinstance(error, dict):
                                error_msg = error.get('message', str(error))
                            else:
                                error_msg = str(error)
                            warnings.append(f"🖼️ Image Error: {error_msg}")
                        warnings.append("🔄 Panel created without image due to validation errors")
                        
                except Exception as image_error:
                    logger.error(f"Error processing image: {image_error}")
                    warnings.append(f"🖼️ Image processing failed: {str(image_error)}")
                    warnings.append("🔄 Panel created without image")
            
            # Layout preservation: Ensure we have enough fields for a balanced wider layout
            # The wider layout should have at least 6 fields for optimal appearance
            while fields_added < 6:
                if fields_added == 3:
                    # Add a separator field to start the second row
                    embed.add_field(name="\u200b", value="\u200b", inline=False)
                    fields_added += 1
                elif fields_added < 6:
                    # Fill remaining slots with helpful information
                    info_fields = [
                        ("🔒 Privacy", "Your information is kept confidential"),
                        ("🚀 Quick Setup", "Get help setting up your account"),
                        ("💡 Tips", "Browse our knowledge base for solutions")
                    ]
                    field_index = fields_added - 4  # Adjust for the separator
                    if field_index < len(info_fields):
                        name, value = info_fields[field_index]
                        embed.add_field(name=name, value=value, inline=True)
                    else:
                        embed.add_field(
                            name="ℹ️ Information",
                            value="Our support team is ready to help you.",
                            inline=True
                        )
                    fields_added += 1
            
            # Set footer with layout preservation
            embed.set_footer(text=footer_text)
            
            # Validate total embed size and ensure layout consistency
            total_chars = len(embed.title or "") + len(embed.description or "") + len(embed.footer.text or "")
            for field in embed.fields:
                total_chars += len(field.name) + len(field.value)
            
            if total_chars > self.EMBED_LIMITS["total_characters"]:
                logger.warning(f"ModernEmbedBuilder: Embed exceeds total character limit ({total_chars}/{self.EMBED_LIMITS['total_characters']})")
                warnings.append("⚠️ Content may be truncated due to Discord's character limits")
            
            # Add layout preservation info to warnings
            if embed.image:
                warnings.append("✅ Wide layout: Image positioned to complement the expanded panel design")
            else:
                warnings.append("✅ Wide layout: Panel optimized for maximum information display")
            
            logger.info("ModernEmbedBuilder: Successfully built wide panel embed with enhanced layout")
            return embed, warnings
            
        except Exception as e:
            logger.error(f"ModernEmbedBuilder: Error building panel embed: {e}")
            traceback.print_exc()
            
            # Return a basic fallback embed with error warning
            fallback_embed = discord.Embed(
                title="🎫 Support Tickets",
                description="Create a support ticket for assistance.",
                color=self.COLOR_PALETTE["primary"],
                timestamp=datetime.now(timezone.utc)
            )
            fallback_embed.set_footer(text="Click a button below to create a ticket")
            
            error_warnings = [f"⚠️ Error building embed: {str(e)}", "Using fallback layout"]
            return fallback_embed, error_warnings
    
    def get_image_recommendations(self) -> Dict[str, Any]:
        """
        Get comprehensive image format and size recommendations with detailed specifications.
        
        Returns:
            Dictionary with detailed image recommendations, specifications, and best practices
        """
        return {
            "supported_formats": ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp', '.tiff', '.ico'],
            "recommended_formats": ['.png', '.jpg', '.jpeg'],
            "format_details": {
                "PNG": {
                    "best_for": "Images with transparency, logos, graphics with sharp edges",
                    "compression": "Lossless",
                    "transparency": True,
                    "animation": False,
                    "compatibility": "Excellent"
                },
                "JPEG": {
                    "best_for": "Photographs, complex images with many colors",
                    "compression": "Lossy (adjustable quality)",
                    "transparency": False,
                    "animation": False,
                    "compatibility": "Excellent"
                },
                "GIF": {
                    "best_for": "Simple animations, low-color images",
                    "compression": "Lossless (limited colors)",
                    "transparency": True,
                    "animation": True,
                    "compatibility": "Good (may distract users)"
                },
                "WebP": {
                    "best_for": "Modern web images with good compression",
                    "compression": "Both lossy and lossless",
                    "transparency": True,
                    "animation": True,
                    "compatibility": "Limited (newer Discord clients)"
                },
                "SVG": {
                    "best_for": "Vector graphics, scalable logos",
                    "compression": "Text-based",
                    "transparency": True,
                    "animation": True,
                    "compatibility": "Variable (may not render consistently)"
                }
            },
            "size_limits": {
                "discord_max_mb": 8,
                "recommended_max_mb": 2,
                "optimal_range_mb": [0.1, 1.0],
                "minimum_size_kb": 30,
                "performance_thresholds": {
                    "excellent": 0.2,  # < 200KB
                    "good": 0.5,       # < 500KB
                    "acceptable": 1.0,  # < 1MB
                    "slow": 3.0,       # < 3MB
                    "poor": 8.0        # < 8MB (Discord limit)
                }
            },
            "recommended_dimensions": {
                "optimal": {"width": 400, "height": 200, "ratio": "2:1"},
                "minimum": {"width": 200, "height": 100, "ratio": "2:1"},
                "maximum": {"width": 1200, "height": 600, "ratio": "2:1"},
                "aspect_ratios": {
                    "preferred": ["2:1", "16:9"],
                    "acceptable": ["3:2", "4:3", "1:1"],
                    "avoid": ["9:16", "1:3", "3:1"]
                }
            },
            "optimization_tips": [
                "🎯 Use PNG for images with transparency, text, or sharp edges",
                "📸 Use JPEG for photographs and images with many colors",
                "⚡ Compress images to 200KB-1MB for optimal loading speed",
                "🔗 Test image URLs before saving to ensure accessibility",
                "🔒 Use HTTPS URLs for better security and reliability",
                "🌐 Consider using CDN services for faster global loading",
                "📱 Ensure images look good on both desktop and mobile",
                "🎨 Use consistent aspect ratios across your server's panels"
            ],
            "layout_preservation": [
                "✅ Images are positioned to maintain consistent embed dimensions",
                "✅ Layout remains stable whether image loads successfully or fails",
                "✅ Field structure automatically adapts to accommodate image content",
                "✅ Compact mode reduces image impact on overall layout height",
                "✅ Image failures gracefully fall back to text-only layout",
                "💡 Large images are automatically scaled to fit embed constraints",
                "💡 Aspect ratio is preserved during scaling to prevent distortion",
                "💡 Loading states don't cause layout shifts or jumps"
            ],
            "accessibility": [
                "🌍 Ensure images are accessible via public URLs (not behind authentication)",
                "⏰ Avoid using temporary or expiring image links (CDN timeouts)",
                "🚀 Consider users with slow internet connections (optimize file size)",
                "🔄 Provide meaningful context in embed descriptions for screen readers",
                "🎯 Use high contrast images that are visible in both light and dark themes",
                "📱 Test images on different devices and screen sizes",
                "🔗 Verify image URLs work from different geographic locations",
                "⚠️ Have fallback content ready if images fail to load"
            ],
            "performance_guidelines": {
                "loading_speed": {
                    "excellent": "< 200KB, loads instantly",
                    "good": "200KB-500KB, loads quickly",
                    "acceptable": "500KB-1MB, reasonable load time",
                    "slow": "1MB-3MB, may cause delays",
                    "poor": "> 3MB, significant loading delays"
                },
                "user_experience": {
                    "best_practices": [
                        "Images should enhance, not distract from ticket content",
                        "Avoid animated content that draws attention away from text",
                        "Use consistent styling across all server panels",
                        "Test loading on slower connections"
                    ],
                    "common_mistakes": [
                        "Using images that are too large (> 2MB)",
                        "Choosing animated GIFs that distract users",
                        "Using temporary image hosting services",
                        "Not testing image URLs before deployment"
                    ]
                }
            },
            "troubleshooting": {
                "common_issues": {
                    "image_not_loading": [
                        "Check if URL is accessible from different locations",
                        "Verify the URL points directly to an image file",
                        "Ensure the image host allows hotlinking",
                        "Check if the image requires authentication"
                    ],
                    "slow_loading": [
                        "Reduce image file size through compression",
                        "Use a CDN or faster image hosting service",
                        "Convert to more efficient format (WebP, optimized JPEG)",
                        "Consider using smaller dimensions"
                    ],
                    "layout_issues": [
                        "Very large images may cause embed layout shifts",
                        "Extreme aspect ratios may not display well",
                        "Animated content may affect visual stability",
                        "Test with and without images to ensure consistency"
                    ]
                }
            },
            "recommended_services": [
                "Discord CDN (for uploaded images)",
                "Imgur (reliable, Discord-friendly)",
                "GitHub (for static assets in repositories)",
                "Cloudinary (professional image optimization)",
                "AWS S3 + CloudFront (enterprise solution)"
            ]
        }
    
    def format_image_warnings(self, warnings: List[str]) -> str:
        """
        Format image warnings into a user-friendly message.
        
        Args:
            warnings: List of warning messages
            
        Returns:
            Formatted warning message string
        """
        if not warnings:
            return ""
        
        formatted_warnings = []
        for warning in warnings:
            if not warning.startswith(('⚠️', '💡', '✅')):
                formatted_warnings.append(f"⚠️ {warning}")
            else:
                formatted_warnings.append(warning)
        
        return "\n".join(formatted_warnings)
    
    async def validate_and_process_image(self, image_url: str) -> Tuple[Optional[str], List[str], Dict[str, Any]]:
        """
        Comprehensive image validation and processing with detailed feedback.
        
        Args:
            image_url: URL of the image to validate
            
        Returns:
            Tuple of (processed_url, warnings, metadata)
        """
        if not image_url:
            return None, [], {}
        
        try:
            validated_url, warnings = await self._validate_image_url(image_url)
            
            if not validated_url:
                return None, warnings, {}
            
            # Get additional image metadata
            metadata = await self._get_image_metadata(validated_url)
            
            # Add layout-specific recommendations
            layout_warnings = self._generate_layout_recommendations(metadata)
            warnings.extend(layout_warnings)
            
            return validated_url, warnings, metadata
            
        except Exception as e:
            logger.error(f"Error in validate_and_process_image: {e}")
            return None, [f"Error processing image: {str(e)}"], {}
    
    async def _get_image_metadata(self, url: str) -> Dict[str, Any]:
        """
        Get comprehensive metadata about the image for detailed analysis and recommendations.
        
        Args:
            url: Validated image URL
            
        Returns:
            Dictionary with detailed image metadata
        """
        metadata = {
            "url": url,
            "accessible": True,
            "size_mb": None,
            "size_bytes": None,
            "content_type": None,
            "estimated_dimensions": None,
            "format": None,
            "is_animated": False,
            "cache_headers": {},
            "server_info": {},
            "performance_score": "unknown",
            "layout_impact": "minimal"
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=15)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.head(url, allow_redirects=True) as response:
                    if response.status == 200:
                        # Enhanced file size analysis
                        content_length = response.headers.get('content-length')
                        if content_length:
                            try:
                                size_bytes = int(content_length)
                                metadata["size_bytes"] = size_bytes
                                metadata["size_mb"] = size_bytes / (1024 * 1024)
                                
                                # More detailed size categorization
                                if metadata["size_mb"] < 0.05:  # < 50KB
                                    metadata["estimated_dimensions"] = "very_small"
                                    metadata["performance_score"] = "excellent"
                                elif metadata["size_mb"] < 0.2:  # < 200KB
                                    metadata["estimated_dimensions"] = "small"
                                    metadata["performance_score"] = "good"
                                elif metadata["size_mb"] < 1:  # < 1MB
                                    metadata["estimated_dimensions"] = "medium"
                                    metadata["performance_score"] = "acceptable"
                                elif metadata["size_mb"] < 3:  # < 3MB
                                    metadata["estimated_dimensions"] = "large"
                                    metadata["performance_score"] = "slow"
                                    metadata["layout_impact"] = "moderate"
                                else:  # >= 3MB
                                    metadata["estimated_dimensions"] = "very_large"
                                    metadata["performance_score"] = "poor"
                                    metadata["layout_impact"] = "significant"
                                    
                            except ValueError:
                                logger.warning(f"Invalid content-length header for {url}")
                        
                        # Enhanced content type analysis
                        content_type = response.headers.get('content-type', '').lower()
                        metadata["content_type"] = content_type
                        
                        # Determine image format and characteristics
                        if 'png' in content_type:
                            metadata["format"] = "PNG"
                            metadata["supports_transparency"] = True
                            metadata["compression"] = "lossless"
                        elif 'jpeg' in content_type or 'jpg' in content_type:
                            metadata["format"] = "JPEG"
                            metadata["supports_transparency"] = False
                            metadata["compression"] = "lossy"
                        elif 'gif' in content_type:
                            metadata["format"] = "GIF"
                            metadata["supports_transparency"] = True
                            metadata["is_animated"] = True  # Assume GIFs are animated
                            metadata["layout_impact"] = "high"  # Animated content affects layout
                        elif 'webp' in content_type:
                            metadata["format"] = "WebP"
                            metadata["supports_transparency"] = True
                            metadata["compression"] = "modern"
                        elif 'svg' in content_type:
                            metadata["format"] = "SVG"
                            metadata["supports_transparency"] = True
                            metadata["is_vector"] = True
                            metadata["layout_impact"] = "variable"  # SVG can scale
                        elif 'bmp' in content_type:
                            metadata["format"] = "BMP"
                            metadata["supports_transparency"] = False
                            metadata["compression"] = "none"
                        else:
                            metadata["format"] = "unknown"
                        
                        # Cache and performance headers analysis
                        cache_control = response.headers.get('cache-control', '')
                        etag = response.headers.get('etag', '')
                        last_modified = response.headers.get('last-modified', '')
                        
                        metadata["cache_headers"] = {
                            "cache_control": cache_control,
                            "etag": etag,
                            "last_modified": last_modified,
                            "has_cache_headers": bool(cache_control or etag or last_modified)
                        }
                        
                        # Server information for performance assessment
                        server = response.headers.get('server', '')
                        cdn_headers = [
                            'cf-ray',  # Cloudflare
                            'x-amz-cf-id',  # AWS CloudFront
                            'x-cache',  # Various CDNs
                            'x-served-by'  # Fastly
                        ]
                        
                        has_cdn = any(header in response.headers for header in cdn_headers)
                        
                        metadata["server_info"] = {
                            "server": server,
                            "has_cdn": has_cdn,
                            "response_time_estimate": "fast" if has_cdn else "unknown"
                        }
                        
                        # Overall performance assessment
                        if metadata["performance_score"] == "excellent" and has_cdn:
                            metadata["performance_score"] = "optimal"
                        elif metadata["performance_score"] == "poor" and not has_cdn:
                            metadata["performance_score"] = "very_poor"
                    
                    else:
                        metadata["accessible"] = False
                        metadata["error_status"] = response.status
        
        except asyncio.TimeoutError:
            logger.warning(f"Timeout getting image metadata for {url}")
            metadata["accessible"] = False
            metadata["error"] = "timeout"
            
        except aiohttp.ClientError as e:
            logger.warning(f"Client error getting image metadata for {url}: {e}")
            metadata["accessible"] = False
            metadata["error"] = str(e)
            
        except Exception as e:
            logger.warning(f"Unexpected error getting image metadata for {url}: {e}")
            metadata["accessible"] = False
            metadata["error"] = str(e)
        
        return metadata
    
    def _generate_layout_recommendations(self, metadata: Dict[str, Any]) -> List[str]:
        """
        Generate comprehensive layout-specific recommendations based on detailed image metadata.
        
        Args:
            metadata: Detailed image metadata dictionary
            
        Returns:
            List of layout recommendation messages with icons and priorities
        """
        recommendations = []
        
        if not metadata.get("accessible"):
            recommendations.append("⚠️ Image accessibility could not be verified - layout preservation may be affected")
            recommendations.append("💡 Test the image URL manually to ensure it loads properly")
            return recommendations
        
        # Performance-based layout recommendations
        performance_score = metadata.get("performance_score", "unknown")
        layout_impact = metadata.get("layout_impact", "minimal")
        
        if performance_score == "very_poor":
            recommendations.append("🚨 Very large image may cause significant loading delays")
            recommendations.append("💡 Consider compressing to under 1MB for optimal user experience")
        elif performance_score == "poor":
            recommendations.append("⚠️ Large image may affect embed loading time")
            recommendations.append("💡 Optimize image size to improve loading performance")
        elif performance_score == "optimal":
            recommendations.append("✅ Excellent image optimization - minimal impact on layout loading")
        
        # Size-based layout preservation recommendations
        size_mb = metadata.get("size_mb")
        estimated_dims = metadata.get("estimated_dimensions")
        
        if size_mb:
            if size_mb > 5:
                recommendations.append("🚨 Very large image (>5MB) may cause layout instability")
                recommendations.append("💡 Recommended: Compress to under 2MB for consistent layout")
            elif size_mb > 2:
                recommendations.append("⚠️ Large image may slow initial embed rendering")
                recommendations.append("💡 Consider optimizing for faster layout stabilization")
            elif size_mb < 0.03:  # 30KB
                recommendations.append("⚠️ Very small image may not be visible in embed layout")
                recommendations.append("💡 Ensure minimum 200x100px dimensions for clarity")
        
        # Format-specific layout considerations
        image_format = metadata.get("format", "unknown")
        is_animated = metadata.get("is_animated", False)
        
        if is_animated:
            recommendations.append("⚠️ Animated content may distract from ticket information")
            recommendations.append("💡 Static images provide better focus on ticket content")
            recommendations.append("🔧 Layout impact: Animation may affect embed visual stability")
        
        if image_format == "SVG":
            recommendations.append("⚠️ SVG images may render inconsistently across devices")
            recommendations.append("💡 Convert to PNG (400x200px) for guaranteed layout consistency")
        elif image_format == "WebP":
            recommendations.append("💡 WebP format may not display on older Discord clients")
            recommendations.append("💡 PNG or JPEG recommended for maximum compatibility")
        elif image_format == "BMP":
            recommendations.append("⚠️ BMP format is uncompressed - may cause slow loading")
            recommendations.append("💡 Convert to PNG or JPEG for better performance")
        
        # Cache and CDN recommendations for layout stability
        cache_headers = metadata.get("cache_headers", {})
        server_info = metadata.get("server_info", {})
        
        if not cache_headers.get("has_cache_headers"):
            recommendations.append("💡 Image lacks cache headers - may reload frequently")
            recommendations.append("💡 Use a CDN or image host with proper caching for stability")
        
        if not server_info.get("has_cdn"):
            recommendations.append("💡 Consider using a CDN for faster image loading")
            recommendations.append("💡 Faster loading improves embed layout stability")
        
        # Dimension and aspect ratio recommendations
        if estimated_dims == "very_large":
            recommendations.append("🔧 Large image detected - will be scaled to fit embed")
            recommendations.append("💡 Recommended dimensions: 400x200px (2:1 ratio) for optimal layout")
        elif estimated_dims == "very_small":
            recommendations.append("🔧 Small image detected - may appear pixelated when scaled")
            recommendations.append("💡 Minimum recommended: 200x100px for clear display")
        
        # Layout preservation assurance
        if layout_impact == "minimal":
            recommendations.append("✅ Image will preserve consistent embed dimensions")
            recommendations.append("✅ Layout remains stable whether image loads or fails")
        elif layout_impact == "moderate":
            recommendations.append("⚠️ Image may slightly affect embed layout timing")
            recommendations.append("💡 Layout will stabilize once image loads completely")
        elif layout_impact == "significant":
            recommendations.append("🚨 Large image may cause noticeable layout shifts")
            recommendations.append("💡 Consider optimizing for smoother layout experience")
        
        # Accessibility and user experience recommendations
        recommendations.append("🔧 Image positioned to maintain consistent field structure")
        recommendations.append("💡 Embed layout adapts gracefully if image fails to load")
        
        # Add specific technical recommendations based on metadata
        if metadata.get("supports_transparency") and image_format in ["PNG", "WebP"]:
            recommendations.append("✅ Transparency support detected - good for overlay designs")
        
        if metadata.get("compression") == "lossless":
            recommendations.append("✅ Lossless format preserves image quality")
        elif metadata.get("compression") == "lossy":
            recommendations.append("💡 Lossy compression - ensure quality is acceptable")
        
        return recommendations
    
    def get_current_config(self) -> dict:
        """
        Get the current merged configuration.
        
        Returns:
            dict: Current configuration with defaults and customizations merged
        """
        return self._merge_config()
    
    def validate_customization_config(self, config: dict) -> tuple[bool, list]:
        """
        Validate a customization configuration before applying it.
        
        Args:
            config (dict): Configuration to validate
            
        Returns:
            tuple[bool, list]: (is_valid, list_of_errors)
        """
        errors = []
        
        if not isinstance(config, dict):
            errors.append("Configuration must be a dictionary")
            return False, errors
        
        # Validate embed section
        if "embed" in config:
            embed_config = config["embed"]
            if not isinstance(embed_config, dict):
                errors.append("embed section must be a dictionary")
            else:
                # Validate title
                if "title" in embed_config:
                    title = embed_config["title"]
                    if title and len(str(title)) > self.EMBED_LIMITS["title"]:
                        errors.append(f"Title exceeds {self.EMBED_LIMITS['title']} character limit")
                
                # Validate description
                if "description" in embed_config:
                    desc = embed_config["description"]
                    if desc and len(str(desc)) > self.EMBED_LIMITS["description"]:
                        errors.append(f"Description exceeds {self.EMBED_LIMITS['description']} character limit")
                
                # Validate footer
                if "footer_text" in embed_config:
                    footer = embed_config["footer_text"]
                    if footer and len(str(footer)) > self.EMBED_LIMITS["footer"]:
                        errors.append(f"Footer exceeds {self.EMBED_LIMITS['footer']} character limit")
                
                # Validate color
                if "color" in embed_config:
                    color = embed_config["color"]
                    if color is not None:
                        try:
                            validated_color = self._validate_color(color)
                            if validated_color == self.COLOR_PALETTE["primary"] and color != self.COLOR_PALETTE["primary"]:
                                errors.append(f"Invalid color value: {color}")
                        except Exception:
                            errors.append(f"Invalid color format: {color}")
                
                # Validate image URL
                if "image_url" in embed_config:
                    url = embed_config["image_url"]
                    if url and not self._validate_image_url(url):
                        errors.append(f"Invalid image URL format: {url}")
        
        return len(errors) == 0, errors

# Simplified transcript system without interactive views

# Interactive view system removed for simplified transcript workflow

# Content validation and dimension constraints for fixed panel appearance
class TicketContentValidator:
    """Handles content validation and dimension constraints for consistent ticket panel appearance"""

    # More lenient character limits for flexible content
    MAX_WELCOME_MESSAGE_LENGTH = 500  # Increased from 250
    MAX_PANEL_DESCRIPTION_LENGTH = 4000  # Near Discord's 4096 embed description limit
    MAX_PANEL_TITLE_LENGTH = 256  # Discord embed title limit
    MAX_TEAM_NAME_LENGTH = 100  # Increased from 50
    MAX_CATEGORY_NAME_LENGTH = 100  # Increased from 50
    MAX_CATEGORY_DESC_LENGTH = 200  # Increased from 100
    MAX_SUPPORT_HOURS_LENGTH = 400  # Increased from 200
    MAX_RESPONSE_TIME_LENGTH = 300  # Increased from 150
    MAX_FOOTER_LENGTH = 200  # Increased from 100
    MAX_TITLE_LENGTH = 100  # Increased from 50
    MAX_DESCRIPTION_LENGTH = 600  # Increased from 300

    # Image constraints for bulletproof visual consistency (lowered for more flexibility)
    MAX_IMAGE_WIDTH = 1200  # Increased from 600
    MAX_IMAGE_HEIGHT = 400  # Increased from 200

    @staticmethod
    def truncate_text(text: str, max_length: int, add_ellipsis: bool = True) -> str:
        """Truncate text to specified length with ellipsis for consistent appearance"""
        if not text:
            return ""

        text = str(text).strip()
        if len(text) <= max_length:
            return text

        if add_ellipsis and max_length > 3:
            return text[:max_length-3] + "..."
        else:
            return text[:max_length]

    @staticmethod
    def validate_image_url(url: str) -> Tuple[bool, str]:
        """Very lenient image URL validation - accepts almost any HTTP/HTTPS URL"""
        if not url:
            return True, ""  # Empty URL is valid (no image)

        # Very basic URL validation - just check for http/https
        if url.startswith(('http://', 'https://')):
            return True, ""  # Accept any HTTP/HTTPS URL

        # If it doesn't start with http/https, reject it
        return False, "URL must start with http:// or https://"

    @staticmethod
    async def validate_and_process_image_url(url: str) -> Tuple[bool, str, Optional[str]]:
        """
        Validate image URL and check accessibility for bulletproof consistency.
        Returns: (is_valid, error_message, processed_url)
        """
        if not url:
            return True, "", None  # Empty URL is valid (no image)

        # First do basic URL validation
        is_valid, error_msg = TicketContentValidator.validate_image_url(url)
        if not is_valid:
            return False, error_msg, None

        # Skip all network validation - just accept any valid HTTP/HTTPS URL
        return True, "", url

    @classmethod
    def get_dimension_guidance(cls) -> str:
        """Get user-friendly guidance about image dimensions"""
        return (
            f"**Image Guidelines (Very Flexible):**\n"
            f"• Recommended: {cls.MAX_IMAGE_WIDTH}px × {cls.MAX_IMAGE_HEIGHT}px or smaller\n"
            f"• Any HTTP/HTTPS image URL is accepted\n"
            f"• Format: PNG, JPG, WebP, GIF, or any image format\n"
            f"• Images will be displayed as full-width banners\n"
            f"• Discord will automatically resize images as needed"
        )

# Create global validator instance
content_validator = TicketContentValidator()

def get_image_size_recommendations() -> Dict[str, str]:
    """Get image size recommendations for user guidance in configuration interface"""
    return {
        'max_width': str(content_validator.MAX_IMAGE_WIDTH),
        'max_height': str(content_validator.MAX_IMAGE_HEIGHT),
        'aspect_ratio': 'Any aspect ratio (flexible)'
    }

def get_dynamic_footer_text(guild: discord.Guild) -> str:
    """Generate dynamic footer text based on server name"""
    if guild and guild.name:
        # Clean server name for professional appearance
        server_name = guild.name.strip()
        return f"Powered by {server_name} Ticket System"
    else:
        # Fallback to generic branding
        return "Powered by Ticket System"

def count_user_open_tickets(user_id: int) -> int:
    """Count how many open tickets a user currently has"""
    count = 0
    for ticket_data in active_tickets.values():
        if (ticket_data.get("user_id") == user_id and
            ticket_data.get("status", "open") == "open"):
            count += 1
    return count

async def validate_and_format_panel_content(content: Dict[str, str]) -> Tuple[Dict[str, str], List[str]]:
    """
    Validate and format all panel content with bulletproof size constraints for consistent appearance.
    Returns: (formatted_content, validation_warnings)
    """
    formatted = {}
    warnings = []

    # Validate and truncate panel description (new comprehensive field)
    panel_desc_raw = content.get("panel_description", "")
    panel_desc = (panel_desc_raw or "").strip()  # Handle None values safely
    if panel_desc:
        if len(panel_desc) > content_validator.MAX_PANEL_DESCRIPTION_LENGTH:
            warnings.append(f"Panel description truncated from {len(panel_desc)} to {content_validator.MAX_PANEL_DESCRIPTION_LENGTH} characters")
        formatted["panel_description"] = content_validator.truncate_text(
            panel_desc, content_validator.MAX_PANEL_DESCRIPTION_LENGTH
        )
    else:
        formatted["panel_description"] = ""

    # Validate and truncate panel title (new comprehensive field)
    panel_title_raw = content.get("panel_title", "")
    panel_title = (panel_title_raw or "").strip()  # Handle None values safely
    if panel_title:
        if len(panel_title) > content_validator.MAX_PANEL_TITLE_LENGTH:
            warnings.append(f"Panel title truncated from {len(panel_title)} to {content_validator.MAX_PANEL_TITLE_LENGTH} characters")
        formatted["panel_title"] = content_validator.truncate_text(
            panel_title, content_validator.MAX_PANEL_TITLE_LENGTH
        )
    else:
        formatted["panel_title"] = ""

    # Validate panel color (new comprehensive field)
    panel_color_raw = content.get("panel_color", "")
    if panel_color_raw is not None:
        # Handle both string and integer color values
        if isinstance(panel_color_raw, int):
            # Already an integer color value
            formatted["panel_color"] = panel_color_raw
        elif isinstance(panel_color_raw, str):
            panel_color = panel_color_raw.strip()
            if panel_color:
                # Remove # if present and validate hex format
                if panel_color.startswith("#"):
                    panel_color = panel_color[1:]

                # Validate hex color format
                if len(panel_color) == 6 and all(c in '0123456789ABCDEFabcdef' for c in panel_color):
                    try:
                        # Convert to integer for Discord
                        formatted["panel_color"] = int(panel_color, 16)
                    except ValueError:
                        warnings.append("Invalid color format, using default color")
                        formatted["panel_color"] = 0x5865F2  # Default Discord blurple
                else:
                    warnings.append("Invalid color format (use 6-digit hex like #FF0000), using default color")
                    formatted["panel_color"] = 0x5865F2  # Default Discord blurple
            else:
                formatted["panel_color"] = None
        else:
            warnings.append("Invalid color type, using default color")
            formatted["panel_color"] = 0x5865F2  # Default Discord blurple
    else:
        formatted["panel_color"] = None

    # Validate and truncate welcome message with new limit
    welcome_msg_raw = content.get("welcome_message", "")
    welcome_msg = (welcome_msg_raw or "").strip()  # Handle None values safely
    if welcome_msg:
        if len(welcome_msg) > content_validator.MAX_WELCOME_MESSAGE_LENGTH:
            warnings.append(f"Welcome message truncated from {len(welcome_msg)} to {content_validator.MAX_WELCOME_MESSAGE_LENGTH} characters")
        formatted["welcome_message"] = content_validator.truncate_text(
            welcome_msg, content_validator.MAX_WELCOME_MESSAGE_LENGTH
        )
    else:
        formatted["welcome_message"] = ""

    # Validate image URL with enhanced async validation
    image_url_raw = content.get("welcome_image_url", "")
    image_url = (image_url_raw or "").strip()  # Handle None values safely
    if image_url:
        is_valid, error_msg, processed_url = await content_validator.validate_and_process_image_url(image_url)
        if is_valid and processed_url:
            formatted["welcome_image_url"] = processed_url
            if error_msg:  # Warning message
                warnings.append(f"🖼️ Image Warning: {error_msg}")
        else:
            logger.warning(f"Invalid image URL provided: {error_msg}")
            formatted["welcome_image_url"] = ""  # Clear invalid URL
            warnings.append(f"❌ Image URL rejected: {error_msg}")
    else:
        formatted["welcome_image_url"] = ""

    # Validate and truncate support team name
    team_name_raw = content.get("support_team_name", "")
    team_name = (team_name_raw or "").strip()  # Handle None values safely
    if team_name:
        if len(team_name) > content_validator.MAX_TEAM_NAME_LENGTH:
            warnings.append(f"Support team name truncated from {len(team_name)} to {content_validator.MAX_TEAM_NAME_LENGTH} characters")
        formatted["support_team_name"] = content_validator.truncate_text(
            team_name, content_validator.MAX_TEAM_NAME_LENGTH
        )
    else:
        formatted["support_team_name"] = ""

    # Validate and truncate custom footer
    footer_raw = content.get("custom_footer", "")
    footer = (footer_raw or "").strip()  # Handle None values safely
    if footer:
        if len(footer) > content_validator.MAX_FOOTER_LENGTH:
            warnings.append(f"Custom footer truncated from {len(footer)} to {content_validator.MAX_FOOTER_LENGTH} characters")
        formatted["custom_footer"] = content_validator.truncate_text(
            footer, content_validator.MAX_FOOTER_LENGTH
        )
    else:
        formatted["custom_footer"] = ""

    # Validate and truncate support hours
    hours_raw = content.get("support_hours", "")
    hours = (hours_raw or "").strip()  # Handle None values safely
    if hours:
        if len(hours) > content_validator.MAX_SUPPORT_HOURS_LENGTH:
            warnings.append(f"Support hours truncated from {len(hours)} to {content_validator.MAX_SUPPORT_HOURS_LENGTH} characters")
        formatted["support_hours"] = content_validator.truncate_text(
            hours, content_validator.MAX_SUPPORT_HOURS_LENGTH
        )
    else:
        formatted["support_hours"] = ""

    # Validate and truncate response time
    response_raw = content.get("response_time", "")
    response = (response_raw or "").strip()  # Handle None values safely
    if response:
        if len(response) > content_validator.MAX_RESPONSE_TIME_LENGTH:
            warnings.append(f"Response time truncated from {len(response)} to {content_validator.MAX_RESPONSE_TIME_LENGTH} characters")
        formatted["response_time"] = content_validator.truncate_text(
            response, content_validator.MAX_RESPONSE_TIME_LENGTH
        )
    else:
        formatted["response_time"] = ""

    return formatted, warnings

async def send_validation_notification(interaction: discord.Interaction, warnings: List[str], success_message: str = None):
    """Send a Discord embed notification with validation warnings and success information"""
    try:
        if warnings:
            # Create warning embed
            embed = discord.Embed(
                title="⚠️ Validation Warnings",
                description="Some issues were found with your input:",
                color=0xff9900  # Orange for warnings
            )

            # Add warnings as fields
            warning_text = "\n".join([f"• {warning}" for warning in warnings])
            embed.add_field(
                name="Issues Found",
                value=warning_text,
                inline=False
            )

            if success_message:
                embed.add_field(
                    name="✅ Action Taken",
                    value=success_message,
                    inline=False
                )

            embed.add_field(
                name="💡 What This Means",
                value=(
                    "• Invalid content was automatically corrected or removed\n"
                    "• Your configuration was saved with valid values only\n"
                    "• The ticket panel will display correctly with fixed dimensions"
                ),
                inline=False
            )

            embed.set_footer(text="These warnings help ensure bulletproof visual consistency")

            # Send as ephemeral message
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=embed, ephemeral=True)

        elif success_message:
            # Create success embed if no warnings
            embed = discord.Embed(
                title="✅ Success",
                description=success_message,
                color=0x00ff00  # Green for success
            )

            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        logger.error(f"Error sending validation notification: {e}")
        # Fallback to simple text message
        try:
            message = f"⚠️ Validation warnings: {'; '.join(warnings)}" if warnings else success_message
            if not interaction.response.is_done():
                await interaction.response.send_message(message, ephemeral=True)
            else:
                await interaction.followup.send(message, ephemeral=True)
        except:
            pass  # If all else fails, just log it

async def setup_ticket_system(interaction: discord.Interaction):
    """Setup the ticket system with enhanced categories and options"""
    try:
        # Create a professional setup embed
        embed = discord.Embed(
            title="🎫 Ticket System Setup",
            description="Configure your ticket system with the options below. Click the buttons to manage different aspects of the system.",
            color=0x5865F2  # Discord Blurple color for a professional look
        )

        # Add current configuration info
        config_info = []

        # Check for staff roles
        staff_roles_str = "None"
        if ticket_config.get("staff_roles"):
            staff_roles = []
            for role_id in ticket_config["staff_roles"]:
                role = interaction.guild.get_role(role_id)
                if role:
                    staff_roles.append(role.mention)
            if staff_roles:
                staff_roles_str = ", ".join(staff_roles)

        config_info.append(f"**Staff Roles:** {staff_roles_str}")

        # Check for transcript channel
        transcript_channel_str = "None"
        if ticket_config.get("transcript_channel"):
            channel = interaction.guild.get_channel(ticket_config["transcript_channel"])
            if channel:
                transcript_channel_str = channel.mention

        config_info.append(f"**Transcript Channel:** {transcript_channel_str}")

        # Check for ticket channel
        ticket_channel_str = "None"
        if ticket_config.get("ticket_channel"):
            channel = interaction.guild.get_channel(ticket_config["ticket_channel"])
            if channel:
                ticket_channel_str = channel.mention

        config_info.append(f"**Ticket Channel:** {ticket_channel_str}")

        # Check for categories
        categories_str = "None"
        if ticket_config.get("categories"):
            categories = []
            for category_id, details in ticket_config["categories"].items():
                category = interaction.guild.get_channel(int(category_id))
                if category:
                    categories.append(f"{details.get('name', 'Unknown')}")
            if categories:
                categories_str = ", ".join(categories)

        config_info.append(f"**Categories:** {categories_str}")

        # Check for concurrent ticket limit
        max_tickets = ticket_config.get("max_tickets_per_user", 3)
        config_info.append(f"**Max Tickets Per User:** {max_tickets}")

        # Add the configuration info to the embed
        embed.add_field(
            name="Current Configuration",
            value="\n".join(config_info),
            inline=False
        )

        # Create buttons for different setup options
        view = discord.ui.View()

        # Add Category button
        add_category_btn = discord.ui.Button(
            label="Add Category",
            style=discord.ButtonStyle.primary,
            custom_id="add_category",
            emoji="➕"
        )

        # Set Staff Role button
        set_staff_btn = discord.ui.Button(
            label="Set Staff Role",
            style=discord.ButtonStyle.success,
            custom_id="set_staff",
            emoji="👮"
        )

        # Set Transcript Channel button
        set_transcript_btn = discord.ui.Button(
            label="Set Transcript Channel",
            style=discord.ButtonStyle.gray,
            custom_id="set_transcript",
            emoji="📜"
        )

        # Set Ticket Channel button
        set_ticket_channel_btn = discord.ui.Button(
            label="Set Ticket Channel",
            style=discord.ButtonStyle.blurple,
            custom_id="set_ticket_channel",
            emoji="🎫"
        )

        # Remove Category button
        remove_category_btn = discord.ui.Button(
            label="Remove Category",
            style=discord.ButtonStyle.red,
            custom_id="remove_category",
            emoji="🗑️"
        )

        # Configure Ticket Appearance button
        configure_appearance_btn = discord.ui.Button(
            label="Configure Ticket Appearance",
            style=discord.ButtonStyle.primary,
            custom_id="configure_appearance",
            emoji="🎨",
            row=1  # Put on second row
        )

        # Set Ticket Limit button
        set_ticket_limit_btn = discord.ui.Button(
            label="Set Ticket Limit",
            style=discord.ButtonStyle.secondary,
            custom_id="set_ticket_limit",
            emoji="🔢",
            row=1  # Put on second row
        )

        # Define button callbacks
        async def add_category_callback(i: discord.Interaction):
            # Use the new advanced category setup modal
            await i.response.send_modal(AdvancedCategorySetupModal())

        async def set_staff_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Staff Role")
            modal.add_item(discord.ui.TextInput(label="Role ID", placeholder="Enter role ID"))

            async def modal_callback(m_i: discord.Interaction):
                try:
                    role_id = int(modal.children[0].value)
                    success = await set_staff_role(role_id)
                    # Use response.defer() first to prevent timeout
                    await m_i.response.defer(ephemeral=True)
                    await m_i.followup.send(
                        f"Staff role {'added successfully' if success else 'already exists'}",
                        ephemeral=True
                    )
                except ValueError:
                    await m_i.response.send_message("Invalid role ID", ephemeral=True)
                except Exception as e:
                    print(f"Error in set staff modal: {e}")
                    try:
                        await m_i.response.send_message("An error occurred while setting the staff role.", ephemeral=True)
                    except:
                        await m_i.followup.send("An error occurred while setting the staff role.", ephemeral=True)

            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        async def set_transcript_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Transcript Channel")
            modal.add_item(discord.ui.TextInput(label="Channel ID", placeholder="Enter channel ID"))

            async def modal_callback(m_i: discord.Interaction):
                try:
                    channel_id = int(modal.children[0].value)
                    await set_transcript_channel(channel_id)
                    await m_i.response.send_message("Transcript channel set successfully!", ephemeral=True)
                except ValueError:
                    await m_i.response.send_message("Invalid channel ID", ephemeral=True)

            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        async def set_ticket_channel_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Ticket Channel")
            modal.add_item(discord.ui.TextInput(label="Channel ID", placeholder="Enter channel ID"))

            async def modal_callback(m_i: discord.Interaction):
                try:
                    await m_i.response.defer(ephemeral=True)
                    channel_id = int(modal.children[0].value)
                    channel = m_i.guild.get_channel(channel_id)
                    if not channel:
                        await m_i.followup.send("Channel not found!", ephemeral=True)
                        return

                    await set_ticket_channel(channel_id)
                    # TODO: Replace with create_modern_ticket_panel(channel)
                    await m_i.followup.send("Ticket channel set and panel created successfully!", ephemeral=True)
                except ValueError:
                    await m_i.followup.send("Invalid channel ID", ephemeral=True)
                except Exception as e:
                    print(f"Error in set_ticket_channel modal: {e}")
                    await m_i.followup.send("An error occurred while setting up the ticket channel.", ephemeral=True)

            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        async def remove_category_callback(i: discord.Interaction):
            # Check if there are any categories first
            if not ticket_config.get("categories"):
                await i.response.send_message("No ticket categories exist to remove.", ephemeral=True)
                return

            # Create dropdown with existing categories
            options = [
                discord.SelectOption(
                    label=details["name"],
                    value=str(category_id),  # Ensure category_id is string
                    description=(details.get("description", "") or "")[:100]  # Handle None values safely
                )
                for category_id, details in ticket_config["categories"].items()
            ]

            # Verify we have options before creating the dropdown
            if not options:
                await i.response.send_message("No ticket categories exist to remove.", ephemeral=True)
                return

            select = discord.ui.Select(
                placeholder="Select category to remove",
                options=options
            )

            async def select_callback(interaction: discord.Interaction):
                try:
                    # Defer the response first
                    await interaction.response.defer(ephemeral=True)

                    selected_id = select.values[0]  # Get selected value as string

                    # Check if category exists in config
                    if selected_id not in ticket_config["categories"]:
                        await interaction.followup.send("Category not found.", ephemeral=True)
                        return

                    # Get the category from Discord using int conversion only when needed
                    category = interaction.guild.get_channel(int(selected_id))
                    if category:
                        try:
                            # Delete the category and its channels
                            for channel in category.channels:
                                await channel.delete()
                            await category.delete()
                        except discord.Forbidden:
                            await interaction.followup.send("Missing permissions to delete category.", ephemeral=True)
                            return
                        except Exception as e:
                            await interaction.followup.send(f"Error deleting category: {str(e)}", ephemeral=True)
                            return

                    # Remove from config using string ID
                    del ticket_config["categories"][selected_id]
                    await save_ticket_data()

                    # Update ticket panel if it exists
                    if ticket_config.get("ticket_channel"):
                        channel = interaction.guild.get_channel(ticket_config["ticket_channel"])
                        if channel:
                            # TODO: Replace with create_modern_ticket_panel(channel)
                            pass

                    await interaction.followup.send("Category removed successfully!", ephemeral=True)

                except ValueError:
                    await interaction.followup.send("Invalid category ID format.", ephemeral=True)
                except Exception as e:
                    await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)

            select.callback = select_callback

            # Create a new view for the dropdown
            dropdown_view = discord.ui.View()
            dropdown_view.add_item(select)

            await i.response.send_message("Select a category to remove:", view=dropdown_view, ephemeral=True)

        # Define configure appearance callback
        async def configure_appearance_callback(i: discord.Interaction):
            """Show consolidated ticket appearance configuration using new customization system"""
            await i.response.defer(ephemeral=True)

            try:
                # Initialize customization manager
                customization_manager = PanelCustomizationManager(
                    ticket_config_collection, 
                    save_ticket_data
                )
                
                # Get current customization configuration
                guild_id = i.guild.id
                customization_config = customization_manager.get_customization_config(guild_id)

                # Create embed for appearance configuration with clear explanations
                embed = discord.Embed(
                    title="🎨 Configure Ticket Appearance",
                    description="Customize all aspects of your ticket system appearance using the new modern customization system. Configure all settings in one place and immediately apply changes to your ticket panel.",
                    color=0x5865F2
                )

                # Add explanations for each setting category
                embed.add_field(
                    name="Configuration Categories",
                    value=(
                        "**Panel Content**: Title, description, and footer text\n"
                        "**Visual Settings**: Colors, images, and styling\n"
                        "**Support Information**: Team name, hours, and contact info\n"
                        "**Layout Options**: Button styles and display modes\n"
                        "**Advanced Settings**: Auto-close, permissions, and more"
                    ),
                    inline=False
                )

                # Add current customization info using new system
                custom_settings = []

                # Embed settings
                embed_config = customization_config.get("embed", {})
                if embed_config.get("title"):
                    preview = embed_config["title"][:50] + "..." if len(embed_config["title"]) > 50 else embed_config["title"]
                    custom_settings.append(f"✅ **Panel Title**: \"{preview}\"")
                else:
                    custom_settings.append("❌ **Panel Title**: Using default")

                if embed_config.get("description"):
                    preview = embed_config["description"][:50] + "..." if len(embed_config["description"]) > 50 else embed_config["description"]
                    custom_settings.append(f"✅ **Panel Description**: \"{preview}\"")
                else:
                    custom_settings.append("❌ **Panel Description**: Using default")

                if embed_config.get("image_url"):
                    custom_settings.append(f"✅ **Panel Image**: [View Image]({embed_config['image_url']})")
                else:
                    custom_settings.append("❌ **Panel Image**: Not set")

                # Branding settings
                branding_config = customization_config.get("branding", {})
                if branding_config.get("support_team"):
                    custom_settings.append(f"✅ **Support Team**: \"{branding_config['support_team']}\"")
                else:
                    custom_settings.append("❌ **Support Team**: Using default")

                # Layout settings
                layout_config = customization_config.get("layout", {})
                button_style = layout_config.get("button_style", "secondary")
                custom_settings.append(f"✅ **Button Style**: {button_style.title()}")

                embed.add_field(
                    name="Current Settings",
                    value="\n".join(custom_settings),
                    inline=False
                )

                # Use the updated professional appearance configuration view
                view = ModernAppearanceConfigView(i.guild, customization_manager)
                await i.followup.send(embed=embed, view=view, ephemeral=True)
                
            except Exception as e:
                logger.error(f"Error in configure_appearance_callback: {e}")
                await i.followup.send(
                    "❌ An error occurred while loading the appearance configuration. Please try again.",
                    ephemeral=True
                )

        async def set_ticket_limit_callback(i: discord.Interaction):
            """Configure the maximum number of concurrent tickets per user"""
            modal = discord.ui.Modal(title="Set Concurrent Ticket Limit")

            current_limit = ticket_config.get("max_tickets_per_user", 3)
            modal.add_item(discord.ui.TextInput(
                label="Maximum Tickets Per User",
                placeholder="Enter a number between 1 and 10 (recommended: 3-5)",
                default=str(current_limit),
                min_length=1,
                max_length=2
            ))

            async def modal_callback(m_i: discord.Interaction):
                try:
                    new_limit = int(modal.children[0].value)

                    # Validate the limit
                    if new_limit < 1 or new_limit > 10:
                        await m_i.response.send_message(
                            "❌ Invalid limit! Please enter a number between 1 and 10.",
                            ephemeral=True
                        )
                        return

                    # Update the configuration
                    ticket_config["max_tickets_per_user"] = new_limit
                    await save_ticket_data()

                    # Send success embed
                    success_embed = discord.Embed(
                        title="✅ Ticket Limit Updated",
                        description=f"Maximum concurrent tickets per user set to **{new_limit}**",
                        color=0x00ff00
                    )

                    success_embed.add_field(
                        name="What this means",
                        value=(
                            f"• Users can now have up to {new_limit} open tickets simultaneously\n"
                            f"• Users must close existing tickets to create new ones after reaching the limit\n"
                            f"• This helps prevent spam and ensures fair resource usage"
                        ),
                        inline=False
                    )

                    success_embed.set_footer(text=get_dynamic_footer_text(m_i.guild))

                    await m_i.response.send_message(embed=success_embed, ephemeral=True)

                except ValueError:
                    await m_i.response.send_message(
                        "❌ Invalid input! Please enter a valid number.",
                        ephemeral=True
                    )
                except Exception as e:
                    logger.error(f"Error setting ticket limit: {e}")
                    await m_i.response.send_message(
                        "❌ An error occurred while updating the ticket limit.",
                        ephemeral=True
                    )

            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        # Assign callbacks to buttons
        add_category_btn.callback = add_category_callback
        set_staff_btn.callback = set_staff_callback
        set_transcript_btn.callback = set_transcript_callback
        set_ticket_channel_btn.callback = set_ticket_channel_callback
        remove_category_btn.callback = remove_category_callback
        configure_appearance_btn.callback = configure_appearance_callback
        set_ticket_limit_btn.callback = set_ticket_limit_callback

        # Add buttons to view
        view.add_item(add_category_btn)
        view.add_item(set_staff_btn)
        view.add_item(set_transcript_btn)
        view.add_item(set_ticket_channel_btn)
        view.add_item(remove_category_btn)
        view.add_item(configure_appearance_btn)
        view.add_item(set_ticket_limit_btn)

        # Send the embed with the view
        await interaction.followup.send(embed=embed, view=view)

    except Exception as e:
        logger.error(f"Error in setup_ticket_system: {e}")
        traceback.print_exc()
        await interaction.followup.send("An error occurred while setting up the ticket system.", ephemeral=True)

class CloseConfirmView(discord.ui.View):
	def __init__(self):
		super().__init__(timeout=60)  # 60 second timeout
		self.value = None

	@discord.ui.button(label="Close", style=discord.ButtonStyle.red)
	async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
		await interaction.response.defer(ephemeral=True)
		self.value = True
		self.stop()

	@discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
	async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
		await interaction.response.defer(ephemeral=True)
		self.value = False
		self.stop()

class PurgeConfirmationView(discord.ui.View):
	"""Confirmation view for purging all tickets - requires double confirmation for safety"""
	def __init__(self, guild_tickets, requesting_user):
		super().__init__(timeout=60)  # 60 second timeout
		self.guild_tickets = guild_tickets
		self.requesting_user = requesting_user
		self.confirmed = False

	@discord.ui.button(label="⚠️ CONFIRM PURGE", style=discord.ButtonStyle.danger)
	async def confirm_purge(self, interaction: discord.Interaction, button: discord.ui.Button):
		# Verify the user is the same one who initiated the command
		if interaction.user.id != self.requesting_user.id:
			await interaction.response.send_message("Only the user who initiated this command can confirm it.", ephemeral=True)
			return

		if not self.confirmed:
			# First confirmation - require second confirmation
			self.confirmed = True
			button.label = "🔥 FINAL CONFIRMATION - DELETE ALL"
			button.style = discord.ButtonStyle.danger

			await interaction.response.edit_message(
				content="⚠️ **FINAL WARNING**: Click the button again to permanently delete all tickets!",
				view=self
			)
		else:
			# Second confirmation - execute the purge
			await interaction.response.defer(ephemeral=True)

			try:
				success, deleted_count, errors = await purge_all_tickets(interaction.guild, self.guild_tickets)

				if success:
					result_embed = discord.Embed(
						title="✅ Purge Completed",
						description=f"Successfully deleted **{deleted_count}** tickets from the server.",
						color=0x00ff00  # Green for success
					)

					if errors:
						result_embed.add_field(
							name="Errors encountered:",
							value=f"{len(errors)} channels could not be deleted",
							inline=False
						)
				else:
					result_embed = discord.Embed(
						title="❌ Purge Failed",
						description="An error occurred during the purge operation.",
						color=0xff0000  # Red for error
					)

				await interaction.followup.send(embed=result_embed, ephemeral=True)

			except Exception as e:
				logger.error(f"Error during purge confirmation: {e}")
				await interaction.followup.send("An error occurred during the purge operation.", ephemeral=True)

			# Disable the view
			self.stop()

	@discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
	async def cancel_purge(self, interaction: discord.Interaction, button: discord.ui.Button):
		await interaction.response.send_message("Purge operation cancelled.", ephemeral=True)
		self.stop()

async def modal_callback(m_i: discord.Interaction):
	try:
		channel_id = int(m_i.data["components"][0]["components"][0]["value"])
		channel = m_i.guild.get_channel(channel_id)
		if not channel:
			await m_i.response.send_message("Channel not found!", ephemeral=True)
			return

		success = await set_ticket_channel(channel_id)
		if success:
			try:
				await m_i.response.send_message("Ticket channel set and panel created successfully!", ephemeral=True)
			except discord.errors.NotFound:
				await m_i.followup.send("Ticket channel set and panel created successfully!", ephemeral=True)
		else:
			try:
				await m_i.response.send_message("Failed to set ticket channel", ephemeral=True)
			except discord.errors.NotFound:
				await m_i.followup.send("Failed to set ticket channel", ephemeral=True)
	except ValueError:
		try:
			await m_i.response.send_message("Invalid channel ID", ephemeral=True)
		except discord.errors.NotFound:
			await m_i.followup.send("Invalid channel ID", ephemeral=True)
from discord.ext import commands
from bot_instance import bot
from discord.ui import Button, View, Select
import asyncio
import json
import os
import io
from datetime import datetime

class TicketModal(discord.ui.Modal):
	def __init__(self, category_id, ticket_number):
		# Get category name for more professional title
		category_name = "Support"
		category_str_id = str(category_id)
		if category_str_id in ticket_config.get("categories", {}):
			category_name = ticket_config["categories"][category_str_id].get("name", "Support")

		super().__init__(title=f"{category_name} Request")
		self.category_id = category_id
		self.ticket_number = ticket_number

		# Check if this category has custom questions
		category_str_id = str(category_id)

		# Debug output to understand the structure
		print(f"Processing ticket category: {category_id}")
		print(f"Available categories: {ticket_config.get('categories', {})}")

		# Use custom questions if available, otherwise use default questions
		if category_str_id in ticket_config.get("categories", {}):
			category_data = ticket_config["categories"][category_str_id]

			# Check for questions_config first (new format with required/optional settings)
			if "questions_config" in category_data and category_data["questions_config"]:
				questions_config = category_data["questions_config"]

				# Get sorted question keys (q1, q2, q3, etc.)
				question_keys = sorted(questions_config.keys())

				# Discord modals can only have up to 5 text inputs
				max_questions = min(len(question_keys), 5)

				# Add each question to the modal
				for i in range(max_questions):
					key = question_keys[i]
					question_data = questions_config[key]
					question_text = question_data["text"]
					is_required = question_data.get("required", False)

					# For longer questions, we'll format them to fit better in the modal
					if len(question_text) > 45:
						# Create a shortened version for the label
						label = question_text[:42] + "..."
						# Use the full question as the placeholder for context
						placeholder = question_text
					else:
						label = question_text
						placeholder = question_data.get("placeholder", "Please provide your answer here")

					# Add the question to the modal with appropriate styling
					self.add_item(discord.ui.TextInput(
						label=label,
						placeholder=placeholder,
						style=discord.TextStyle.paragraph,
						required=is_required,  # Use the required setting from the config
						min_length=None,  # Allow any length
						max_length=4000   # Maximum Discord allows
					))

			# Fall back to old format if questions_config is not available
			elif "questions" in category_data:
				questions_data = category_data["questions"]

				# Check if questions is a dictionary (like {'q1': 'Question text', 'q2': 'Another question'})
				if isinstance(questions_data, dict):
					# Convert dictionary to list of questions, limiting to 5 (Discord modal limit)
					question_texts = []
					question_keys = sorted(questions_data.keys())[:5]  # Take first 5 keys when sorted

					for key in question_keys:
						question_text = questions_data[key]
						question_texts.append(question_text)

					# Process each question to fit within Discord's constraints
					for i, question_text in enumerate(question_texts):
						# For longer questions, we'll format them to fit better in the modal
						if len(question_text) > 45:
							# Create a shortened version for the label
							label = question_text[:42] + "..."
							# Use the full question as the placeholder for context
							placeholder = question_text
						else:
							label = question_text
							placeholder = "Please provide your answer here"

						# Add the question to the modal with appropriate styling
						self.add_item(discord.ui.TextInput(
							label=label,
							placeholder=placeholder,
							style=discord.TextStyle.paragraph,
							required=i == 0,  # First question is required, others optional
							min_length=None,  # Allow any length
							max_length=4000   # Maximum Discord allows
						))

				# If questions is a list
				elif isinstance(questions_data, list):
					# Process each question (up to 5) to fit within Discord's constraints
					for i, question_text in enumerate(questions_data[:5]):
						# For longer questions, we'll format them to fit better
						if len(question_text) > 45:
							# Create a shortened version for the label
							label = question_text[:42] + "..."
							# Use the full question as the placeholder for context
							placeholder = question_text
						else:
							label = question_text
							placeholder = "Please provide your answer here"

						# Add the question to the modal with appropriate styling
						self.add_item(discord.ui.TextInput(
							label=label,
							placeholder=placeholder,
							style=discord.TextStyle.paragraph,
							required=i == 0,  # First question is required, others optional
							min_length=None,  # Allow any length
							max_length=4000   # Maximum Discord allows
						))
			else:
				# No questions defined, use default professional questions
				self._add_default_questions()
		else:
			# Category not found, use default professional questions
			self._add_default_questions()

	def _add_default_questions(self):
		"""Add default professional questions to the modal"""
		# First question - required
		self.add_item(discord.ui.TextInput(
			label="Please describe your issue",
			placeholder="Provide a detailed description of the problem you're experiencing",
			style=discord.TextStyle.paragraph,
			required=True,
			min_length=10,  # Require at least some detail
			max_length=4000
		))

		# Second question - optional
		self.add_item(discord.ui.TextInput(
			label="Relevant information",
			placeholder="Include any relevant details, screenshots, error messages, or steps to reproduce the issue",
			style=discord.TextStyle.paragraph,
			required=False
		))

		# Third question - optional
		self.add_item(discord.ui.TextInput(
			label="What assistance do you need?",
			placeholder="Let us know what kind of help or resolution you're looking for",
			style=discord.TextStyle.paragraph,
			required=False
		))

	async def on_submit(self, interaction: discord.Interaction):
		try:
			# Get answers from the form
			answers = [child.value for child in self.children]

			# Defer the response immediately to prevent timeout
			await interaction.response.defer(ephemeral=True)

			# Now increment and save the ticket number since we're actually creating the ticket
			async with asyncio.Lock():
				# Double-check the ticket number is still valid and increment it
				current_last_number = ticket_config.get("last_ticket_number", 0)
				if self.ticket_number <= current_last_number:
					# Someone else created a ticket, use the next available number
					self.ticket_number = current_last_number + 1

				# Now officially increment the counter
				ticket_config["last_ticket_number"] = self.ticket_number

				# Save the updated counter immediately
				await save_ticket_data()

			# Define category_str_id at the beginning of the method
			category_str_id = str(self.category_id)

			guild = interaction.guild
			category = guild.get_channel(int(self.category_id))

			if not category:
				await interaction.followup.send(
					"Invalid ticket category! Please contact an administrator.",
					ephemeral=True
				)
				return

			channel_name = f"ticket-{self.ticket_number:04d}"

			channel = await guild.create_text_channel(
				channel_name,
				category=category,
				topic=f"Ticket #{self.ticket_number:04d} | {interaction.user.name}"
			)

			# Set permissions
			await channel.set_permissions(interaction.user, read_messages=True, send_messages=True)
			await channel.set_permissions(guild.default_role, read_messages=False)

			for role_id in ticket_config.get("staff_roles", []):
				role = guild.get_role(role_id)
				if role:
					await channel.set_permissions(role, read_messages=True, send_messages=True)

			# Get category name safely
			category_name = "Support"
			if category_str_id in ticket_config.get("categories", {}):
				category_name = ticket_config["categories"][category_str_id].get("name", "Support")

			# Get category info
			category_info = ticket_config.get("categories", {}).get(category_str_id, {})

			# Get the questions for this category
			questions_list = []

			if category_str_id in ticket_config.get("categories", {}):
				questions_data = ticket_config["categories"][category_str_id].get("questions", [])

				# Handle different question storage formats
				if isinstance(questions_data, dict):
					# Convert dictionary to list of questions
					for key in sorted(questions_data.keys())[:3]:  # Take first 3 keys when sorted
						questions_list.append(questions_data[key])
				elif isinstance(questions_data, list):
					# Use list directly
					questions_list = questions_data[:3]  # Limit to 3 questions

			# If no custom questions, use default ones
			if not questions_list:
				questions_list = [
					"Please describe your issue",
					"Relevant information or evidence",
					"What kind of assistance do you need?"
				]

			# Format all questions and answers
			qa_text = ""
			if len(answers) > 0 and len(questions_list) > 0:
				# Process each question and answer
				for i, question in enumerate(questions_list):
					if i < len(answers):
						answer = answers[i] if answers[i] else "No response"
						qa_text += f"**{question}**\n{answer}\n\n"

			# Send a separate message with the user mention to properly ping them
			mention_message = await channel.send(f"{interaction.user.mention}")

			# Create a clean, minimal ticket embed without title
			ticket_embed = discord.Embed(
				description="Support will be with you shortly.\nTo close this ticket react with 🔒",
				color=0x2b2d31  # Dark theme color
			)

			# Add dynamic footer using server name or support team name
			support_team_name = ticket_config.get("support_team_name")
			if support_team_name:
				footer_text = f"🔧 {support_team_name} - Ticketing without clutter"
			else:
				footer_text = f"🔧 {channel.guild.name} - Ticketing without clutter"
			ticket_embed.set_footer(text=footer_text)

			# Send the welcome embed first
			welcome_message = await channel.send(embed=ticket_embed)

			# Create questions embed like in the image
			questions_embed = discord.Embed(color=0x2b2d31)  # Same dark color

			# Add the questions as fields with dark boxes
			for i, question in enumerate(questions_list):
				if i < len(answers):
					answer_text = answers[i] if answers[i] else "g"  # Default to "g" like in image
					questions_embed.add_field(
						name=question.upper(),  # Uppercase like in image
						value=f"```{answer_text}```",  # Dark box formatting
						inline=False
					)

			# Create a view with buttons for ticket actions
			view = discord.ui.View(timeout=None)  # Persistent view

			# Create buttons for ticket actions with consistent styling
			close_button = discord.ui.Button(
				label="Close Ticket",
				style=discord.ButtonStyle.secondary,
				custom_id="close_ticket",
				emoji="🔒",
				row=0
			)

			close_reason_button = discord.ui.Button(
				label="Close with Reason",
				style=discord.ButtonStyle.red,
				custom_id="close_with_reason",
				emoji="📝",
				row=0
			)

			# Add buttons to the view
			view.add_item(close_button)
			view.add_item(close_reason_button)

			# Send the questions embed with buttons
			ticket_message = await channel.send(embed=questions_embed, view=view)

			# Store message IDs including the mention message, welcome embed, and questions embed
			message_ids = [mention_message.id, welcome_message.id, ticket_message.id]

			# Store ticket info with message IDs
			active_tickets[str(channel.id)] = {
				"user_id": interaction.user.id,
				"ticket_number": self.ticket_number,
				"category_id": self.category_id,
				"mention_message_id": mention_message.id,  # User mention message
				"ticket_message_id": ticket_message.id,  # Main ticket message with consolidated embed and buttons
				"message_ids": message_ids  # All message IDs
			}

			await save_ticket_data()

			# Register ticket commands for this guild now that we have a ticket channel
			try:
				command_manager = get_ticket_command_manager()
				await command_manager.ensure_commands_for_ticket_channel(channel)
			except Exception as e:
				logger.warning(f"Failed to register ticket commands for guild {guild.name}: {e}")

			# Create simple embed matching the reference style
			creation_embed = discord.Embed(
				title="Ticket",
				description=f"Opened a new ticket. {channel.mention}",
				color=0x00ff00  # Green accent color
			)

			# Add dynamic bot branding footer
			creation_embed.set_footer(text=get_dynamic_footer_text(interaction.guild))

			await interaction.followup.send(embed=creation_embed, ephemeral=True)

		except Exception as e:
			print(f"Error in modal submit: {e}")
			traceback.print_exc()

			try:
				await interaction.followup.send(
					"An error occurred while creating your ticket.",
					ephemeral=True
				)
			except:
				print("Could not send error message")





import io
import random
from datetime import datetime

async def channel_operation_with_backoff(operation, max_retries=5):
	"""Execute channel operations with exponential backoff for rate limits"""
	base_delay = 1.0  # Start with 1 second delay
	max_delay = 600.0  # Maximum delay of 10 minutes

	for attempt in range(max_retries):
		try:
			return await operation()
		except discord.HTTPException as e:
			if e.status == 429:  # Rate limit error
				# Get retry_after from the error if available, otherwise calculate it
				retry_after = e.retry_after if hasattr(e, 'retry_after') else min(base_delay * (2 ** attempt), max_delay)

				# Add jitter to prevent thundering herd
				jitter = random.uniform(0, 0.1 * retry_after)
				total_delay = retry_after + jitter

				print(f"Rate limited on channel operation, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{max_retries})")
				await asyncio.sleep(total_delay)
				continue
			raise  # Re-raise other HTTP exceptions

	raise Exception(f"Failed channel operation after {max_retries} attempts")

async def delete_message_with_backoff(message, max_retries=5):
	"""Delete a message with improved exponential backoff for rate limits"""
	base_delay = 0.1  # Start with 100ms delay
	max_delay = 5.0   # Maximum delay of 5 seconds

	for attempt in range(max_retries):
		try:
			await message.delete()
			return True
		except discord.HTTPException as e:
			if e.status == 429:  # Rate limit error
				# Get retry_after from the error if available, otherwise calculate it
				retry_after = e.retry_after if hasattr(e, 'retry_after') else min(base_delay * (2 ** attempt), max_delay)

				# Add jitter to prevent thundering herd
				jitter = random.uniform(0, 0.1 * retry_after)
				total_delay = retry_after + jitter

				print(f"Rate limited on delete, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{max_retries})")
				await asyncio.sleep(total_delay)
				continue
			elif e.status == 404:  # Message already deleted
				return True
			else:
				print(f"Error deleting message: {e}")
				return False
		except Exception as e:
			print(f"Unexpected error deleting message: {e}")
			return False

	print(f"Failed to delete message after {max_retries} attempts")
	return False

import random
import time




async def perform_channel_operation(operation):
	"""Simple channel operation without rate limit handling"""
	try:
		return await operation()
	except Exception as e:
		print(f"Error in channel operation: {e}")
		return None







# Initialize ticket configuration with enhanced default structure
ticket_config = {
	"categories": {},
	"staff_roles": [],
	"transcript_channel": None,
	"ticket_channel": None,
	"last_ticket_number": 0,
	"welcome_message": "Support will be with you shortly.",
	"support_team_name": None,
	"custom_footer": None,
	"panel_message_id": None,
	"support_hours": "Monday-Friday: 9AM-5PM\nWeekends: Limited Support",
	"response_time": "Our team typically responds within 24 hours during business days.",
	"max_tickets_per_user": 3  # Default concurrent ticket limit per user
}

# Active tickets storage
active_tickets = {}
ticket_counter = 0

# Note: on_ready event is handled in bot.py which calls load_ticket_data() and auto_recreate_ticket_panels()

async def auto_recreate_ticket_panels():
    """Automatically recreate ticket panels using the global ticket configuration"""
    try:
        print("🔄 Auto-recreating ticket panels...")
        logger.info("Starting auto-recreation of ticket panels")

        # Check if we have any guilds
        if not bot.guilds:
            print("⚠️ No guilds found - bot may not be ready yet")
            return

        # Use the global ticket configuration that was loaded by load_ticket_data()
        if not ticket_config.get("ticket_channel"):
            print("⚠️ No ticket channel configured in global config")
            return

        if not ticket_config.get("categories"):
            print("⚠️ No categories configured in global config")
            return

        ticket_channel_id = ticket_config["ticket_channel"]
        categories = ticket_config["categories"]

        print(f"📋 Found {len(categories)} categories in global config")
        print(f"🎯 Target ticket channel ID: {ticket_channel_id}")

        # Find the guild that contains the ticket channel
        target_guild = None
        target_channel = None

        for guild in bot.guilds:
            channel = guild.get_channel(ticket_channel_id)
            if channel:
                target_guild = guild
                target_channel = channel
                break

        if not target_channel:
            print(f"⚠️ Ticket channel {ticket_channel_id} not found in any guild")
            return

        print(f"🎯 Found ticket channel #{target_channel.name} in guild {target_guild.name}")

        # Recreate the panel
        print(f"🔄 Recreating ticket panel for {target_guild.name} in #{target_channel.name}")
        success = await create_modern_ticket_panel(target_channel)

        if success:
            print(f"✅ Successfully recreated ticket panel for {target_guild.name}")
        else:
            print(f"❌ Failed to recreate ticket panel for {target_guild.name}")

        print("✅ Auto-recreation of ticket panels completed")

    except Exception as e:
        print(f"❌ Error in auto_recreate_ticket_panels: {e}")
        logger.error(f"Error in auto_recreate_ticket_panels: {e}")

# Enhanced Theme Management Functions
# VLife RP theme functions removed - will be replaced with modern customization system




class AdvancedCategorySetupModal(discord.ui.Modal):
	"""Initial modal for setting up a ticket category before configuring questions"""
	def __init__(self):
		super().__init__(title="Add Ticket Category")

		# Category name input
		self.add_item(discord.ui.TextInput(
			label="Category Name",
			placeholder="e.g., Support Tickets, Bug Reports, Feature Requests",
			required=True,
			max_length=100
		))

		# Category description input
		self.add_item(discord.ui.TextInput(
			label="Category Description",
			placeholder="Brief description of what this category is for",
			style=discord.TextStyle.paragraph,
			required=True,
			max_length=1000
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle initial category setup and proceed to question configuration"""
		name = self.children[0].value
		description = self.children[1].value

		# Create category in Discord
		try:
			# Create the Discord category
			category = await interaction.guild.create_category(name)
			category_id = str(category.id)

			# Store initial category data without questions
			ticket_config["categories"][category_id] = {
				"name": name,
				"description": description,
				"questions_config": {},
				"questions": {}
			}

			# Save to database
			await save_ticket_data()

			# Create a view for question configuration
			view = QuestionConfigView(category_id, name)

			# Send message with the view
			embed = discord.Embed(
				title="Category Created Successfully",
				description=f"The '{name}' ticket category has been created. Now you can configure questions for this category.",
				color=discord.Color.green()
			)

			embed.add_field(
				name="Category ID",
				value=category_id,
				inline=True
			)

			embed.add_field(
				name="Instructions",
				value="Use the buttons below to add, edit, or remove questions. You can add as many questions as needed, set which ones are required, and arrange their order.",
				inline=False
			)

			await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

		except Exception as e:
			logger.error(f"Error creating category: {e}")
			traceback.print_exc()
			await interaction.response.send_message(
				f"Error creating category: {str(e)}",
				ephemeral=True
			)

class QuestionConfigView(discord.ui.View):
	"""View for configuring questions for a ticket category"""
	def __init__(self, category_id, category_name):
		super().__init__(timeout=300)  # 5 minute timeout
		self.category_id = category_id
		self.category_name = category_name
		self.questions = []  # List to store questions

	@discord.ui.button(label="Add Question", style=discord.ButtonStyle.primary, emoji="➕", row=0)
	async def add_question_button(self, interaction: discord.Interaction, button: discord.ui.Button):
		"""Show modal to add a new question"""
		modal = AddQuestionModal(len(self.questions) + 1)
		modal.on_submit_callback = self.add_question_callback
		await interaction.response.send_modal(modal)

	@discord.ui.button(label="Edit Questions", style=discord.ButtonStyle.secondary, emoji="✏️", row=0)
	async def edit_questions_button(self, interaction: discord.Interaction, button: discord.ui.Button):
		"""Show dropdown to select a question to edit"""
		if not self.questions and not ticket_config["categories"][self.category_id].get("questions_config"):
			await interaction.response.send_message("No questions to edit. Add some questions first.", ephemeral=True)
			return

		# Get questions from config if we don't have any in our view yet
		if not self.questions:
			questions_config = ticket_config["categories"][self.category_id].get("questions_config", {})
			for q_id, q_data in questions_config.items():
				self.questions.append({
					"text": q_data["text"],
					"required": q_data["required"],
					"id": q_id
				})

		# Create select menu options
		options = []
		for i, question in enumerate(self.questions):
			required_status = "Required" if question.get("required") else "Optional"
			options.append(
				discord.SelectOption(
					label=f"Question {i+1}",
					description=f"{question['text'][:50]}... ({required_status})",
					value=str(i)
				)
			)

		# Create select menu
		select = discord.ui.Select(
			placeholder="Select a question to edit",
			options=options,
			custom_id="edit_question_select"
		)

		async def select_callback(select_interaction):
			question_index = int(select_interaction.data["values"][0])
			question = self.questions[question_index]

			# Create modal to edit the question
			modal = EditQuestionModal(question_index + 1, question["text"], question.get("required", False))
			modal.on_submit_callback = self.edit_question_callback
			await select_interaction.response.send_modal(modal)

		select.callback = select_callback

		# Create a view for the select menu
		select_view = discord.ui.View(timeout=60)
		select_view.add_item(select)

		await interaction.response.send_message("Select a question to edit:", view=select_view, ephemeral=True)

	@discord.ui.button(label="Remove Question", style=discord.ButtonStyle.danger, emoji="🗑️", row=0)
	async def remove_question_button(self, interaction: discord.Interaction, button: discord.ui.Button):
		"""Show dropdown to select a question to remove"""
		if not self.questions and not ticket_config["categories"][self.category_id].get("questions_config"):
			await interaction.response.send_message("No questions to remove. Add some questions first.", ephemeral=True)
			return

		# Get questions from config if we don't have any in our view yet
		if not self.questions:
			questions_config = ticket_config["categories"][self.category_id].get("questions_config", {})
			for q_id, q_data in questions_config.items():
				self.questions.append({
					"text": q_data["text"],
					"required": q_data["required"],
					"id": q_id
				})

		# Create select menu options
		options = []
		for i, question in enumerate(self.questions):
			required_status = "Required" if question.get("required") else "Optional"
			options.append(
				discord.SelectOption(
					label=f"Question {i+1}",
					description=f"{question['text'][:50]}... ({required_status})",
					value=str(i)
				)
			)

		# Create select menu
		select = discord.ui.Select(
			placeholder="Select a question to remove",
			options=options,
			custom_id="remove_question_select"
		)

		async def select_callback(select_interaction):
			question_index = int(select_interaction.data["values"][0])
			question = self.questions[question_index]

			# Create confirmation view
			confirm_view = discord.ui.View(timeout=60)

			async def confirm_callback(confirm_interaction):
				# Remove the question
				removed_question = self.questions.pop(question_index)

				# Update the category config
				await self.save_questions_to_config()

				await confirm_interaction.response.send_message(
					f"Question removed: '{removed_question['text']}'",
					ephemeral=True
				)

			async def cancel_callback(cancel_interaction):
				await cancel_interaction.response.send_message(
					"Question removal cancelled.",
					ephemeral=True
				)

			# Add confirm/cancel buttons
			confirm_button = discord.ui.Button(
				label="Confirm",
				style=discord.ButtonStyle.danger,
				custom_id="confirm_remove_question"
			)
			confirm_button.callback = confirm_callback

			cancel_button = discord.ui.Button(
				label="Cancel",
				style=discord.ButtonStyle.secondary,
				custom_id="cancel_remove_question"
			)
			cancel_button.callback = cancel_callback

			confirm_view.add_item(confirm_button)
			confirm_view.add_item(cancel_button)

			await select_interaction.response.send_message(
				f"Are you sure you want to remove this question: '{question['text']}'?",
				view=confirm_view,
				ephemeral=True
			)

		select.callback = select_callback

		# Create a view for the select menu
		select_view = discord.ui.View(timeout=60)
		select_view.add_item(select)

		await interaction.response.send_message("Select a question to remove:", view=select_view, ephemeral=True)

	@discord.ui.button(label="Save Configuration", style=discord.ButtonStyle.success, emoji="💾", row=1)
	async def save_button(self, interaction: discord.Interaction, button: discord.ui.Button):
		"""Save the question configuration"""
		if not self.questions:
			await interaction.response.send_message(
				"No questions have been added. Add at least one question before saving.",
				ephemeral=True
			)
			return

		# Save questions to config
		await self.save_questions_to_config()

		# Create summary embed
		embed = discord.Embed(
			title="Question Configuration Saved",
			description=f"Configuration for '{self.category_name}' has been saved with {len(self.questions)} questions.",
			color=discord.Color.green()
		)

		# Add questions to embed
		questions_text = ""
		for i, question in enumerate(self.questions):
			required_status = "Required" if question.get("required") else "Optional"
			questions_text += f"{i+1}. {question['text']} ({required_status})\n"

		embed.add_field(
			name="Questions",
			value=questions_text or "No questions configured",
			inline=False
		)

		# Disable all buttons
		for item in self.children:
			item.disabled = True

		await interaction.response.edit_message(embed=embed, view=self)

	async def add_question_callback(self, interaction, question_text, required):
		"""Callback for when a question is added"""
		# Add the question to our list
		self.questions.append({
			"text": question_text,
			"required": required
		})

		# Update the config
		await self.save_questions_to_config()

		# Send confirmation
		await interaction.response.send_message(
			f"Question added: '{question_text}' (Required: {required})",
			ephemeral=True
		)

	async def edit_question_callback(self, interaction, question_index, question_text, required):
		"""Callback for when a question is edited"""
		# Update the question in our list
		self.questions[question_index - 1] = {
			"text": question_text,
			"required": required
		}

		# Update the config
		await self.save_questions_to_config()

		# Send confirmation
		await interaction.response.send_message(
			f"Question {question_index} updated: '{question_text}' (Required: {required})",
			ephemeral=True
		)

	async def save_questions_to_config(self):
		"""Save the current questions to the ticket config"""
		questions_config = {}
		questions_simple = {}

		for i, question in enumerate(self.questions):
			q_id = f"q{i+1}"
			questions_config[q_id] = {
				"text": question["text"],
				"placeholder": f"Your answer to: {question['text']}",
				"required": question.get("required", False)
			}
			questions_simple[q_id] = question["text"]

		# Update the config
		ticket_config["categories"][self.category_id]["questions_config"] = questions_config
		ticket_config["categories"][self.category_id]["questions"] = questions_simple

		# Save to database
		await save_ticket_data()

		return True

class AddQuestionModal(discord.ui.Modal):
	"""Modal for adding a new question"""
	def __init__(self, question_number):
		super().__init__(title=f"Add Question {question_number}")
		self.question_number = question_number
		self.on_submit_callback = None

		# Question text input
		self.add_item(discord.ui.TextInput(
			label="Question Text",
			placeholder="e.g., Please describe your issue in detail",
			required=True,
			max_length=100
		))

		# Required checkbox (implemented as a select menu since Discord doesn't have checkboxes)
		self.add_item(discord.ui.TextInput(
			label="Required",
			placeholder="Type 'yes' if this question is required, or 'no' if optional",
			required=True,
			max_length=3
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle question submission"""
		question_text = self.children[0].value
		required_input = self.children[1].value.lower()
		required = required_input in ('yes', 'y', 'true', '1')

		if self.on_submit_callback:
			await self.on_submit_callback(interaction, question_text, required)
		else:
			await interaction.response.send_message(
				f"Question added: '{question_text}' (Required: {required})",
				ephemeral=True
			)

class EditQuestionModal(discord.ui.Modal):
	"""Modal for editing an existing question"""
	def __init__(self, question_number, current_text, is_required):
		super().__init__(title=f"Edit Question {question_number}")
		self.question_number = question_number
		self.on_submit_callback = None

		# Question text input
		self.add_item(discord.ui.TextInput(
			label="Question Text",
			placeholder="e.g., Please describe your issue in detail",
			required=True,
			max_length=100,
			default=current_text
		))

		# Required checkbox (implemented as a select menu since Discord doesn't have checkboxes)
		self.add_item(discord.ui.TextInput(
			label="Required",
			placeholder="Type 'yes' if this question is required, or 'no' if optional",
			required=True,
			max_length=3,
			default="yes" if is_required else "no"
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle question update"""
		question_text = self.children[0].value
		required_input = self.children[1].value.lower()
		required = required_input in ('yes', 'y', 'true', '1')

		if self.on_submit_callback:
			await self.on_submit_callback(interaction, self.question_number, question_text, required)
		else:
			await interaction.response.send_message(
				f"Question {self.question_number} updated: '{question_text}' (Required: {required})",
				ephemeral=True
			)

# Keep the original CategoryModal for backward compatibility
class CategoryModal(discord.ui.Modal):
	"""Enhanced modal for adding a ticket category with question configuration"""
	def __init__(self):
		super().__init__(title="Add Ticket Category")

		# Category name input
		self.add_item(discord.ui.TextInput(
			label="Category Name",
			placeholder="e.g., Support Tickets, Bug Reports, Feature Requests",
			required=True,
			max_length=100
		))

		# Category description input
		self.add_item(discord.ui.TextInput(
			label="Category Description",
			placeholder="Brief description of what this category is for",
			style=discord.TextStyle.paragraph,
			required=True,
			max_length=1000
		))

		# Question 1 input (required)
		self.add_item(discord.ui.TextInput(
			label="Question 1 (Required)",
			placeholder="e.g., Please describe your issue in detail",
			required=True,
			max_length=100
		))

		# Question 2 input (optional)
		self.add_item(discord.ui.TextInput(
			label="Question 2 (Optional)",
			placeholder="e.g., What steps have you taken to resolve this?",
			required=False,
			max_length=100
		))

		# Question 3 input (optional)
		self.add_item(discord.ui.TextInput(
			label="Question 3 (Optional)",
			placeholder="e.g., Any additional information we should know?",
			required=False,
			max_length=100
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle category creation with enhanced question configuration"""
		name = self.children[0].value
		description = self.children[1].value

		# Get questions (filter out empty ones)
		questions = {}
		for i, child in enumerate(self.children[2:5], 1):
			if child.value.strip():
				questions[f"q{i}"] = {
					"text": child.value,
					"placeholder": f"Your answer to: {child.value}",
					"required": i == 1  # First question is required
				}

		# Create category in Discord
		try:
			# Create the Discord category
			category = await interaction.guild.create_category(name)

			# Store in config with enhanced data
			ticket_config["categories"][str(category.id)] = {
				"name": name,
				"description": description,
				"questions_config": questions,
				"questions": {f"q{i}": q["text"] for i, q in enumerate(questions.values(), 1)}
			}

			# Save to database
			await save_ticket_data()

			# Send success message with details
			embed = discord.Embed(
				title="Category Created Successfully",
				description=f"The '{name}' ticket category has been created with {len(questions)} question(s).",
				color=discord.Color.green()
			)

			embed.add_field(
				name="Category ID",
				value=str(category.id),
				inline=True
			)

			embed.add_field(
				name="Questions",
				value="\n".join([f"{i}. {q['text']}" for i, q in enumerate(questions.values(), 1)]) if questions else "No custom questions configured",
				inline=False
			)

			await interaction.response.send_message(embed=embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error creating category: {e}")
			traceback.print_exc()
			await interaction.response.send_message(
				f"Error creating category: {str(e)}",
				ephemeral=True
			)


class EditCategoryModal(discord.ui.Modal):
	"""Modal for editing an existing ticket category"""
	def __init__(self, category_id):
		self.category_id = str(category_id)

		# Get current category data
		category_data = ticket_config.get("categories", {}).get(self.category_id, {})
		category_name = category_data.get("name", "")
		category_description = category_data.get("description", "")

		# Get current questions
		questions_config = category_data.get("questions_config", {})
		questions = category_data.get("questions", {})

		super().__init__(title=f"Edit Category: {category_name}")

		# Category name input
		self.add_item(discord.ui.TextInput(
			label="Category Name",
			placeholder="e.g., Support Tickets, Bug Reports",
			required=True,
			max_length=100,
			default=category_name
		))

		# Category description input
		self.add_item(discord.ui.TextInput(
			label="Category Description",
			placeholder="Brief description of what this category is for",
			style=discord.TextStyle.paragraph,
			required=True,
			max_length=1000,
			default=category_description
		))

		# Question 1 input
		q1_text = questions.get("q1", "") or next((q["text"] for q_id, q in questions_config.items() if q_id == "q1"), "")
		self.add_item(discord.ui.TextInput(
			label="Question 1 (Required)",
			placeholder="e.g., Please describe your issue in detail",
			required=True,
			max_length=100,
			default=q1_text
		))

		# Question 2 input
		q2_text = questions.get("q2", "") or next((q["text"] for q_id, q in questions_config.items() if q_id == "q2"), "")
		self.add_item(discord.ui.TextInput(
			label="Question 2 (Optional)",
			placeholder="e.g., What steps have you taken to resolve this?",
			required=False,
			max_length=100,
			default=q2_text
		))

		# Question 3 input
		q3_text = questions.get("q3", "") or next((q["text"] for q_id, q in questions_config.items() if q_id == "q3"), "")
		self.add_item(discord.ui.TextInput(
			label="Question 3 (Optional)",
			placeholder="e.g., Any additional information we should know?",
			required=False,
			max_length=100,
			default=q3_text
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle category update with enhanced question configuration"""
		name = self.children[0].value
		description = self.children[1].value

		# Get questions (filter out empty ones)
		questions = {}
		for i, child in enumerate(self.children[2:5], 1):
			if child.value.strip():
				questions[f"q{i}"] = {
					"text": child.value,
					"placeholder": f"Your answer to: {child.value}",
					"required": i == 1  # First question is required
				}

		try:
			# Update category in Discord if it's a Discord category
			try:
				category_id_int = int(self.category_id)
				category = interaction.guild.get_channel(category_id_int)
				if category:
					await category.edit(name=name)
			except (ValueError, TypeError):
				# Not a Discord category ID or channel not found
				pass

			# Update in config
			if self.category_id in ticket_config.get("categories", {}):
				# Update while preserving other settings
				ticket_config["categories"][self.category_id].update({
					"name": name,
					"description": description,
					"questions_config": questions,
					"questions": {f"q{i}": q["text"] for i, q in enumerate(questions.values(), 1)}
				})

				# Save to database
				await save_ticket_data()

				# Send success message
				embed = discord.Embed(
					title="Category Updated Successfully",
					description=f"The '{name}' ticket category has been updated with {len(questions)} question(s).",
					color=discord.Color.green()
				)

				embed.add_field(
					name="Category ID",
					value=self.category_id,
					inline=True
				)

				embed.add_field(
					name="Questions",
					value="\n".join([f"{i}. {q['text']}" for i, q in enumerate(questions.values(), 1)]) if questions else "No custom questions configured",
					inline=False
				)

				await interaction.response.send_message(embed=embed, ephemeral=True)
			else:
				await interaction.response.send_message(
					"Error: Category not found in configuration.",
					ephemeral=True
				)

		except Exception as e:
			logger.error(f"Error updating category: {e}")
			traceback.print_exc()
			await interaction.response.send_message(
				f"Error updating category: {str(e)}",
				ephemeral=True
			)




class ManageCategoriesView(discord.ui.View):
	"""Enhanced view for managing ticket categories"""
	def __init__(self):
		super().__init__(timeout=180)  # 3 minute timeout
		self.update_options()

	def update_options(self):
		# Clear existing items
		self.clear_items()

		# Add category button with improved styling
		add_button = discord.ui.Button(
			label="Add Category",
			style=discord.ButtonStyle.success,
			custom_id="add_category",
			emoji="➕"
		)
		add_button.callback = self.add_category_callback
		self.add_item(add_button)

		# Add edit category button
		edit_button = discord.ui.Button(
			label="Edit Category",
			style=discord.ButtonStyle.primary,
			custom_id="edit_category",
			emoji="✏️"
		)
		edit_button.callback = self.edit_category_callback
		self.add_item(edit_button)

		# Add remove buttons for each category with improved styling
		for cat_id, info in ticket_config.get("categories", {}).items():
			remove_button = discord.ui.Button(
				label=f"Remove {info['name']}",
				style=discord.ButtonStyle.danger,
				custom_id=f"remove_category_{cat_id}",
				emoji="🗑️"
			)
			remove_button.callback = self.remove_category_callback
			self.add_item(remove_button)

		# Add back button
		back_button = discord.ui.Button(
			label="Back to Setup",
			style=discord.ButtonStyle.secondary,
			custom_id="back_to_setup",
			emoji="◀️"
		)
		back_button.callback = self.back_to_setup_callback
		self.add_item(back_button)

	async def add_category_callback(self, interaction: discord.Interaction):
		"""Show modal to add a new category"""
		await interaction.response.send_modal(CategoryModal())

	async def edit_category_callback(self, interaction: discord.Interaction):
		"""Show dropdown to select a category to edit"""
		# Create a select menu with all categories
		categories = ticket_config.get("categories", {})
		if not categories:
			await interaction.response.send_message(
				"No categories available to edit. Please add a category first.",
				ephemeral=True
			)
			return

		# Create select menu options
		options = []
		for cat_id, info in categories.items():
			options.append(
				discord.SelectOption(
					label=info.get("name", "Unnamed Category"),
					description=f"Edit {info.get('name', 'Unnamed Category')} settings",
					value=cat_id
				)
			)

		# Create select menu view
		view = discord.ui.View(timeout=60)
		select = discord.ui.Select(
			placeholder="Select a category to edit",
			options=options,
			custom_id="edit_category_select"
		)

		async def select_callback(select_interaction):
			category_id = select_interaction.data["values"][0]
			await select_interaction.response.send_modal(
				EditCategoryModal(category_id)
			)

		select.callback = select_callback
		view.add_item(select)

		await interaction.response.send_message(
			"Select a category to edit:",
			view=view,
			ephemeral=True
		)

	async def remove_category_callback(self, interaction: discord.Interaction):
		"""Remove a category with confirmation"""
		category_id = interaction.data["custom_id"].split("_")[-1]

		# Convert to int if it's a Discord category ID
		try:
			category_id = int(category_id)
		except ValueError:
			# If it's not an integer, keep it as a string (for custom categories)
			pass

		if str(category_id) in ticket_config.get("categories", {}):
			# Get category name for confirmation message
			category_name = ticket_config["categories"][str(category_id)].get("name", "Unknown")

			# Create confirmation view
			view = discord.ui.View(timeout=60)

			async def confirm_callback(confirm_interaction):
				# Delete Discord category if it exists
				category = interaction.guild.get_channel(category_id) if isinstance(category_id, int) else None
				if category:
					try:
						await category.delete()
					except Exception as e:
						logger.warning(f"Could not delete category channel: {e}")
						# Continue anyway - this is not critical

				# Remove from config
				del ticket_config["categories"][str(category_id)]
				await save_ticket_data()

				# Update view
				self.update_options()
				await interaction.edit_original_response(view=self)
				await confirm_interaction.response.send_message(
					f"Category '{category_name}' removed successfully!",
					ephemeral=True
				)

			async def cancel_callback(cancel_interaction):
				await cancel_interaction.response.send_message(
					"Category removal cancelled.",
					ephemeral=True
				)

			# Add confirm/cancel buttons
			confirm_button = discord.ui.Button(
				label="Confirm",
				style=discord.ButtonStyle.danger,
				custom_id="confirm_remove"
			)
			confirm_button.callback = confirm_callback

			cancel_button = discord.ui.Button(
				label="Cancel",
				style=discord.ButtonStyle.secondary,
				custom_id="cancel_remove"
			)
			cancel_button.callback = cancel_callback

			view.add_item(confirm_button)
			view.add_item(cancel_button)

			await interaction.response.send_message(
				f"Are you sure you want to remove the '{category_name}' category? This cannot be undone.",
				view=view,
				ephemeral=True
			)

	async def back_to_setup_callback(self, interaction: discord.Interaction):
		"""Go back to the main setup view"""
		await interaction.response.defer(ephemeral=True)
		await setup_ticket_system(interaction)


# Active tickets storage
active_tickets = {}

class StaffRolesView(discord.ui.View):
	"""View for managing staff roles that can access tickets"""
	def __init__(self, guild):
		super().__init__(timeout=180)  # 3 minute timeout
		self.guild = guild

		# Add role button
		add_role_button = discord.ui.Button(
			label="Add Role",
			style=discord.ButtonStyle.success,
			custom_id="add_staff_role",
			emoji="➕"
		)
		add_role_button.callback = self.add_role_callback
		self.add_item(add_role_button)

		# Remove role button
		remove_role_button = discord.ui.Button(
			label="Remove Role",
			style=discord.ButtonStyle.danger,
			custom_id="remove_staff_role",
			emoji="➖"
		)
		remove_role_button.callback = self.remove_role_callback
		self.add_item(remove_role_button)

		# Back button
		back_button = discord.ui.Button(
			label="Back to Setup",
			style=discord.ButtonStyle.secondary,
			custom_id="back_to_setup",
			emoji="◀️"
		)
		back_button.callback = self.back_to_setup_callback
		self.add_item(back_button)

	async def add_role_callback(self, interaction: discord.Interaction):
		"""Show modal to add a staff role"""
		# Create a modal for role ID input
		modal = discord.ui.Modal(title="Add Staff Role")

		# Add text input for role ID
		role_input = discord.ui.TextInput(
			label="Role ID",
			placeholder="Enter the ID of the role to add as staff",
			required=True
		)
		modal.add_item(role_input)

		# Define the callback for when the modal is submitted
		async def modal_callback(modal_interaction: discord.Interaction):
			try:
				role_id = int(role_input.value)
				role = modal_interaction.guild.get_role(role_id)

				if not role:
					await modal_interaction.response.send_message(
						"Role not found! Please check the ID and try again.",
						ephemeral=True
					)
					return

				# Check if role is already in staff roles
				if role_id in ticket_config.get("staff_roles", []):
					await modal_interaction.response.send_message(
						f"The role {role.mention} is already a staff role.",
						ephemeral=True
					)
					return

				# Add role to staff roles
				if "staff_roles" not in ticket_config:
					ticket_config["staff_roles"] = []

				ticket_config["staff_roles"].append(role_id)
				await save_ticket_data()

				await modal_interaction.response.send_message(
					f"Added {role.mention} to staff roles. This role can now access all tickets.",
					ephemeral=True
				)

			except ValueError:
				await modal_interaction.response.send_message(
					"Invalid role ID. Please enter a valid number.",
					ephemeral=True
				)
			except Exception as e:
				logger.error(f"Error adding staff role: {e}")
				traceback.print_exc()
				await modal_interaction.response.send_message(
					f"An error occurred: {str(e)}",
					ephemeral=True
				)

		modal.on_submit = modal_callback
		await interaction.response.send_modal(modal)

	async def remove_role_callback(self, interaction: discord.Interaction):
		"""Show dropdown to select a role to remove"""
		await interaction.response.defer(ephemeral=True)

		# Get current staff roles
		staff_roles = []
		for role_id in ticket_config.get("staff_roles", []):
			role = interaction.guild.get_role(role_id)
			if role:
				staff_roles.append((role_id, role))

		if not staff_roles:
			await interaction.followup.send(
				"No staff roles to remove.",
				ephemeral=True
			)
			return

		# Create select menu options
		options = []
		for role_id, role in staff_roles:
			options.append(
				discord.SelectOption(
					label=role.name,
					description=f"Role ID: {role_id}",
					value=str(role_id)
				)
			)

		# Create select menu view
		view = discord.ui.View(timeout=60)
		select = discord.ui.Select(
			placeholder="Select a role to remove",
			options=options,
			custom_id="remove_staff_role_select"
		)

		async def select_callback(select_interaction):
			role_id = int(select_interaction.data["values"][0])

			# Remove role from staff roles
			if role_id in ticket_config.get("staff_roles", []):
				ticket_config["staff_roles"].remove(role_id)
				await save_ticket_data()

				role = select_interaction.guild.get_role(role_id)
				role_name = role.name if role else f"Role ID: {role_id}"

				await select_interaction.response.send_message(
					f"Removed {role_name} from staff roles.",
					ephemeral=True
				)
			else:
				await select_interaction.response.send_message(
					"This role is not in the staff roles list.",
					ephemeral=True
				)

		select.callback = select_callback
		view.add_item(select)

		await interaction.followup.send(
			"Select a role to remove from staff:",
			view=view,
			ephemeral=True
		)

	async def back_to_setup_callback(self, interaction: discord.Interaction):
		"""Go back to the main setup view"""
		await interaction.response.defer(ephemeral=True)
		await setup_ticket_system(interaction)











class ModernAppearanceConfigView(discord.ui.View):
	"""Modern view for configuring ticket appearance using the new customization system"""
	def __init__(self, guild, customization_manager):
		super().__init__(timeout=300)  # 5 minute timeout for comprehensive configuration
		self.guild = guild
		self.customization_manager = customization_manager

		# Panel Content button (title, description, footer)
		panel_content_button = discord.ui.Button(
			label="Panel Content",
			style=discord.ButtonStyle.primary,
			custom_id="panel_content_config",
			emoji="📝",
			row=0
		)
		panel_content_button.callback = self.panel_content_callback
		self.add_item(panel_content_button)

		# Visual Settings button (colors, images, styling)
		visual_settings_button = discord.ui.Button(
			label="Visual Settings",
			style=discord.ButtonStyle.primary,
			custom_id="visual_settings_config",
			emoji="🎨",
			row=0
		)
		visual_settings_button.callback = self.visual_settings_callback
		self.add_item(visual_settings_button)

		# Branding & Support button (team name, contact info)
		branding_button = discord.ui.Button(
			label="Branding & Support",
			style=discord.ButtonStyle.primary,
			custom_id="branding_config",
			emoji="👥",
			row=0
		)
		branding_button.callback = self.branding_callback
		self.add_item(branding_button)

		# Layout Options button (button styles, display modes)
		layout_button = discord.ui.Button(
			label="Layout Options",
			style=discord.ButtonStyle.primary,
			custom_id="layout_config",
			emoji="📋",
			row=0
		)
		layout_button.callback = self.layout_callback
		self.add_item(layout_button)

		# Advanced Settings button (auto-close, permissions)
		advanced_button = discord.ui.Button(
			label="Advanced Settings",
			style=discord.ButtonStyle.secondary,
			custom_id="advanced_config",
			emoji="⚙️",
			row=1
		)
		advanced_button.callback = self.advanced_callback
		self.add_item(advanced_button)

		# Recreate Ticket Panel button
		recreate_panel_button = discord.ui.Button(
			label="Recreate Panel",
			style=discord.ButtonStyle.success,
			custom_id="recreate_panel",
			emoji="🔄",
			row=1
		)
		recreate_panel_button.callback = self.recreate_panel_callback
		self.add_item(recreate_panel_button)

		# Reset to Defaults button
		reset_button = discord.ui.Button(
			label="Reset to Defaults",
			style=discord.ButtonStyle.danger,
			custom_id="reset_defaults",
			emoji="🔄",
			row=1
		)
		reset_button.callback = self.reset_defaults_callback
		self.add_item(reset_button)

		# Back button
		back_button = discord.ui.Button(
			label="Back to Setup",
			style=discord.ButtonStyle.secondary,
			custom_id="back_to_setup",
			emoji="◀️",
			row=1
		)
		back_button.callback = self.back_to_setup_callback
		self.add_item(back_button)

	async def panel_content_callback(self, interaction: discord.Interaction):
		"""Open panel content configuration modal using new customization system"""
		try:
			# Get current configuration
			guild_id = interaction.guild.id
			current_config = self.customization_manager.get_customization_config(guild_id)
			
			modal = ModernPanelContentModal(current_config, self.customization_manager)
			await interaction.response.send_modal(modal)
		except Exception as e:
			logger.error(f"Error in panel_content_callback: {e}")
			await interaction.response.send_message("❌ Error opening panel content configuration.", ephemeral=True)

	async def visual_settings_callback(self, interaction: discord.Interaction):
		"""Open visual settings configuration modal using new customization system"""
		try:
			# Get current configuration
			guild_id = interaction.guild.id
			current_config = self.customization_manager.get_customization_config(guild_id)
			
			modal = ModernVisualSettingsModal(current_config, self.customization_manager)
			await interaction.response.send_modal(modal)
		except Exception as e:
			logger.error(f"Error in visual_settings_callback: {e}")
			await interaction.response.send_message("❌ Error opening visual settings configuration.", ephemeral=True)

	async def branding_callback(self, interaction: discord.Interaction):
		"""Open branding and support information configuration modal"""
		try:
			# Get current configuration
			guild_id = interaction.guild.id
			current_config = self.customization_manager.get_customization_config(guild_id)
			
			modal = ModernBrandingModal(current_config, self.customization_manager)
			await interaction.response.send_modal(modal)
		except Exception as e:
			logger.error(f"Error in branding_callback: {e}")
			await interaction.response.send_message("❌ Error opening branding configuration.", ephemeral=True)

	async def layout_callback(self, interaction: discord.Interaction):
		"""Open layout options configuration modal"""
		try:
			# Get current configuration
			guild_id = interaction.guild.id
			current_config = self.customization_manager.get_customization_config(guild_id)
			
			modal = ModernLayoutModal(current_config, self.customization_manager)
			await interaction.response.send_modal(modal)
		except Exception as e:
			logger.error(f"Error in layout_callback: {e}")
			await interaction.response.send_message("❌ Error opening layout configuration.", ephemeral=True)

	async def advanced_callback(self, interaction: discord.Interaction):
		"""Open advanced settings configuration modal"""
		try:
			# Get current configuration
			guild_id = interaction.guild.id
			current_config = self.customization_manager.get_customization_config(guild_id)
			
			modal = ModernAdvancedModal(current_config, self.customization_manager)
			await interaction.response.send_modal(modal)
		except Exception as e:
			logger.error(f"Error in advanced_callback: {e}")
			await interaction.response.send_message("❌ Error opening advanced configuration.", ephemeral=True)

	async def recreate_panel_callback(self, interaction: discord.Interaction):
		"""Recreate the ticket panel with current customization settings"""
		await interaction.response.defer(ephemeral=True)

		try:
			# Check if user has staff permissions
			if not await self._check_staff_permissions(interaction):
				await interaction.followup.send(
					"❌ **Insufficient Permissions**\n"
					"You need staff permissions to recreate the ticket panel.",
					ephemeral=True
				)
				return

			# Check if ticket channel is set
			if not ticket_config.get("ticket_channel"):
				await interaction.followup.send(
					"❌ **No Ticket Channel Configured**\n"
					"Please set a ticket channel first using 'Set Ticket Channel' in the main setup.",
					ephemeral=True
				)
				return

			# Get the channel
			channel = self.guild.get_channel(ticket_config["ticket_channel"])
			if not channel:
				await interaction.followup.send(
					"❌ **Channel Not Found**\n"
					"The configured ticket channel no longer exists. Please set a new one in the main setup.",
					ephemeral=True
				)
				return

			# Check if bot has necessary permissions in the channel
			bot_member = channel.guild.get_member(bot.user.id)
			if not bot_member:
				await interaction.followup.send(
					"❌ **Bot Permission Error**\n"
					"Unable to verify bot permissions. Please ensure the bot is properly configured.",
					ephemeral=True
				)
				return

			required_permissions = discord.Permissions(
				send_messages=True,
				embed_links=True,
				manage_messages=True,
				read_message_history=True
			)
			
			channel_permissions = channel.permissions_for(bot_member)
			if not channel_permissions.is_superset(required_permissions):
				missing_perms = []
				if not channel_permissions.send_messages:
					missing_perms.append("Send Messages")
				if not channel_permissions.embed_links:
					missing_perms.append("Embed Links")
				if not channel_permissions.manage_messages:
					missing_perms.append("Manage Messages")
				if not channel_permissions.read_message_history:
					missing_perms.append("Read Message History")
				
				await interaction.followup.send(
					f"❌ **Missing Bot Permissions**\n"
					f"The bot is missing the following permissions in {channel.mention}:\n"
					f"• {chr(10).join(missing_perms)}\n\n"
					f"Please grant these permissions and try again.",
					ephemeral=True
				)
				return

			# Send progress message
			progress_embed = discord.Embed(
				title="🔄 Recreating Ticket Panel...",
				description=f"Applying current customization settings to {channel.mention}",
				color=0xfee75c  # Warning yellow
			)
			progress_message = await interaction.followup.send(embed=progress_embed, ephemeral=True)

			# Recreate the ticket panel using the modern system
			success = await create_modern_ticket_panel(channel)
			
			if success:
				# Get customization info for success message
				customization_manager = PanelCustomizationManager(
					ticket_config_collection, 
					save_ticket_data
				)
				customization_config = customization_manager.get_customization_config(interaction.guild.id)
				
				# Build success message with applied settings info
				applied_settings = []
				if customization_config.get("visual", {}).get("primary_color") != 0x000000:
					applied_settings.append("Custom colors")
				if customization_config.get("content", {}).get("title") != "Support Tickets":
					applied_settings.append("Custom title")
				if customization_config.get("content", {}).get("description"):
					applied_settings.append("Custom description")
				if customization_config.get("visual", {}).get("image_url"):
					applied_settings.append("Custom image")
				if customization_config.get("branding", {}).get("footer_text"):
					applied_settings.append("Custom footer")

				settings_text = f"Applied settings: {', '.join(applied_settings)}" if applied_settings else "Using default settings"

				success_embed = discord.Embed(
					title="✅ Ticket Panel Recreated Successfully!",
					description=(
						f"Panel recreated in {channel.mention} with the modern design system.\n\n"
						f"**{settings_text}**\n\n"
						f"The panel now includes all current customization settings and "
						f"will automatically apply any future changes when recreated."
					),
					color=0x57f287  # Success green
				)
				success_embed.add_field(
					name="🎨 Customization",
					value=(
						"To modify the panel appearance, use the customization options "
						"in the setup menu and recreate the panel to apply changes."
					),
					inline=False
				)
				success_embed.set_footer(text=f"Recreated by {interaction.user.display_name}")
				
				await progress_message.edit(embed=success_embed)
				logger.info(f"Panel recreated successfully by {interaction.user.id} in channel {channel.id}")
			else:
				error_embed = discord.Embed(
					title="❌ Panel Recreation Failed",
					description=(
						"There was an error recreating the ticket panel. This could be due to:\n\n"
						"• Missing bot permissions\n"
						"• Invalid ticket configuration\n"
						"• Network connectivity issues\n\n"
						"Please check the configuration and try again. If the problem persists, "
						"contact an administrator."
					),
					color=0xed4245  # Error red
				)
				error_embed.add_field(
					name="🔧 Troubleshooting",
					value=(
						"1. Verify bot permissions in the ticket channel\n"
						"2. Check that ticket categories are properly configured\n"
						"3. Ensure the channel still exists and is accessible"
					),
					inline=False
				)
				await progress_message.edit(embed=error_embed)
				logger.error(f"Panel recreation failed for user {interaction.user.id} in channel {channel.id}")
				
		except discord.Forbidden:
			await interaction.followup.send(
				"❌ **Permission Denied**\n"
				"The bot doesn't have permission to recreate the panel in the configured channel.",
				ephemeral=True
			)
		except discord.HTTPException as e:
			logger.error(f"HTTP error recreating ticket panel: {e}")
			await interaction.followup.send(
				"❌ **Network Error**\n"
				f"A network error occurred while recreating the panel: {str(e)}\n"
				"Please try again in a moment.",
				ephemeral=True
			)
		except Exception as e:
			logger.error(f"Error recreating ticket panel: {e}")
			logger.error(traceback.format_exc())
			await interaction.followup.send(
				"❌ **Unexpected Error**\n"
				"An unexpected error occurred while recreating the ticket panel. "
				"Please try again or contact an administrator if the problem persists.",
				ephemeral=True
			)

	async def _check_staff_permissions(self, interaction: discord.Interaction) -> bool:
		"""Check if user has staff permissions to recreate panel"""
		try:
			# Check if user has administrator permission
			if interaction.user.guild_permissions.administrator:
				return True
			
			# Check if user has any staff roles
			staff_roles = ticket_config.get("staff_roles", [])
			user_role_ids = [role.id for role in interaction.user.roles]
			
			return any(role_id in staff_roles for role_id in user_role_ids)
		except Exception as e:
			logger.error(f"Error checking staff permissions: {e}")
			return False

	async def reset_defaults_callback(self, interaction: discord.Interaction):
		"""Reset all customization settings to defaults"""
		await interaction.response.defer(ephemeral=True)

		try:
			guild_id = interaction.guild.id
			success, errors = await self.customization_manager.reset_to_defaults(guild_id)
			
			if success:
				embed = discord.Embed(
					title="✅ Settings Reset Successfully!",
					description="All customization settings have been reset to their default values. Use 'Recreate Panel' to apply the changes.",
					color=0x57f287  # Success green
				)
				await interaction.followup.send(embed=embed, ephemeral=True)
			else:
				error_embed = discord.Embed(
					title="❌ Reset Failed",
					description=f"Failed to reset settings: {', '.join(errors)}",
					color=0xed4245  # Error red
				)
				await interaction.followup.send(embed=error_embed, ephemeral=True)
				
		except Exception as e:
			logger.error(f"Error resetting to defaults: {e}")
			error_embed = discord.Embed(
				title="❌ Unexpected Error",
				description="An unexpected error occurred while resetting settings.",
				color=0xed4245  # Error red
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)

	async def back_to_setup_callback(self, interaction: discord.Interaction):
		"""Go back to the main setup view"""
		await interaction.response.defer(ephemeral=True)
		await setup_ticket_system(interaction)


class ModernPanelContentModal(discord.ui.Modal):
	"""Modal for configuring panel content using the new customization system"""
	def __init__(self, current_config, customization_manager):
		super().__init__(title="Configure Panel Content")
		self.current_config = current_config
		self.customization_manager = customization_manager

		# Get current embed configuration
		embed_config = current_config.get("embed", {})

		# Panel Title
		current_title = embed_config.get("title", "")
		self.add_item(discord.ui.TextInput(
			label="Panel Title",
			placeholder="e.g., 🎫 Support Tickets, Help Center (max 256 chars)",
			style=discord.TextStyle.short,
			required=False,
			max_length=256,
			default=current_title
		))

		# Panel Description
		current_description = embed_config.get("description", "")
		self.add_item(discord.ui.TextInput(
			label="Panel Description",
			placeholder="Main text shown in the ticket panel (max 4000 chars)",
			style=discord.TextStyle.paragraph,
			required=False,
			max_length=4000,
			default=current_description
		))

		# Footer Text
		current_footer = embed_config.get("footer_text", "")
		self.add_item(discord.ui.TextInput(
			label="Footer Text",
			placeholder="Text shown at the bottom of the panel (max 2048 chars)",
			style=discord.TextStyle.paragraph,
			required=False,
			max_length=2048,
			default=current_footer
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle panel content configuration submission using new customization system"""
		try:
			await interaction.response.defer(ephemeral=True)

			# Get guild ID
			guild_id = interaction.guild.id

			# Create updated configuration
			updated_config = self.current_config.copy()
			if "embed" not in updated_config:
				updated_config["embed"] = {}

			# Update embed content
			updated_config["embed"]["title"] = self.children[0].value.strip() if self.children[0].value.strip() else None
			updated_config["embed"]["description"] = self.children[1].value.strip() if self.children[1].value.strip() else None
			updated_config["embed"]["footer_text"] = self.children[2].value.strip() if self.children[2].value.strip() else None

			# Update customization using the manager
			success, errors = await self.customization_manager.update_customization(guild_id, updated_config)

			if success:
				embed = discord.Embed(
					title="✅ Panel Content Updated",
					description="Panel title, description, and footer have been configured successfully!",
					color=0x57f287  # Success green
				)
				embed.add_field(
					name="🎨 Next Steps",
					value="• Use 'Visual Settings' to configure colors and images\n• Use 'Recreate Panel' to apply changes",
					inline=False
				)
				await interaction.followup.send(embed=embed, ephemeral=True)
			else:
				error_embed = discord.Embed(
					title="❌ Configuration Error",
					description=f"Failed to update panel content:\n• {chr(10).join(errors)}",
					color=0xed4245  # Error red
				)
				await interaction.followup.send(embed=error_embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error updating panel content: {e}")
			error_embed = discord.Embed(
				title="❌ Unexpected Error",
				description="An unexpected error occurred while updating panel content.",
				color=0xed4245  # Error red
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)


class ModernVisualSettingsModal(discord.ui.Modal):
	"""Modal for configuring visual settings using the new customization system"""
	def __init__(self, current_config, customization_manager):
		super().__init__(title="Configure Visual Settings")
		self.current_config = current_config
		self.customization_manager = customization_manager

		# Get current embed and layout configuration
		embed_config = current_config.get("embed", {})
		layout_config = current_config.get("layout", {})

		# Panel Image URL
		current_image_url = embed_config.get("image_url", "")
		self.add_item(discord.ui.TextInput(
			label="Panel Image URL",
			placeholder="HTTPS image URL for panel banner (optional)",
			style=discord.TextStyle.short,
			required=False,
			max_length=500,
			default=current_image_url
		))

		# Panel Color (hex format)
		current_color = embed_config.get("color", 0x2b2d31)
		color_hex = f"#{current_color:06x}" if isinstance(current_color, int) else "#2b2d31"
		self.add_item(discord.ui.TextInput(
			label="Panel Color",
			placeholder="Hex color code (e.g., #3498db, #5865F2)",
			style=discord.TextStyle.short,
			required=False,
			max_length=7,
			default=color_hex
		))

		# Button Style
		current_button_style = layout_config.get("button_style", "secondary")
		self.add_item(discord.ui.TextInput(
			label="Button Style",
			placeholder="primary, secondary, success, danger",
			style=discord.TextStyle.short,
			required=False,
			max_length=20,
			default=current_button_style
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle visual settings configuration submission using new customization system"""
		try:
			await interaction.response.defer(ephemeral=True)

			# Get guild ID
			guild_id = interaction.guild.id

			# Create updated configuration
			updated_config = self.current_config.copy()
			if "embed" not in updated_config:
				updated_config["embed"] = {}
			if "layout" not in updated_config:
				updated_config["layout"] = {}

			# Process image URL
			image_url = self.children[0].value.strip()
			updated_config["embed"]["image_url"] = image_url if image_url else None

			# Process color
			color_input = self.children[1].value.strip()
			if color_input:
				try:
					# Remove # if present and convert to int
					color_hex = color_input.lstrip('#')
					color_int = int(color_hex, 16)
					updated_config["embed"]["color"] = color_int
				except ValueError:
					await interaction.followup.send(
						"❌ Invalid color format. Please use hex format like #3498db",
						ephemeral=True
					)
					return

			# Process button style
			button_style = self.children[2].value.strip().lower()
			valid_styles = ["primary", "secondary", "success", "danger"]
			if button_style and button_style in valid_styles:
				updated_config["layout"]["button_style"] = button_style

			# Update customization using the manager
			success, errors = await self.customization_manager.update_customization(guild_id, updated_config)

			if success:
				embed = discord.Embed(
					title="✅ Visual Settings Updated",
					description="Panel colors, image, and button style have been configured successfully!",
					color=0x57f287  # Success green
				)
				
				# Show current settings
				settings_info = []
				if updated_config["embed"].get("image_url"):
					settings_info.append(f"**Image:** [View Image]({updated_config['embed']['image_url']})")
				if updated_config["embed"].get("color"):
					color_hex = f"#{updated_config['embed']['color']:06x}"
					settings_info.append(f"**Color:** {color_hex}")
				if updated_config["layout"].get("button_style"):
					settings_info.append(f"**Button Style:** {updated_config['layout']['button_style'].title()}")
				
				if settings_info:
					embed.add_field(
						name="Applied Settings",
						value="\n".join(settings_info),
						inline=False
					)
				
				embed.add_field(
					name="🎨 Next Steps",
					value="• Use 'Recreate Panel' to apply visual changes\n• Use other configuration options for more customization",
					inline=False
				)
				await interaction.followup.send(embed=embed, ephemeral=True)
			else:
				error_embed = discord.Embed(
					title="❌ Configuration Error",
					description=f"Failed to update visual settings:\n• {chr(10).join(errors)}",
					color=0xed4245  # Error red
				)
				await interaction.followup.send(embed=error_embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error updating visual settings: {e}")
			error_embed = discord.Embed(
				title="❌ Unexpected Error",
				description="An unexpected error occurred while updating visual settings.",
				color=0xed4245  # Error red
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)


class ModernBrandingModal(discord.ui.Modal):
	"""Modal for configuring branding and support information using the new customization system"""
	def __init__(self, current_config, customization_manager):
		super().__init__(title="Configure Branding & Support")
		self.current_config = current_config
		self.customization_manager = customization_manager

		# Get current branding configuration
		branding_config = current_config.get("branding", {})

		# Support Team Name
		current_team_name = branding_config.get("support_team", "")
		self.add_item(discord.ui.TextInput(
			label="Support Team Name",
			placeholder="e.g., Support Team, Help Desk Staff",
			style=discord.TextStyle.short,
			required=False,
			max_length=100,
			default=current_team_name
		))

		# Server Name Override
		current_server_name = branding_config.get("server_name", "")
		self.add_item(discord.ui.TextInput(
			label="Server Name Override",
			placeholder="Leave empty to use actual server name",
			style=discord.TextStyle.short,
			required=False,
			max_length=100,
			default=current_server_name
		))

		# Contact Information
		current_contact_info = branding_config.get("contact_info", "")
		self.add_item(discord.ui.TextInput(
			label="Additional Contact Info",
			placeholder="e.g., Email, website, or other contact methods",
			style=discord.TextStyle.paragraph,
			required=False,
			max_length=200,
			default=current_contact_info
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle branding configuration submission using new customization system"""
		try:
			await interaction.response.defer(ephemeral=True)

			# Get guild ID
			guild_id = interaction.guild.id

			# Create updated configuration
			updated_config = self.current_config.copy()
			if "branding" not in updated_config:
				updated_config["branding"] = {}

			# Update branding settings
			updated_config["branding"]["support_team"] = self.children[0].value.strip() if self.children[0].value.strip() else None
			updated_config["branding"]["server_name"] = self.children[1].value.strip() if self.children[1].value.strip() else None
			updated_config["branding"]["contact_info"] = self.children[2].value.strip() if self.children[2].value.strip() else None

			# Update customization using the manager
			success, errors = await self.customization_manager.update_customization(guild_id, updated_config)

			if success:
				embed = discord.Embed(
					title="✅ Branding & Support Updated",
					description="Support team information and branding have been configured successfully!",
					color=0x57f287  # Success green
				)
				
				# Show current settings
				settings_info = []
				if updated_config["branding"].get("support_team"):
					settings_info.append(f"**Support Team:** {updated_config['branding']['support_team']}")
				if updated_config["branding"].get("server_name"):
					settings_info.append(f"**Server Name:** {updated_config['branding']['server_name']}")
				if updated_config["branding"].get("contact_info"):
					settings_info.append(f"**Contact Info:** {updated_config['branding']['contact_info']}")
				
				if settings_info:
					embed.add_field(
						name="Applied Settings",
						value="\n".join(settings_info),
						inline=False
					)
				
				embed.add_field(
					name="🎨 Next Steps",
					value="• Use 'Recreate Panel' to apply branding changes\n• Configure other settings for complete customization",
					inline=False
				)
				await interaction.followup.send(embed=embed, ephemeral=True)
			else:
				error_embed = discord.Embed(
					title="❌ Configuration Error",
					description=f"Failed to update branding settings:\n• {chr(10).join(errors)}",
					color=0xed4245  # Error red
				)
				await interaction.followup.send(embed=error_embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error updating branding settings: {e}")
			error_embed = discord.Embed(
				title="❌ Unexpected Error",
				description="An unexpected error occurred while updating branding settings.",
				color=0xed4245  # Error red
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)


class ModernLayoutModal(discord.ui.Modal):
	"""Modal for configuring layout options using the new customization system"""
	def __init__(self, current_config, customization_manager):
		super().__init__(title="Configure Layout Options")
		self.current_config = current_config
		self.customization_manager = customization_manager

		# Get current layout configuration
		layout_config = current_config.get("layout", {})

		# Button Style
		current_button_style = layout_config.get("button_style", "secondary")
		self.add_item(discord.ui.TextInput(
			label="Button Style",
			placeholder="primary, secondary, success, danger",
			style=discord.TextStyle.short,
			required=False,
			max_length=20,
			default=current_button_style
		))

		# Show Categories (boolean as text)
		current_show_categories = layout_config.get("show_categories", True)
		self.add_item(discord.ui.TextInput(
			label="Show Categories",
			placeholder="true or false - whether to display category information",
			style=discord.TextStyle.short,
			required=False,
			max_length=5,
			default=str(current_show_categories).lower()
		))

		# Compact Mode (boolean as text)
		current_compact_mode = layout_config.get("compact_mode", False)
		self.add_item(discord.ui.TextInput(
			label="Compact Mode",
			placeholder="true or false - use compact layout for smaller panels",
			style=discord.TextStyle.short,
			required=False,
			max_length=5,
			default=str(current_compact_mode).lower()
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle layout configuration submission using new customization system"""
		try:
			await interaction.response.defer(ephemeral=True)

			# Get guild ID
			guild_id = interaction.guild.id

			# Create updated configuration
			updated_config = self.current_config.copy()
			if "layout" not in updated_config:
				updated_config["layout"] = {}

			# Process button style
			button_style = self.children[0].value.strip().lower()
			valid_styles = ["primary", "secondary", "success", "danger"]
			if button_style in valid_styles:
				updated_config["layout"]["button_style"] = button_style
			elif button_style:
				await interaction.followup.send(
					f"❌ Invalid button style '{button_style}'. Valid options: {', '.join(valid_styles)}",
					ephemeral=True
				)
				return

			# Process show categories
			show_categories_input = self.children[1].value.strip().lower()
			if show_categories_input in ["true", "false"]:
				updated_config["layout"]["show_categories"] = show_categories_input == "true"
			elif show_categories_input:
				await interaction.followup.send(
					"❌ Invalid value for 'Show Categories'. Use 'true' or 'false'.",
					ephemeral=True
				)
				return

			# Process compact mode
			compact_mode_input = self.children[2].value.strip().lower()
			if compact_mode_input in ["true", "false"]:
				updated_config["layout"]["compact_mode"] = compact_mode_input == "true"
			elif compact_mode_input:
				await interaction.followup.send(
					"❌ Invalid value for 'Compact Mode'. Use 'true' or 'false'.",
					ephemeral=True
				)
				return

			# Update customization using the manager
			success, errors = await self.customization_manager.update_customization(guild_id, updated_config)

			if success:
				embed = discord.Embed(
					title="✅ Layout Options Updated",
					description="Panel layout and display options have been configured successfully!",
					color=0x57f287  # Success green
				)
				
				# Show current settings
				settings_info = []
				if updated_config["layout"].get("button_style"):
					settings_info.append(f"**Button Style:** {updated_config['layout']['button_style'].title()}")
				settings_info.append(f"**Show Categories:** {'Yes' if updated_config['layout'].get('show_categories', True) else 'No'}")
				settings_info.append(f"**Compact Mode:** {'Yes' if updated_config['layout'].get('compact_mode', False) else 'No'}")
				
				embed.add_field(
					name="Applied Settings",
					value="\n".join(settings_info),
					inline=False
				)
				
				embed.add_field(
					name="🎨 Next Steps",
					value="• Use 'Recreate Panel' to apply layout changes\n• Configure other settings for complete customization",
					inline=False
				)
				await interaction.followup.send(embed=embed, ephemeral=True)
			else:
				error_embed = discord.Embed(
					title="❌ Configuration Error",
					description=f"Failed to update layout settings:\n• {chr(10).join(errors)}",
					color=0xed4245  # Error red
				)
				await interaction.followup.send(embed=error_embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error updating layout settings: {e}")
			error_embed = discord.Embed(
				title="❌ Unexpected Error",
				description="An unexpected error occurred while updating layout settings.",
				color=0xed4245  # Error red
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)


class ModernAdvancedModal(discord.ui.Modal):
	"""Modal for configuring advanced settings using the new customization system"""
	def __init__(self, current_config, customization_manager):
		super().__init__(title="Configure Advanced Settings")
		self.current_config = current_config
		self.customization_manager = customization_manager

		# Get current advanced configuration
		advanced_config = current_config.get("advanced", {})

		# Auto Close Inactive
		current_auto_close = advanced_config.get("auto_close_inactive", False)
		self.add_item(discord.ui.TextInput(
			label="Auto Close Inactive Tickets",
			placeholder="true or false - automatically close inactive tickets",
			style=discord.TextStyle.short,
			required=False,
			max_length=5,
			default=str(current_auto_close).lower()
		))

		# Inactive Timeout Hours
		current_timeout = advanced_config.get("inactive_timeout_hours", 24)
		self.add_item(discord.ui.TextInput(
			label="Inactive Timeout (Hours)",
			placeholder="1-168 hours before auto-closing inactive tickets",
			style=discord.TextStyle.short,
			required=False,
			max_length=3,
			default=str(current_timeout)
		))

		# Require Reason
		current_require_reason = advanced_config.get("require_reason", False)
		self.add_item(discord.ui.TextInput(
			label="Require Close Reason",
			placeholder="true or false - require reason when closing tickets",
			style=discord.TextStyle.short,
			required=False,
			max_length=5,
			default=str(current_require_reason).lower()
		))

		# Allow User Close
		current_allow_user_close = advanced_config.get("allow_user_close", True)
		self.add_item(discord.ui.TextInput(
			label="Allow User Close",
			placeholder="true or false - allow users to close their own tickets",
			style=discord.TextStyle.short,
			required=False,
			max_length=5,
			default=str(current_allow_user_close).lower()
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle advanced settings configuration submission using new customization system"""
		try:
			await interaction.response.defer(ephemeral=True)

			# Get guild ID
			guild_id = interaction.guild.id

			# Create updated configuration
			updated_config = self.current_config.copy()
			if "advanced" not in updated_config:
				updated_config["advanced"] = {}

			# Process auto close inactive
			auto_close_input = self.children[0].value.strip().lower()
			if auto_close_input in ["true", "false"]:
				updated_config["advanced"]["auto_close_inactive"] = auto_close_input == "true"
			elif auto_close_input:
				await interaction.followup.send(
					"❌ Invalid value for 'Auto Close Inactive'. Use 'true' or 'false'.",
					ephemeral=True
				)
				return

			# Process timeout hours
			timeout_input = self.children[1].value.strip()
			if timeout_input:
				try:
					timeout_hours = int(timeout_input)
					if 1 <= timeout_hours <= 168:
						updated_config["advanced"]["inactive_timeout_hours"] = timeout_hours
					else:
						await interaction.followup.send(
							"❌ Timeout hours must be between 1 and 168 (1 week).",
							ephemeral=True
						)
						return
				except ValueError:
					await interaction.followup.send(
						"❌ Invalid timeout hours. Please enter a number between 1 and 168.",
						ephemeral=True
					)
					return

			# Process require reason
			require_reason_input = self.children[2].value.strip().lower()
			if require_reason_input in ["true", "false"]:
				updated_config["advanced"]["require_reason"] = require_reason_input == "true"
			elif require_reason_input:
				await interaction.followup.send(
					"❌ Invalid value for 'Require Close Reason'. Use 'true' or 'false'.",
					ephemeral=True
				)
				return

			# Process allow user close
			allow_user_close_input = self.children[3].value.strip().lower()
			if allow_user_close_input in ["true", "false"]:
				updated_config["advanced"]["allow_user_close"] = allow_user_close_input == "true"
			elif allow_user_close_input:
				await interaction.followup.send(
					"❌ Invalid value for 'Allow User Close'. Use 'true' or 'false'.",
					ephemeral=True
				)
				return

			# Update customization using the manager
			success, errors = await self.customization_manager.update_customization(guild_id, updated_config)

			if success:
				embed = discord.Embed(
					title="✅ Advanced Settings Updated",
					description="Advanced ticket system settings have been configured successfully!",
					color=0x57f287  # Success green
				)
				
				# Show current settings
				settings_info = []
				settings_info.append(f"**Auto Close Inactive:** {'Yes' if updated_config['advanced'].get('auto_close_inactive', False) else 'No'}")
				settings_info.append(f"**Timeout Hours:** {updated_config['advanced'].get('inactive_timeout_hours', 24)}")
				settings_info.append(f"**Require Close Reason:** {'Yes' if updated_config['advanced'].get('require_reason', False) else 'No'}")
				settings_info.append(f"**Allow User Close:** {'Yes' if updated_config['advanced'].get('allow_user_close', True) else 'No'}")
				
				embed.add_field(
					name="Applied Settings",
					value="\n".join(settings_info),
					inline=False
				)
				
				embed.add_field(
					name="🎨 Next Steps",
					value="• Advanced settings are applied immediately\n• Use 'Recreate Panel' to refresh the panel display",
					inline=False
				)
				await interaction.followup.send(embed=embed, ephemeral=True)
			else:
				error_embed = discord.Embed(
					title="❌ Configuration Error",
					description=f"Failed to update advanced settings:\n• {chr(10).join(errors)}",
					color=0xed4245  # Error red
				)
				await interaction.followup.send(embed=error_embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error updating advanced settings: {e}")
			error_embed = discord.Embed(
				title="❌ Unexpected Error",
				description="An unexpected error occurred while updating advanced settings.",
				color=0xed4245  # Error red
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)


class SupportInfoModal(discord.ui.Modal):
	"""Modal for configuring support team information"""
	def __init__(self):
		super().__init__(title="Configure Support Information")

		# Get current panel configuration
		panel_config = ticket_config.get("panel_config", {})

		# Support Team Name
		current_team_name = panel_config.get("support_team_name", "")
		self.add_item(discord.ui.TextInput(
			label="Support Team Name",
			placeholder="e.g., Technical Support, Customer Service (max 100 chars, optional)",
			style=discord.TextStyle.short,
			required=False,
			max_length=100,
			default=current_team_name
		))

		# Support Hours
		current_hours = panel_config.get("support_hours", "")
		self.add_item(discord.ui.TextInput(
			label="Support Hours",
			placeholder="e.g., Monday-Friday: 9AM-5PM EST (max 150 chars, optional)",
			style=discord.TextStyle.short,
			required=False,
			max_length=150,
			default=current_hours
		))

		# Response Time
		current_response_time = panel_config.get("response_time", "")
		self.add_item(discord.ui.TextInput(
			label="Response Time",
			placeholder="e.g., Within 24 hours during business days (max 100 chars, optional)",
			style=discord.TextStyle.short,
			required=False,
			max_length=100,
			default=current_response_time
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle support information configuration submission"""
		try:
			await interaction.response.defer(ephemeral=True)

			# Get or create panel_config
			if "panel_config" not in ticket_config:
				ticket_config["panel_config"] = {}

			# Update support information
			ticket_config["panel_config"]["support_team_name"] = self.children[0].value
			ticket_config["panel_config"]["support_hours"] = self.children[1].value
			ticket_config["panel_config"]["response_time"] = self.children[2].value

			# Save configuration
			await save_ticket_data()

			# Success message
			embed = discord.Embed(
				title="✅ Support Information Updated",
				description="Support team information has been configured successfully!",
				color=0x00ff00
			)
			embed.add_field(
				name="🎨 Next Steps",
				value="• Use 'Button Settings' to configure button appearance\n• Use 'Recreate Ticket Panel' to apply changes",
				inline=False
			)
			await interaction.followup.send(embed=embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error updating support information: {e}")
			error_embed = discord.Embed(
				title="❌ Configuration Error",
				description="Failed to update support information.",
				color=0xff0000
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)


class CategoryDisplayModeModal(discord.ui.Modal):
	"""Modal for configuring category display mode (dropdown vs buttons) - ENHANCED PROFESSIONAL SYSTEM"""
	def __init__(self):
		super().__init__(title="Configure Category Display Mode")

		# Get current display mode
		panel_config = ticket_config.get("panel_config", {})
		current_mode = panel_config.get("category_display_mode", "dropdown")

		# Display Mode Selection
		self.add_item(discord.ui.TextInput(
			label="Display Mode",
			placeholder="Enter 'dropdown' for menu or 'buttons' for individual buttons",
			style=discord.TextStyle.short,
			required=True,
			max_length=20,
			default=current_mode
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle display mode configuration submission"""
		try:
			await interaction.response.defer(ephemeral=True)

			display_mode = self.children[0].value.lower().strip()

			# Validate input
			if display_mode not in ["dropdown", "buttons"]:
				error_embed = discord.Embed(
					title="❌ Invalid Display Mode",
					description="Please enter either 'dropdown' or 'buttons'.",
					color=0xff0000
				)
				await interaction.followup.send(embed=error_embed, ephemeral=True)
				return

			# Get or create panel_config
			if "panel_config" not in ticket_config:
				ticket_config["panel_config"] = {}

			# Update display mode
			ticket_config["panel_config"]["category_display_mode"] = display_mode

			# Save configuration
			await save_ticket_data()

			# Success message
			embed = discord.Embed(
				title="✅ Display Mode Updated",
				description=f"Category display mode set to: **{display_mode.title()}**",
				color=0x00ff00
			)

			if display_mode == "buttons":
				embed.add_field(
					name="🔘 Button Mode",
					value="Categories will appear as individual buttons. Use 'Button Configuration' to customize each category's appearance.",
					inline=False
				)
			else:
				embed.add_field(
					name="📋 Dropdown Mode",
					value="Categories will appear in a dropdown menu for a compact layout.",
					inline=False
				)

			embed.add_field(
				name="🎨 Next Steps",
				value="• Use 'Recreate Ticket Panel' to apply the new display mode\n• Configure individual button styles with 'Button Configuration'",
				inline=False
			)
			await interaction.followup.send(embed=embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error updating display mode: {e}")
			error_embed = discord.Embed(
				title="❌ Configuration Error",
				description="Failed to update display mode.",
				color=0xff0000
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)


class CategoryButtonConfigView(discord.ui.View):
	"""View for selecting categories to configure individual button settings - ENHANCED PROFESSIONAL SYSTEM"""
	def __init__(self):
		super().__init__(timeout=300)  # 5 minute timeout

		# Get available categories
		categories = ticket_config.get("categories", {})

		if not categories:
			# Add a disabled button if no categories exist
			no_categories_button = discord.ui.Button(
				label="No Categories Available",
				style=discord.ButtonStyle.secondary,
				disabled=True,
				emoji="❌"
			)
			self.add_item(no_categories_button)
		else:
			# Create dropdown for category selection
			options = []
			for category_id, category_info in categories.items():
				category_name = category_info.get("name", "Unknown Category")
				options.append(discord.SelectOption(
					label=category_name,
					value=str(category_id),
					description=f"Configure button appearance for {category_name}",
					emoji="🔘"
				))

			# Add dropdown (max 25 options)
			if len(options) <= 25:
				category_select = CategorySelectDropdown(options)
				self.add_item(category_select)
			else:
				# If too many categories, show error
				error_button = discord.ui.Button(
					label="Too Many Categories",
					style=discord.ButtonStyle.secondary,
					disabled=True,
					emoji="⚠️"
				)
				self.add_item(error_button)


class CategorySelectDropdown(discord.ui.Select):
	"""Dropdown for selecting a category to configure - ENHANCED PROFESSIONAL SYSTEM"""
	def __init__(self, options):
		super().__init__(
			placeholder="Select a category to configure...",
			options=options,
			min_values=1,
			max_values=1
		)

	async def callback(self, interaction: discord.Interaction):
		"""Handle category selection"""
		try:
			category_id = self.values[0]
			categories = ticket_config.get("categories", {})
			category_info = categories.get(category_id)

			if not category_info:
				await interaction.response.send_message(
					"❌ Selected category no longer exists.",
					ephemeral=True
				)
				return

			# Open category-specific configuration modal
			modal = CategorySpecificButtonModal(category_id, category_info)
			await interaction.response.send_modal(modal)

		except Exception as e:
			logger.error(f"Error in category selection: {e}")
			await interaction.response.send_message(
				"❌ An error occurred. Please try again.",
				ephemeral=True
			)


class CategorySpecificButtonModal(discord.ui.Modal):
	"""Modal for configuring individual category button appearance - ENHANCED PROFESSIONAL SYSTEM"""
	def __init__(self, category_id, category_info):
		self.category_id = str(category_id)
		self.category_name = category_info.get("name", "Unknown Category")

		super().__init__(title=f"Configure: {self.category_name}")

		# Get current button configuration
		panel_config = ticket_config.get("panel_config", {})
		button_config = panel_config.get("button_config", {})
		category_configs = button_config.get("category_configs", {})
		current_config = category_configs.get(self.category_id, {})

		# Button Style
		current_style = current_config.get("button_style", "primary")
		self.add_item(discord.ui.TextInput(
			label="Button Style",
			placeholder="primary, secondary, success, danger, or link",
			style=discord.TextStyle.short,
			required=False,
			max_length=20,
			default=current_style
		))

		# Custom Emoji
		current_emoji = current_config.get("custom_emoji", "🎫")
		self.add_item(discord.ui.TextInput(
			label="Custom Emoji",
			placeholder="e.g., 🎫, 📧, 💬 (single emoji)",
			style=discord.TextStyle.short,
			required=False,
			max_length=10,
			default=current_emoji
		))

		# Label Format
		current_format = current_config.get("label_format", "normal")
		self.add_item(discord.ui.TextInput(
			label="Label Format",
			placeholder="normal, uppercase, lowercase, or title",
			style=discord.TextStyle.short,
			required=False,
			max_length=20,
			default=current_format
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle category button configuration submission"""
		try:
			await interaction.response.defer(ephemeral=True)

			# Get input values
			button_style = self.children[0].value.lower().strip() or "primary"
			custom_emoji = self.children[1].value.strip() or "🎫"
			label_format = self.children[2].value.lower().strip() or "normal"

			# Validate button style
			valid_styles = ["primary", "secondary", "success", "danger", "link"]
			if button_style not in valid_styles:
				button_style = "primary"

			# Validate label format
			valid_formats = ["normal", "uppercase", "lowercase", "title"]
			if label_format not in valid_formats:
				label_format = "normal"

			# Get or create configuration structure
			if "panel_config" not in ticket_config:
				ticket_config["panel_config"] = {}
			if "button_config" not in ticket_config["panel_config"]:
				ticket_config["panel_config"]["button_config"] = {}
			if "category_configs" not in ticket_config["panel_config"]["button_config"]:
				ticket_config["panel_config"]["button_config"]["category_configs"] = {}

			# Update category-specific configuration
			ticket_config["panel_config"]["button_config"]["category_configs"][self.category_id] = {
				"button_style": button_style,
				"custom_emoji": custom_emoji,
				"label_format": label_format
			}

			# Save configuration
			await save_ticket_data()

			# Success message
			embed = discord.Embed(
				title="✅ Button Configuration Updated",
				description=f"Button appearance for **{self.category_name}** has been configured!",
				color=0x00ff00
			)
			embed.add_field(
				name="🎨 Configuration Applied",
				value=f"**Style:** {button_style.title()}\n**Emoji:** {custom_emoji}\n**Label Format:** {label_format.title()}",
				inline=False
			)
			embed.add_field(
				name="🔄 Next Steps",
				value="• Set display mode to 'buttons' in 'Category Display Mode'\n• Use 'Recreate Ticket Panel' to apply changes",
				inline=False
			)
			await interaction.followup.send(embed=embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error updating category button config: {e}")
			error_embed = discord.Embed(
				title="❌ Configuration Error",
				description="Failed to update button configuration.",
				color=0xff0000
			)
			await interaction.followup.send(embed=error_embed, ephemeral=True)


# Removed unused TicketView class that was causing interaction conflicts
# The ticket system will use ModernTicketPanelView with modern buttons

class TicketChannel(View):
	def __init__(self):
		super().__init__(timeout=None)

		# Add ticket management buttons
		close_btn = Button(label="Close", style=discord.ButtonStyle.red, custom_id="close_ticket")
		close_reason_btn = Button(label="Close With Reason", style=discord.ButtonStyle.red, custom_id="close_with_reason")

		self.add_item(close_btn)
		self.add_item(close_reason_btn)
		# Removed claim button as it's not functional




# Duplicate function removed - using the correct version below




async def load_ticket_data():
	"""Load ticket configuration and data from MongoDB"""
	global ticket_config, active_tickets

	try:
		# Use the global MongoDB connection that's already established
		# Load ticket configuration from MongoDB
		data = ticket_config_collection.find_one({"_id": "ticket_config"})
		if data:
			# Load config
			if 'config' in data:
				ticket_config.update(data['config'])

				# Ensure welcome_image_url is properly loaded
				if 'welcome_image_url' in data['config']:
					ticket_config['welcome_image_url'] = data['config']['welcome_image_url']
					print(f"Loaded welcome image URL: {ticket_config['welcome_image_url']}")

			# Load active tickets and counter
			active_tickets.update(data.get('active_tickets', {}))

			# Load last ticket number
			if 'last_ticket_number' in data:
				ticket_config['last_ticket_number'] = data['last_ticket_number']

			print(f"Loaded ticket configuration from MongoDB: {ticket_config}")
			print(f"Loaded active tickets: {active_tickets}")
			print(f"Loaded last ticket number: {ticket_config.get('last_ticket_number', 0)}")

			# Recreate panel if ticket channel exists
			if ticket_config.get("ticket_channel"):
				channel = bot.get_channel(ticket_config["ticket_channel"])
				if channel:
					# TODO: Replace with create_modern_ticket_panel(channel)
					pass
					print("Recreated ticket panel successfully")
				else:
					print(f"Warning: Could not find ticket channel {ticket_config['ticket_channel']}")
		else:
			print("No ticket configuration found in MongoDB. Starting with empty configuration.")

		return True

	except Exception as e:
		print(f"Error loading ticket data from MongoDB: {e}")
		traceback.print_exc()
		return False

async def save_ticket_data():
	"""Save ticket configuration and data to MongoDB"""
	try:
		# Ensure all message settings are properly included
		message_settings = {
			"welcome_message": ticket_config.get("welcome_message"),
			"welcome_image_url": ticket_config.get("welcome_image_url"),
			"support_team_name": ticket_config.get("support_team_name"),
			"custom_footer": ticket_config.get("custom_footer"),
			"support_hours": ticket_config.get("support_hours", "Monday-Friday: 9AM-5PM\nWeekends: Limited Support"),
			"response_time": ticket_config.get("response_time", "Our team typically responds within 24 hours during business days.")
		}

		# Update ticket_config with message settings
		for key, value in message_settings.items():
			if value is not None:
				ticket_config[key] = value

		# Prepare data for MongoDB using the global connection
		data = {
			"_id": "ticket_config",
			"config": ticket_config,
			"active_tickets": active_tickets,
			"last_ticket_number": ticket_config.get("last_ticket_number", 0)
		}

		# Use upsert to either update existing document or insert new one
		result = ticket_config_collection.replace_one(
			{"_id": "ticket_config"},
			data,
			upsert=True
		)

		logger.info(f"Saved ticket configuration successfully to MongoDB")
		logger.debug(f"Saved message settings: {message_settings}")
		return True

	except Exception as e:
		logger.error(f"Error saving ticket data to MongoDB: {e}")
		traceback.print_exc()
		return False






class TicketCategoryButton(discord.ui.Button):
    """Individual button for each ticket category - will be replaced with modern theming"""
    def __init__(self, category_id, category_info):
        category_name = category_info["name"]

        # Basic emoji mapping - will be replaced with modern customization system
        emoji = "🎫"  # Default
        style = discord.ButtonStyle.secondary

        # Basic category mapping - will be replaced with customizable theming
        name_lower = category_name.lower()
        if "report" in name_lower:
            emoji = "🚨"
            style = discord.ButtonStyle.danger
        elif "refund" in name_lower or "money" in name_lower:
            emoji = "💰"
            style = discord.ButtonStyle.success
        elif "bug" in name_lower:
            emoji = "🐛"
            style = discord.ButtonStyle.secondary
        elif "apply" in name_lower:
            emoji = "📋"
            style = discord.ButtonStyle.primary
        elif "help" in name_lower or "general" in name_lower:
            emoji = "❓"
            style = discord.ButtonStyle.secondary

        super().__init__(
            label=category_name,
            emoji=emoji,
            style=style,
            custom_id=f"ticket_category_btn_{category_id}"
        )
        self.category_id = category_id

class PersistentTicketCategoryButton(discord.ui.Button):
    """Persistent button for ticket categories that never times out"""
    def __init__(self, category_id, category_info):
        category_name = category_info["name"]

        # Professional themed emoji mapping
        emoji = "🎫"  # Default
        style = discord.ButtonStyle.secondary

        # Map category names to professional emojis and styles
        name_lower = category_name.lower()
        if "report" in name_lower and "player" in name_lower:
            emoji = "🚨"
            style = discord.ButtonStyle.danger
        elif "refund" in name_lower or "money" in name_lower:
            emoji = "💰"
            style = discord.ButtonStyle.success
        elif "bug" in name_lower:
            emoji = "🐛"
            style = discord.ButtonStyle.secondary
        elif "whitelist" in name_lower or "apply" in name_lower:
            emoji = "📋"
            style = discord.ButtonStyle.primary
        elif "help" in name_lower or "general" in name_lower:
            emoji = "❓"
            style = discord.ButtonStyle.secondary

        super().__init__(
            label=category_name,
            emoji=emoji,
            style=style,
            custom_id=f"persistent_ticket_{category_id}"  # Changed to avoid conflicts
        )
        self.category_id = category_id

    async def callback(self, interaction: discord.Interaction):
        """Handle button click with robust error handling"""
        try:
            # Verify category still exists
            categories = ticket_config.get("categories", {})
            if str(self.category_id) not in {str(k): v for k, v in categories.items()}:
                await interaction.response.send_message(
                    "❌ This ticket category is no longer available. Please contact an administrator.",
                    ephemeral=True
                )
                return

            # Create the ticket using the existing function
            await create_ticket(interaction, int(self.category_id))

        except Exception as e:
            logger.error(f"Error in persistent ticket button callback: {e}")
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        "❌ An error occurred while creating your ticket. Please try again or contact an administrator.",
                        ephemeral=True
                    )
            except:
                pass

class TicketCategoryDropdown(discord.ui.Select):
    """Dropdown menu for selecting ticket categories (legacy fallback)"""
    def __init__(self, categories):
        # Create options for each category
        options = []
        for category_id, category_info in categories.items():
            category_name = category_info["name"]
            description = category_info.get("description", "")

            # Choose emoji based on category name
            emoji = "🎫"  # Default ticket emoji
            if "support" in category_name.lower():
                emoji = "❓"
            elif "bug" in category_name.lower():
                emoji = "🐛"
            elif "feature" in category_name.lower():
                emoji = "✨"
            elif "general" in category_name.lower():
                emoji = "💬"

            # Truncate description if too long
            if len(description) > 100:
                description = description[:97] + "..."

            options.append(discord.SelectOption(
                label=category_name,
                description=description or "Click to create a ticket",
                value=str(category_id),
                emoji=emoji
            ))

        super().__init__(
            placeholder="🎫 Select a category to create a ticket...",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="ticket_category_dropdown_v2"  # Changed to v2 to avoid conflicts
        )

    # Callback removed - handled by global interaction handler in bot.py
    # This prevents duplicate interaction handling and conflicts

class ModernTicketCategoryButton(discord.ui.Button):
    """Modern professional button for ticket categories with enhanced styling and error handling"""
    
    def __init__(self, category_id: str, category_info: dict, customization_config: dict = None):
        """
        Initialize a modern ticket category button.
        
        Args:
            category_id (str): Unique identifier for the ticket category
            category_info (dict): Category information including name, description, etc.
            customization_config (dict): Optional customization configuration
        """
        self.category_id = str(category_id)
        self.category_info = category_info
        self.customization_config = customization_config or {}
        
        # Get category name and apply validation
        category_name = category_info.get("name", "Unknown Category")
        if len(category_name) > 80:  # Discord button label limit
            category_name = category_name[:77] + "..."
        
        # Modern professional styling with customization support
        emoji, style = self._get_modern_styling(category_name)
        
        super().__init__(
            label=category_name,
            emoji=emoji,
            style=style,
            custom_id=f"modern_ticket_{category_id}",
            row=self._calculate_button_row()
        )
    
    def _get_modern_styling(self, category_name: str) -> tuple[str, discord.ButtonStyle]:
        """
        Get modern professional styling for the button based on category name.
        
        Args:
            category_name (str): Name of the category
            
        Returns:
            tuple[str, discord.ButtonStyle]: Emoji and button style
        """
        # Check for custom styling in customization config
        custom_buttons = self.customization_config.get("buttons", {})
        if self.category_id in custom_buttons:
            custom_config = custom_buttons[self.category_id]
            emoji = custom_config.get("emoji", "🎫")
            style_name = custom_config.get("style", "secondary")
            
            # Convert style name to Discord style
            style_mapping = {
                "primary": discord.ButtonStyle.primary,
                "secondary": discord.ButtonStyle.secondary,
                "success": discord.ButtonStyle.success,
                "danger": discord.ButtonStyle.danger,
                "blurple": discord.ButtonStyle.primary,
                "grey": discord.ButtonStyle.secondary,
                "gray": discord.ButtonStyle.secondary,
                "green": discord.ButtonStyle.success,
                "red": discord.ButtonStyle.danger
            }
            style = style_mapping.get(style_name.lower(), discord.ButtonStyle.secondary)
            
            return emoji, style
        
        # Modern professional default styling based on category type
        name_lower = category_name.lower()
        
        # Professional category mappings with modern emojis
        # Check for bug first since "bug report" contains both "bug" and "report"
        if any(word in name_lower for word in ["bug", "error", "issue", "problem"]):
            return "🐛", discord.ButtonStyle.secondary
        elif any(word in name_lower for word in ["report", "violation", "abuse"]):
            return "🚨", discord.ButtonStyle.danger
        elif any(word in name_lower for word in ["refund", "payment", "billing", "money"]):
            return "💳", discord.ButtonStyle.success
        elif any(word in name_lower for word in ["application", "apply", "whitelist", "join"]):
            return "📋", discord.ButtonStyle.primary
        elif any(word in name_lower for word in ["help", "support", "general", "question"]):
            return "❓", discord.ButtonStyle.secondary
        elif any(word in name_lower for word in ["feature", "suggestion", "request"]):
            return "💡", discord.ButtonStyle.primary
        elif any(word in name_lower for word in ["appeal", "ban", "unban"]):
            return "⚖️", discord.ButtonStyle.danger
        elif any(word in name_lower for word in ["partnership", "business", "collab"]):
            return "🤝", discord.ButtonStyle.primary
        else:
            # Default professional styling
            return "🎫", discord.ButtonStyle.secondary
    
    def _calculate_button_row(self) -> int:
        """
        Calculate which row this button should be placed in for optimal layout.
        
        Returns:
            int: Row number (0-4)
        """
        # Simple hash-based row assignment for consistent layout
        # This ensures buttons are distributed evenly across rows
        hash_value = hash(self.category_id) % 4  # Use 4 rows max (0-3)
        return hash_value
    
    async def callback(self, interaction: discord.Interaction):
        """
        Handle button click with comprehensive error handling and user feedback.

        Args:
            interaction (discord.Interaction): The interaction that triggered this callback
        """
        try:
            # DO NOT defer the response here - let create_ticket handle it
            # This prevents the "Interaction already responded to" error when sending modals

            # Validate that the category still exists
            current_categories = ticket_config.get("categories", {})
            if self.category_id not in current_categories:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        content="❌ **Category Unavailable**\n"
                        "This ticket category is no longer available. The configuration may have been updated.\n"
                        "Please contact an administrator if this issue persists.",
                        ephemeral=True
                    )
                logger.warning(f"User {interaction.user.id} tried to access non-existent category {self.category_id}")
                return
            
            # Validate user permissions (if any restrictions are configured)
            if not await self._validate_user_permissions(interaction):
                return
            
            # Check rate limiting for ticket creation
            if not await self._check_rate_limits(interaction):
                return
            
            # Log the ticket creation attempt
            logger.info(f"User {interaction.user.id} ({interaction.user.name}) creating ticket for category {self.category_id}")
            
            # Create the ticket using the existing function
            # Convert category_id to int if it's numeric, otherwise use as string
            try:
                category_id_int = int(self.category_id)
                await create_ticket(interaction, category_id_int)
            except ValueError:
                # If category_id is not numeric, we need to handle this differently
                # For now, log the issue and send an error message
                logger.error(f"Category ID {self.category_id} is not numeric")
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        content="❌ **Configuration Error**\n"
                        "There is an issue with the ticket category configuration. "
                        "Please contact an administrator.",
                        ephemeral=True
                    )
            
        except discord.NotFound:
            logger.warning(f"Interaction not found for user {interaction.user.id} in category {self.category_id}")
        except discord.HTTPException as e:
            logger.error(f"HTTP error in modern ticket button callback: {e}")
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        content="❌ **Connection Error**\n"
                        "There was a problem communicating with Discord. Please try again in a moment.",
                        ephemeral=True
                    )
            except:
                pass
        except Exception as e:
            logger.error(f"Unexpected error in modern ticket button callback: {e}")
            traceback.print_exc()
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        content="❌ **System Error**\n"
                        "An unexpected error occurred while creating your ticket. "
                        "Please try again or contact an administrator if the problem persists.",
                        ephemeral=True
                    )
            except:
                pass
    
    async def _validate_user_permissions(self, interaction: discord.Interaction) -> bool:
        """
        Validate user permissions for creating tickets in this category.
        
        Args:
            interaction (discord.Interaction): The interaction to validate
            
        Returns:
            bool: True if user has permission, False otherwise
        """
        # Check if category has specific role requirements
        category_info = ticket_config.get("categories", {}).get(self.category_id, {})
        required_roles = category_info.get("required_roles", [])
        
        if required_roles:
            user_role_ids = [role.id for role in interaction.user.roles]
            if not any(role_id in user_role_ids for role_id in required_roles):
                required_role_names = []
                for role_id in required_roles:
                    role = interaction.guild.get_role(role_id)
                    if role:
                        required_role_names.append(role.name)
                
                if not interaction.response.is_done():
                    await interaction.response.send_message(
                        content=f"❌ **Insufficient Permissions**\n"
                        f"You need one of the following roles to create tickets in this category:\n"
                        f"• {', '.join(required_role_names)}",
                        ephemeral=True
                    )
                return False
        
        return True
    
    async def _check_rate_limits(self, interaction: discord.Interaction) -> bool:
        """
        Check rate limits for ticket creation.
        
        Args:
            interaction (discord.Interaction): The interaction to check
            
        Returns:
            bool: True if within rate limits, False otherwise
        """
        # Check maximum tickets per user
        max_tickets_config = ticket_config.get("max_tickets_per_user", 3)
        # Handle case where max_tickets_per_user might be returned as a dict
        if isinstance(max_tickets_config, dict):
            max_tickets = max_tickets_config.get("max_tickets_per_user", 3)
        else:
            max_tickets = max_tickets_config
            
        user_tickets = sum(1 for ticket in active_tickets.values() 
                          if ticket.get("user_id") == interaction.user.id)
        
        if user_tickets >= max_tickets:
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    content=f"❌ **Ticket Limit Reached**\n"
                    f"You already have {user_tickets} active tickets (maximum: {max_tickets}).\n"
                    f"Please close an existing ticket before creating a new one.",
                    ephemeral=True
                )
            return False
        
        return True


class ModernTicketCategoryDropdown(discord.ui.Select):
    """
    Modern professional dropdown menu for selecting ticket categories.
    Provides enhanced styling, better space efficiency, and comprehensive error handling.
    """

    def __init__(self, categories: dict, customization_config: dict = None):
        """
        Initialize the modern ticket category dropdown.

        Args:
            categories (dict): Dictionary of ticket categories
            customization_config (dict): Optional customization configuration
        """
        self.categories = categories
        self.customization_config = customization_config or {}

        # Create options for each category with modern styling
        options = []
        sorted_categories = sorted(
            categories.items(),
            key=lambda x: x[1].get("name", "").lower()
        )

        for category_id, category_info in sorted_categories:
            try:
                category_name = category_info.get("name", "Unknown Category")
                description = category_info.get("description", "")

                # Truncate name if too long for dropdown
                if len(category_name) > 100:
                    category_name = category_name[:97] + "..."

                # Truncate description if too long
                if len(description) > 100:
                    description = description[:97] + "..."
                elif not description:
                    description = "Click to create a ticket"

                # Get modern emoji based on category
                emoji = self._get_category_emoji(category_name)

                options.append(discord.SelectOption(
                    label=category_name,
                    description=description,
                    value=str(category_id),
                    emoji=emoji
                ))

            except Exception as e:
                logger.error(f"Error creating dropdown option for category {category_id}: {e}")
                continue

        # Ensure we have at least one option
        if not options:
            options.append(discord.SelectOption(
                label="No Categories Available",
                description="Please configure ticket categories",
                value="none",
                emoji="❌"
            ))

        super().__init__(
            placeholder="🎫 Select a category to create a ticket...",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="modern_ticket_dropdown_v3",
            row=0  # Place dropdown in first row
        )

        logger.info(f"ModernTicketCategoryDropdown initialized with {len(options)} options")

    def _get_category_emoji(self, category_name: str) -> str:
        """
        Get appropriate emoji for category based on name.

        Args:
            category_name (str): Name of the category

        Returns:
            str: Appropriate emoji for the category
        """
        name_lower = category_name.lower()

        # Enhanced emoji mapping for better visual appeal
        if any(word in name_lower for word in ["support", "help", "general"]):
            return "❓"
        elif any(word in name_lower for word in ["bug", "error", "issue"]):
            return "🐛"
        elif any(word in name_lower for word in ["feature", "suggestion", "request"]):
            return "✨"
        elif any(word in name_lower for word in ["report", "player", "staff"]):
            return "🚨"
        elif any(word in name_lower for word in ["refund", "money", "payment"]):
            return "💰"
        elif any(word in name_lower for word in ["whitelist", "application", "apply"]):
            return "📋"
        elif any(word in name_lower for word in ["ban", "appeal", "unban"]):
            return "⚖️"
        elif any(word in name_lower for word in ["job", "business", "work"]):
            return "💼"
        else:
            return "🎫"  # Default ticket emoji

    async def callback(self, interaction: discord.Interaction):
        """
        Handle dropdown selection with comprehensive error handling.

        Args:
            interaction (discord.Interaction): The interaction from the dropdown selection
        """
        try:
            # This callback is intentionally minimal - actual handling is done
            # by the global interaction handler in bot.py to prevent conflicts
            logger.info(f"ModernTicketCategoryDropdown callback triggered for category {self.values[0]}")

            # The interaction will be handled by the global handler
            # This prevents duplicate interaction handling

        except Exception as e:
            logger.error(f"Error in ModernTicketCategoryDropdown callback: {e}")
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "❌ An error occurred while processing your selection. Please try again.",
                    ephemeral=True
                )


class ModernTicketPanelView(discord.ui.View):
    """
    Modern professional ticket panel view with dropdown menu for space efficiency.
    Provides professional styling, comprehensive error handling, and customization support.
    """

    def __init__(self, categories: dict = None, customization_config: dict = None, use_dropdown: bool = True):
        """
        Initialize the modern ticket panel view.

        Args:
            categories (dict): Dictionary of ticket categories
            customization_config (dict): Optional customization configuration
            use_dropdown (bool): Whether to use dropdown menu instead of buttons
        """
        super().__init__(timeout=None)  # Persistent view that never times out

        self.categories = categories or ticket_config.get("categories", {})
        self.customization_config = customization_config or {}
        self.use_dropdown = use_dropdown

        # Add category selection components with modern styling
        if self.use_dropdown:
            self._add_modern_category_dropdown()
        else:
            self._add_modern_category_buttons()

        # Recreate panel button removed - panels auto-recreate on bot startup

        logger.info(f"ModernTicketPanelView initialized with {len(self.categories)} categories using {'dropdown' if use_dropdown else 'buttons'}")
    
    def _add_modern_category_dropdown(self):
        """Add modern category dropdown to the view for space efficiency."""
        # Clear existing items first
        self.clear_items()

        if not self.categories:
            logger.warning("No categories available for modern ticket panel")
            return

        try:
            dropdown = ModernTicketCategoryDropdown(
                categories=self.categories,
                customization_config=self.customization_config
            )
            self.add_item(dropdown)
            logger.info(f"Added modern dropdown with {len(self.categories)} categories")
        except Exception as e:
            logger.error(f"Error adding modern dropdown: {e}")
            # Fallback to buttons if dropdown fails
            self._add_modern_category_buttons()

    def _add_modern_category_buttons(self):
        """Add modern category buttons to the view with professional layout (fallback)."""
        # Clear existing items first
        self.clear_items()

        if not self.categories:
            logger.warning("No categories available for modern ticket panel")
            return

        # Sort categories for consistent layout
        sorted_categories = sorted(
            self.categories.items(),
            key=lambda x: x[1].get("name", "").lower()
        )

        # Add buttons with modern styling
        for category_id, category_info in sorted_categories:
            try:
                button = ModernTicketCategoryButton(
                    category_id=category_id,
                    category_info=category_info,
                    customization_config=self.customization_config
                )
                self.add_item(button)
                logger.info(f"Added button for category {category_id} with custom_id: {button.custom_id}")
            except Exception as e:
                logger.error(f"Error adding modern button for category {category_id}: {e}")
                continue

        logger.info(f"Added {len(self.children)} modern category buttons to panel")
    
    # Recreate button functionality removed - panels auto-recreate on bot startup
    
    async def on_timeout(self):
        """Handle view timeout (should never occur since timeout=None)."""
        logger.warning("ModernTicketPanelView timed out unexpectedly")
        # Don't disable the view - keep it active
        pass
    
    async def on_error(self, interaction: discord.Interaction, error: Exception, item):
        """
        Handle any errors that occur in the view.
        
        Args:
            interaction (discord.Interaction): The interaction that caused the error
            error (Exception): The error that occurred
            item: The item that caused the error
        """
        logger.error(f"Error in ModernTicketPanelView: {error}")
        traceback.print_exc()
        
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "❌ **System Error**\n"
                    "An error occurred while processing your request. "
                    "Please try again or contact an administrator if the problem persists.",
                    ephemeral=True
                )
            else:
                await interaction.followup.send(
                    "❌ **System Error**\n"
                    "An error occurred while processing your request. "
                    "Please try again or contact an administrator if the problem persists.",
                    ephemeral=True
                )
        except:
            # If we can't send an error message, just log it
            logger.error("Failed to send error message to user")
    
    def update_categories(self, new_categories: dict):
        """
        Update the categories and rebuild the UI components.

        Args:
            new_categories (dict): New categories dictionary
        """
        self.categories = new_categories
        if self.use_dropdown:
            self._add_modern_category_dropdown()
        else:
            self._add_modern_category_buttons()
        logger.info(f"ModernTicketPanelView categories updated: {len(new_categories)} categories using {'dropdown' if self.use_dropdown else 'buttons'}")

    def update_customization(self, new_customization: dict):
        """
        Update the customization configuration and rebuild the UI components.

        Args:
            new_customization (dict): New customization configuration
        """
        self.customization_config = new_customization
        if self.use_dropdown:
            self._add_modern_category_dropdown()
        else:
            self._add_modern_category_buttons()
        logger.info("ModernTicketPanelView customization updated")


async def create_modern_ticket_panel(channel: discord.TextChannel) -> bool:
    """
    Create a modern, professional ticket panel with comprehensive error handling and validation.
    
    This function integrates all modern components with robust error handling:
    - Comprehensive validation before panel creation
    - Graceful degradation with fallback configurations
    - Detailed error reporting and recovery mechanisms
    - Permission validation and user-friendly error messages
    
    Args:
        channel (discord.TextChannel): Channel to create the panel in
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info(f"Creating modern ticket panel in channel {channel.id}")
        
        # Initialize customization manager
        customization_manager = PanelCustomizationManager(
            ticket_config_collection, 
            save_ticket_data
        )
        
        # Get customization configuration for this guild
        guild_id = channel.guild.id
        customization_config = customization_manager.get_customization_config(guild_id)
        
        # Comprehensive validation before panel creation
        validation_result = await ticket_error_handler.validate_panel_creation(channel, customization_config)
        
        if not validation_result.is_valid:
            # Handle validation errors with recovery attempt
            logger.warning(f"Panel validation failed for channel {channel.id}")
            
            # Try to recover from the error
            success, error_embed = await ticket_error_handler.handle_panel_creation_error(
                channel, customization_config, Exception("Validation failed")
            )
            
            if not success:
                # Send error embed to channel if possible
                if error_embed:
                    try:
                        await channel.send(embed=error_embed)
                    except Exception as send_error:
                        logger.error(f"Could not send error embed: {send_error}")
                return False
            
            # Recovery succeeded, get the recovered configuration
            customization_config = await ticket_error_handler.recovery_system.recover_from_embed_error(
                customization_config, Exception("Validation failed")
            )
        
        # Validate categories exist
        if not ticket_config.get("categories"):
            logger.error("No ticket categories configured")
            
            # Create error embed for missing categories
            error_embed = ticket_error_handler.recovery_system.create_error_embed(
                "No ticket categories are configured. Please set up categories before creating a panel.",
                ErrorSeverity.HIGH
            )
            
            try:
                await channel.send(embed=error_embed)
            except Exception as send_error:
                logger.error(f"Could not send category error embed: {send_error}")
            
            return False
        
        # Create modern embed using ModernEmbedBuilder with comprehensive error handling
        embed_builder = ModernEmbedBuilder(customization_config)
        
        try:
            embed, warnings = await embed_builder.build_panel_embed()
        except Exception as embed_error:
            logger.error(f"Error building embed: {embed_error}")
            
            # Attempt recovery with fallback configuration
            success, error_embed = await ticket_error_handler.handle_panel_creation_error(
                channel, customization_config, embed_error
            )
            
            if not success:
                if error_embed:
                    try:
                        await channel.send(embed=error_embed)
                    except Exception as send_error:
                        logger.error(f"Could not send embed error: {send_error}")
                return False
            
            # Retry with recovered configuration
            recovery_config = await ticket_error_handler.recovery_system.recover_from_embed_error(
                customization_config, embed_error
            )
            embed_builder = ModernEmbedBuilder(recovery_config)
            embed, warnings = await embed_builder.build_panel_embed()
            warnings.append("🔄 Used fallback configuration due to embed creation error")
        
        # Create modern view with error handling
        try:
            view = ModernTicketPanelView(
                categories=ticket_config["categories"],
                customization_config=customization_config
            )
        except Exception as view_error:
            logger.error(f"Error creating view: {view_error}")
            
            # Create basic view without customization
            view = ModernTicketPanelView(
                categories=ticket_config["categories"],
                customization_config={}
            )
            warnings.append("🔄 Used basic view configuration due to customization error")
        
        # Clear any existing panel messages in the channel with error handling
        try:
            deleted_count = 0
            async for message in channel.history(limit=50):
                if message.author == bot.user and message.embeds:
                    # Check if this looks like a ticket panel
                    embed_title = message.embeds[0].title if message.embeds[0].title else ""
                    if "ticket" in embed_title.lower() or "support" in embed_title.lower():
                        try:
                            await message.delete()
                            deleted_count += 1
                            logger.info(f"Deleted old ticket panel message {message.id}")
                        except discord.NotFound:
                            # Message already deleted
                            continue
                        except discord.Forbidden:
                            logger.warning(f"No permission to delete message {message.id}")
                            continue
                        except Exception as delete_error:
                            logger.warning(f"Error deleting message {message.id}: {delete_error}")
                            continue
            
            if deleted_count > 0:
                logger.info(f"Cleared {deleted_count} old panel messages")
                
        except Exception as cleanup_error:
            logger.warning(f"Could not clear old panel messages: {cleanup_error}")
            warnings.append("⚠️ Could not clear old panel messages - new panel created alongside existing ones")
        
        # Send the new modern ticket panel with comprehensive error handling
        try:
            panel_message = await channel.send(embed=embed, view=view)
            logger.info(f"Successfully created modern ticket panel with message ID {panel_message.id}")

            # Register the persistent view with the bot (CRITICAL for persistent functionality)
            bot.add_view(view)
            logger.info(f"Registered persistent view with bot for panel {panel_message.id}")

            # Log warnings instead of displaying them
            if warnings:
                logger.info(f"Panel creation completed with {len(warnings)} warnings: {warnings}")

            # Store panel information for recreation functionality
            if "panel_info" not in ticket_config:
                ticket_config["panel_info"] = {}

            ticket_config["panel_info"] = {
                "channel_id": channel.id,
                "message_id": panel_message.id,
                "guild_id": guild_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "version": "modern_v1",
                "warnings_count": len(warnings)  # Track warning count
            }

            # Save configuration with error handling
            try:
                await save_ticket_data()
            except Exception as save_error:
                logger.error(f"Error saving ticket data: {save_error}")
                # Panel was created successfully, so don't fail here

            return True
            
        except discord.Forbidden as perm_error:
            logger.error(f"Missing permissions to send messages in channel {channel.id}: {perm_error}")
            
            # Try to recover from permission error
            recovery_message = await ticket_error_handler.recovery_system.recover_from_permission_error(channel, perm_error)
            if recovery_message:
                # Limited functionality available
                logger.info(f"Partial recovery possible: {recovery_message}")
            
            return False
            
        except discord.HTTPException as http_error:
            logger.error(f"HTTP error creating ticket panel: {http_error}")
            
            # Handle rate limiting and other HTTP errors
            if hasattr(http_error, 'status') and http_error.status == 429:
                logger.warning("Rate limited during panel creation")
                # Could implement retry logic here
            
            return False
            
    except Exception as e:
        logger.error(f"Critical error creating modern ticket panel: {e}")
        logger.error(traceback.format_exc())
        
        # Attempt final error recovery
        try:
            error_embed = ticket_error_handler.recovery_system.create_error_embed(
                f"Critical error during panel creation: {str(e)}\n\nPlease contact support if this persists.",
                ErrorSeverity.CRITICAL
            )
            await channel.send(embed=error_embed)
        except Exception as final_error:
            logger.error(f"Could not send final error message: {final_error}")
        
        return False


class PersistentTicketPanelView(discord.ui.View):
    """Persistent ticket panel view that never times out"""
    def __init__(self, categories=None):
        super().__init__(timeout=None)  # Never timeout

        # Store categories for potential recreation
        self.categories = categories or ticket_config.get("categories", {})

        # Add buttons for each category
        self._add_category_buttons()

    def _add_category_buttons(self):
        """Add category buttons to the view"""
        # Clear existing items
        self.clear_items()

        # Add buttons for each category
        if self.categories:
            for category_id, category_info in self.categories.items():
                button = PersistentTicketCategoryButton(category_id, category_info)
                self.add_item(button)
        else:
            # Fallback: Add default categories if none configured
            default_categories = {
                "1": {"name": "🚨 Report a Player"},
                "2": {"name": "💰 Refund Request"},
                "3": {"name": "🐛 Bug Report"},
                "4": {"name": "📋 Apply for Whitelist"},
                "5": {"name": "❓ General Help"}
            }
            for category_id, category_info in default_categories.items():
                button = PersistentTicketCategoryButton(category_id, category_info)
                self.add_item(button)

    async def on_timeout(self):
        """This should never be called since timeout=None, but handle it just in case"""
        logger.warning("Persistent ticket panel view timed out unexpectedly")
        # Don't disable the view - keep it active
        pass

    async def on_error(self, interaction: discord.Interaction, error: Exception, item):
        """Handle any errors that occur in the view"""
        logger.error(f"Error in persistent ticket panel view: {error}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "❌ An error occurred. Please try again or contact an administrator.",
                    ephemeral=True
                )
        except:
            pass


# Global storage for persistent views to prevent garbage collection
persistent_views = {}

class TicketPanelMonitor:
    """Monitors ticket panels and automatically recreates them if they become unresponsive"""

    def __init__(self):
        self.monitored_panels = {}  # {channel_id: {"message_id": id, "last_check": datetime}}
        self.check_interval = 300  # Check every 5 minutes
        self.is_running = False

    async def start_monitoring(self):
        """Start the panel monitoring task"""
        if self.is_running:
            return

        self.is_running = True
        logger.info("Starting ticket panel monitoring system")

        while self.is_running:
            try:
                await self.check_panels()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in panel monitoring: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def check_panels(self):
        """Check all monitored panels and recreate if necessary"""
        try:
            for channel_id, panel_info in list(self.monitored_panels.items()):
                try:
                    channel = bot.get_channel(int(channel_id))
                    if not channel:
                        # Channel no longer exists, remove from monitoring
                        del self.monitored_panels[channel_id]
                        continue

                    message_id = panel_info.get("message_id")
                    if not message_id:
                        continue

                    try:
                        # Try to fetch the message
                        message = await channel.fetch_message(int(message_id))

                        # Check if the message has a view attached
                        if not message.components:
                            logger.warning(f"Ticket panel in {channel.name} has no components, recreating...")
                            await self.recreate_panel(channel)
                        else:
                            logger.debug(f"Ticket panel in {channel.name} is healthy")

                    except discord.NotFound:
                        logger.warning(f"Ticket panel message not found in {channel.name}, recreating...")
                        await self.recreate_panel(channel)
                    except discord.HTTPException as e:
                        logger.warning(f"HTTP error checking panel in {channel.name}: {e}")

                except Exception as e:
                    logger.error(f"Error checking panel in channel {channel_id}: {e}")

        except Exception as e:
            logger.error(f"Error in check_panels: {e}")

    async def recreate_panel(self, channel):
        """Recreate a ticket panel in the specified channel"""
        try:
            logger.info(f"Recreating ticket panel in {channel.name}")

            # Clear old messages from the bot
            try:
                async for message in channel.history(limit=10):
                    if message.author == bot.user:
                        await message.delete()
            except:
                pass

            # Create new panel
            # TODO: Replace with create_modern_ticket_panel(channel)
            success = False
            if success:
                logger.info(f"Successfully recreated ticket panel in {channel.name}")
            else:
                logger.error(f"Failed to recreate ticket panel in {channel.name}")

        except Exception as e:
            logger.error(f"Error recreating panel in {channel.name}: {e}")

    def register_panel(self, channel_id, message_id):
        """Register a panel for monitoring"""
        self.monitored_panels[str(channel_id)] = {
            "message_id": str(message_id),
            "last_check": datetime.now()
        }
        logger.debug(f"Registered panel for monitoring: channel {channel_id}, message {message_id}")

    def stop_monitoring(self):
        """Stop the monitoring system"""
        self.is_running = False
        logger.info("Stopped ticket panel monitoring system")

# Global panel monitor instance
panel_monitor = TicketPanelMonitor()

async def clear_old_ticket_views():
    """Clear any old persistent views to prevent conflicts"""
    try:
        # This function helps prevent interaction conflicts by ensuring
        # we start with a clean slate for persistent views
        logger.info("Clearing old ticket views to prevent interaction conflicts")

        # Note: Discord.py automatically handles view cleanup when views timeout
        # or when new views with the same custom_id are created

    except Exception as e:
        logger.error(f"Error clearing old views: {e}")

# create_ticket_panel function removed - will be replaced with create_modern_ticket_panel








async def create_application_ticket(interaction: discord.Interaction, category_id: int, applicant_user, application_name: str):
	"""Create a new ticket for an application with the applicant automatically added - matches main ticket system styling"""
	try:
		# Get the next ticket number
		ticket_config["last_ticket_number"] = ticket_config.get("last_ticket_number", 0) + 1
		ticket_number = ticket_config["last_ticket_number"]

		# Get guild and category
		guild = interaction.guild
		category = guild.get_channel(category_id)

		if not category:
			logger.error(f"Category {category_id} not found")
			return False

		# Create the ticket channel
		channel_name = f"ticket-{ticket_number:04d}"
		overwrites = {
			guild.default_role: discord.PermissionOverwrite(read_messages=False),
			applicant_user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
			interaction.user: discord.PermissionOverwrite(read_messages=True, send_messages=True)  # Staff member who created it
		}

		# Add staff roles to overwrites
		for role_id in ticket_config.get("staff_roles", []):
			role = guild.get_role(role_id)
			if role:
				overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

		# Create the channel
		channel = await guild.create_text_channel(
			channel_name,
			category=category,
			overwrites=overwrites,
			topic=f"Application ticket for {applicant_user.display_name} - {application_name}"
		)

		# Store ticket data
		active_tickets[str(channel.id)] = {
			"user_id": applicant_user.id,
			"ticket_number": ticket_number,
			"claimed_by": None,
			"application_related": True,
			"application_name": application_name,
			"created_by_staff": interaction.user.id
		}

		# Save ticket data
		await save_ticket_data()

		# Send a separate message with the user mention to properly ping them
		mention_message = await channel.send(f"{applicant_user.mention}")

		# Create a clean, minimal ticket embed without title
		ticket_embed = discord.Embed(
			description="Support will be with you shortly.\nTo close this ticket react with 🔒",
			color=0x2b2d31  # Dark theme color
		)

		# Add dynamic footer using server name or support team name
		support_team_name = ticket_config.get("support_team_name")
		if support_team_name:
			footer_text = f"🔧 {support_team_name} - Ticketing without clutter"
		else:
			footer_text = f"🔧 {channel.guild.name} - Ticketing without clutter"
		ticket_embed.set_footer(text=footer_text)

		# Send the welcome embed first
		welcome_message = await channel.send(embed=ticket_embed)

		# Create questions embed for application details
		questions_embed = discord.Embed(color=0x2b2d31)  # Same dark color

		# Add application details as questions
		questions_embed.add_field(
			name="APPLICATION TYPE",
			value=f"```{application_name}```",
			inline=False
		)

		questions_embed.add_field(
			name="APPLICANT",
			value=f"```{applicant_user.display_name} (@{applicant_user.name})```",
			inline=False
		)

		questions_embed.add_field(
			name="CREATED BY STAFF",
			value=f"```{interaction.user.display_name} (@{interaction.user.name})```",
			inline=False
		)

		# Create a view with buttons for ticket actions
		view = discord.ui.View(timeout=None)  # Persistent view

		# Create buttons for ticket actions with consistent styling
		close_button = discord.ui.Button(
			label="Close Ticket",
			style=discord.ButtonStyle.secondary,
			custom_id="close_ticket",
			emoji="🔒",
			row=0
		)

		close_reason_button = discord.ui.Button(
			label="Close with Reason",
			style=discord.ButtonStyle.red,
			custom_id="close_with_reason",
			emoji="📝",
			row=0
		)

		# Add buttons to the view
		view.add_item(close_button)
		view.add_item(close_reason_button)

		# Send the questions embed with buttons
		ticket_message = await channel.send(embed=questions_embed, view=view)

		# Store message IDs including the mention message, welcome embed, and questions embed
		message_ids = [mention_message.id, welcome_message.id, ticket_message.id]

		# Update ticket info with message IDs (matching main system structure)
		active_tickets[str(channel.id)].update({
			"mention_message_id": mention_message.id,  # User mention message
			"ticket_message_id": ticket_message.id,  # Main ticket message with consolidated embed and buttons
			"message_ids": message_ids  # All message IDs
		})

		await save_ticket_data()

		logger.info(f"Application ticket created: {channel.name} (ID: {channel.id}) for user {applicant_user.id}")
		return True

	except Exception as e:
		logger.error(f"Error creating application ticket: {e}")
		traceback.print_exc()
		return False

async def create_ticket_direct(interaction: discord.Interaction, category_id: int):
	"""Create a new ticket directly from slash command (interaction not deferred)"""
	try:
		# Check if user is rate limited
		user_id = interaction.user.id
		rate_limit_key = f"ticket_create:{user_id}"

		# Simple rate limiting to prevent spam
		current_time = time.time()
		if rate_limit_key in ticket_rate_limiter.rate_limits:
			wait_time = ticket_rate_limiter.rate_limits[rate_limit_key] - current_time
			if wait_time > 0:
				# User is rate limited - send simple embed notification
				rate_limit_embed = discord.Embed(
					title="⏱️ Slow Down",
					description=f"Please wait **{wait_time:.0f} seconds** before creating another ticket.",
					color=0xff9900  # Orange for warning
				)
				rate_limit_embed.set_footer(text=get_dynamic_footer_text(interaction.guild))
				await interaction.response.send_message(embed=rate_limit_embed, ephemeral=True)
				return

		# Set rate limit (one ticket per 30 seconds per user)
		ticket_rate_limiter.rate_limits[rate_limit_key] = current_time + 30

		# Convert category_id to string for comparison since JSON stores keys as strings
		category_id_str = str(category_id)

		# Verify category exists
		if category_id_str not in ticket_config.get("categories", {}):
			await interaction.response.send_message(
				"This ticket category no longer exists. Please contact an administrator.",
				ephemeral=True
			)
			return

		# Check concurrent ticket limit before proceeding
		user_open_tickets = count_user_open_tickets(user_id)
		max_tickets = ticket_config.get("max_tickets_per_user", 3)

		if user_open_tickets >= max_tickets:
			# User has reached concurrent ticket limit
			limit_embed = discord.Embed(
				title="🚫 Ticket Limit Reached",
				description=f"You have reached the maximum of **{max_tickets}** open tickets.",
				color=0xff0000  # Red for error
			)

			limit_embed.add_field(
				name="Current Open Tickets",
				value=f"{user_open_tickets}/{max_tickets}",
				inline=True
			)

			limit_embed.add_field(
				name="What can you do?",
				value=(
					"• Close an existing ticket to create a new one\n"
					"• Use your existing tickets for additional questions\n"
					"• Contact staff directly if this is urgent"
				),
				inline=False
			)

			limit_embed.set_footer(text=get_dynamic_footer_text(interaction.guild))
			await interaction.response.send_message(embed=limit_embed, ephemeral=True)
			return

		# Get the next ticket number (but don't increment yet - only increment on successful creation)
		next_ticket_number = ticket_config.get("last_ticket_number", 0) + 1

		# Create and send modal with the prospective ticket number
		modal = TicketModal(category_id, next_ticket_number)

		# Send modal - this is our response
		await interaction.response.send_modal(modal)

	except discord.NotFound:
		logger.error("Interaction not found or expired")
	except discord.HTTPException as e:
		if e.code == 40060:  # Interaction already acknowledged
			logger.warning("Interaction already acknowledged - this is expected in some cases")
		else:
			logger.error(f"Discord HTTP error creating ticket: {e}")
	except Exception as e:
		logger.error(f"Error creating ticket: {e}")
		traceback.print_exc()

		# Only try to send error message if we haven't responded yet
		if not interaction.response.is_done():
			try:
				await interaction.response.send_message(
					"An error occurred while creating the ticket. Please try again later.",
					ephemeral=True
				)
			except Exception as follow_up_error:
				logger.error(f"Could not send error message: {follow_up_error}")

async def create_ticket(interaction: discord.Interaction, category_id: int):
	"""Create a new ticket with bulletproof interaction handling (for deferred interactions)"""
	# Flag to track if we've responded to the interaction
	interaction_responded = False

	try:
		# Check if user is rate limited
		user_id = interaction.user.id
		rate_limit_key = f"ticket_create:{user_id}"

		# Simple rate limiting to prevent spam
		current_time = time.time()
		if rate_limit_key in ticket_rate_limiter.rate_limits:
			wait_time = ticket_rate_limiter.rate_limits[rate_limit_key] - current_time
			if wait_time > 0:
				# User is rate limited - send simple embed notification
				if not interaction.response.is_done():
					rate_limit_embed = discord.Embed(
						title="⏱️ Slow Down",
						description=f"Please wait **{wait_time:.0f} seconds** before creating another ticket.",
						color=0xff9900  # Orange for warning
					)
					rate_limit_embed.set_footer(text=get_dynamic_footer_text(interaction.guild))
					await interaction.response.send_message(embed=rate_limit_embed, ephemeral=True)
					interaction_responded = True
				return

		# Set rate limit (one ticket per 30 seconds per user)
		ticket_rate_limiter.rate_limits[rate_limit_key] = current_time + 30

		# Convert category_id to string for comparison since JSON stores keys as strings
		category_id_str = str(category_id)

		# Verify category exists
		if category_id_str not in ticket_config.get("categories", {}):
			if not interaction.response.is_done():
				await interaction.response.send_message(
					"This ticket category no longer exists. Please contact an administrator.",
					ephemeral=True
				)
				interaction_responded = True
			return

		# Check concurrent ticket limit before proceeding
		user_open_tickets = count_user_open_tickets(user_id)
		max_tickets = ticket_config.get("max_tickets_per_user", 3)

		if user_open_tickets >= max_tickets:
			# User has reached concurrent ticket limit
			if not interaction.response.is_done():
				limit_embed = discord.Embed(
					title="🚫 Ticket Limit Reached",
					description=f"You have reached the maximum of **{max_tickets}** open tickets.",
					color=0xff0000  # Red for error
				)

				limit_embed.add_field(
					name="Current Open Tickets",
					value=f"{user_open_tickets}/{max_tickets}",
					inline=True
				)

				limit_embed.add_field(
					name="What can you do?",
					value=(
						"• Close an existing ticket to create a new one\n"
						"• Use your existing tickets for additional questions\n"
						"• Contact staff directly if this is urgent"
					),
					inline=False
				)

				limit_embed.set_footer(text=get_dynamic_footer_text(interaction.guild))
				await interaction.response.send_message(embed=limit_embed, ephemeral=True)
				interaction_responded = True
			return

		# Get the next ticket number (but don't increment yet - only increment on successful creation)
		next_ticket_number = ticket_config.get("last_ticket_number", 0) + 1

		# Create and send modal with the prospective ticket number
		modal = TicketModal(category_id, next_ticket_number)

		# Send modal - this is our final response
		if not interaction.response.is_done():
			await interaction.response.send_modal(modal)
			interaction_responded = True
		else:
			logger.warning("Interaction already responded to when trying to send modal")

	except discord.NotFound:
		logger.error("Interaction not found or expired")
	except discord.HTTPException as e:
		if e.code == 40060:  # Interaction already acknowledged
			logger.warning("Interaction already acknowledged - this is expected in some cases")
		else:
			logger.error(f"Discord HTTP error creating ticket: {e}")
	except Exception as e:
		logger.error(f"Error creating ticket: {e}")
		traceback.print_exc()

		# Only try to send error message if we haven't responded yet
		if not interaction_responded and not interaction.response.is_done():
			try:
				await interaction.response.send_message(
					"An error occurred while creating the ticket. Please try again later.",
					ephemeral=True
				)
			except Exception as follow_up_error:
				logger.error(f"Could not send error message: {follow_up_error}")

@bot.command(name="recreate_ticket_panel", description="Recreate the ticket panel with black theme")
@commands.has_permissions(administrator=True)
async def recreate_ticket_panel(ctx):
    """Recreate the ticket panel with black theme"""
    try:
        # Check if ticket channel is set
        if not ticket_config.get("ticket_channel"):
            await ctx.send("No ticket channel is configured. Please set a ticket channel first.")
            return

        # Get the channel
        channel = ctx.guild.get_channel(ticket_config["ticket_channel"])
        if not channel:
            await ctx.send("The configured ticket channel no longer exists. Please set a new one.")
            return

        # Create the ticket panel
        # TODO: Replace with create_modern_ticket_panel(channel)
        success = False

        if success:
            await ctx.send(f"Ticket panel recreated successfully in {channel.mention}.")
        else:
            await ctx.send("There was an error recreating the ticket panel. Please check the logs.")
    except Exception as e:
        logger.error(f"Error recreating ticket panel: {e}")
        traceback.print_exc()
        await ctx.send(f"Error: {str(e)}")

@bot.command(name="debug_ticket_image", description="Debug the ticket welcome image")
@commands.has_permissions(administrator=True)
async def debug_ticket_image(ctx):
	"""Debug command to check the current welcome image URL"""
	try:
		welcome_image_url = ticket_config.get("welcome_image_url")

		embed = discord.Embed(
			title="Ticket Image Debug",
			description="Current welcome image configuration",
			color=0x000000  # Pure black color for consistency
		)

		if welcome_image_url:
			embed.add_field(
				name="Welcome Image URL",
				value=welcome_image_url,
				inline=False
			)
			embed.set_image(url=welcome_image_url)
		else:
			embed.add_field(
				name="Welcome Image URL",
				value="No custom welcome image URL set",
				inline=False
			)
			# Show default image
			default_image = "https://cdn.discordapp.com/attachments/1321118061520224287/1366986791923617925/support-agent.png"
			embed.set_image(url=default_image)
			embed.add_field(
				name="Default Image",
				value=default_image,
				inline=False
			)

		# Add MongoDB data
		try:
			client = pymongo.MongoClient("mongodb://localhost:27017/")
			db = client["missminutesbot"]
			ticket_collection = db["ticket_config"]

			data = ticket_collection.find_one({"_id": "ticket_config"})
			if data and 'config' in data:
				mongo_image_url = data['config'].get('welcome_image_url', 'Not found in MongoDB')
				embed.add_field(
					name="MongoDB Image URL",
					value=mongo_image_url,
					inline=False
				)
		except Exception as e:
			embed.add_field(
				name="MongoDB Error",
				value=str(e),
				inline=False
			)

		await ctx.send(embed=embed)
	except Exception as e:
		logger.error(f"Error in debug_ticket_image: {e}")
		traceback.print_exc()
		await ctx.send(f"Error: {str(e)}")



# Ticket Channel Detection Utilities
def is_ticket_channel(channel: discord.TextChannel) -> bool:
	"""
	Check if a channel is a ticket channel based on naming pattern.
	Returns True if channel name matches ticket-XXXX or closed-XXXX pattern.
	"""
	if not channel or not hasattr(channel, 'name'):
		return False

	channel_name = channel.name.lower()

	# Check for active ticket pattern: ticket-XXXX
	if channel_name.startswith('ticket-'):
		# Verify the format is ticket-XXXX where XXXX is a number
		parts = channel_name.split('-', 1)
		if len(parts) == 2:
			try:
				int(parts[1])  # Check if the part after 'ticket-' is a number
				return True
			except ValueError:
				pass

	# Check for closed ticket pattern: closed-XXXX
	if channel_name.startswith('closed-'):
		# Verify the format is closed-XXXX where XXXX is a number
		parts = channel_name.split('-', 1)
		if len(parts) == 2:
			try:
				int(parts[1])  # Check if the part after 'closed-' is a number
				return True
			except ValueError:
				pass

	return False

def is_active_ticket_channel(channel: discord.TextChannel) -> bool:
	"""
	Check if a channel is an active (not closed) ticket channel.
	Returns True only for channels matching ticket-XXXX pattern.
	"""
	if not channel or not hasattr(channel, 'name'):
		return False

	channel_name = channel.name.lower()

	# Check for active ticket pattern: ticket-XXXX
	if channel_name.startswith('ticket-'):
		parts = channel_name.split('-', 1)
		if len(parts) == 2:
			try:
				int(parts[1])  # Check if the part after 'ticket-' is a number
				return True
			except ValueError:
				pass

	return False

def is_closed_ticket_channel(channel: discord.TextChannel) -> bool:
	"""
	Check if a channel is a closed ticket channel.
	Returns True only for channels matching closed-XXXX pattern.
	"""
	if not channel or not hasattr(channel, 'name'):
		return False

	channel_name = channel.name.lower()

	# Check for closed ticket pattern: closed-XXXX
	if channel_name.startswith('closed-'):
		parts = channel_name.split('-', 1)
		if len(parts) == 2:
			try:
				int(parts[1])  # Check if the part after 'closed-' is a number
				return True
			except ValueError:
				pass

	return False

def get_ticket_number_from_channel(channel: discord.TextChannel) -> str:
	"""
	Extract ticket number from a ticket channel name.
	Returns the ticket number as a string, or None if not a valid ticket channel.
	"""
	if not is_ticket_channel(channel):
		return None

	channel_name = channel.name.lower()

	# Handle both ticket-XXXX and closed-XXXX patterns
	if channel_name.startswith('ticket-') or channel_name.startswith('closed-'):
		parts = channel_name.split('-', 1)
		if len(parts) == 2:
			return parts[1]

	return None

def validate_ticket_channel_context(interaction: discord.Interaction) -> tuple[bool, str]:
	"""
	Validate that an interaction is happening in a valid ticket channel context.
	Returns (is_valid, error_message).
	"""
	if not interaction.channel:
		return False, "This command can only be used in a channel."

	if not isinstance(interaction.channel, discord.TextChannel):
		return False, "This command can only be used in text channels."

	if not is_ticket_channel(interaction.channel):
		return False, "This command can only be used in ticket channels."

	return True, ""

# Context-Aware Command Registration System
class TicketCommandManager:
	"""Manages context-aware registration of ticket-specific slash commands"""

	def __init__(self, bot):
		self.bot = bot
		self.registered_guilds = set()  # Track guilds with ticket commands registered
		self.ticket_commands = []  # Will store the command objects

	async def register_ticket_commands_for_guild(self, guild: discord.Guild):
		"""Register ticket management commands for a specific guild"""
		if guild.id in self.registered_guilds:
			logger.debug(f"Ticket commands already registered for guild {guild.name}")
			return

		try:
			# Use the bot's existing command tree
			tree = self.bot.tree

			# Add ticket management commands to the guild tree
			await self._add_ticket_commands_to_tree(tree, guild)

			# Sync commands to the specific guild
			await tree.sync(guild=guild)

			self.registered_guilds.add(guild.id)
			logger.info(f"Successfully registered ticket commands for guild {guild.name}")

		except Exception as e:
			logger.error(f"Failed to register ticket commands for guild {guild.name}: {e}")

	async def _add_ticket_commands_to_tree(self, tree: app_commands.CommandTree, guild: discord.Guild):
		"""Add all ticket management commands to a command tree for a specific guild using command groups"""

		# Create the main ticket command group
		ticket_group = app_commands.Group(
			name="ticket",
			description="🎫 Comprehensive ticket management system",
			guild_ids=[guild.id]
		)

		# Close ticket subcommand
		@tree.command(name="close", description="Close the current ticket", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def close_ticket_cmd(interaction: discord.Interaction, reason: str = None):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				await interaction.response.defer(ephemeral=True)
				success, error = await close_ticket(interaction.channel.id, reason, interaction.user)

				# Only send error message if failed - success is shown via embed in channel
				if not success:
					await interaction.followup.send(f"Failed to close ticket: {error}", ephemeral=True)
			except Exception as e:
				logger.error(f"Error in close ticket command: {e}")
				await interaction.followup.send("An error occurred while closing the ticket.", ephemeral=True)

		# Add user to ticket command
		@tree.command(name="add", description="Add a user to the ticket", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def add_user_cmd(interaction: discord.Interaction, user: discord.Member):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				# Add user to channel permissions
				overwrites = interaction.channel.overwrites
				overwrites[user] = discord.PermissionOverwrite(read_messages=True, send_messages=True)
				await interaction.channel.edit(overwrites=overwrites)

				embed = discord.Embed(
					title="User Added",
					description=f"{user.mention} has been added to this ticket by {interaction.user.mention}",
					color=0x00ff00
				)
				# Send embed as interaction response to complete the command
				await interaction.response.send_message(embed=embed)

			except Exception as e:
				logger.error(f"Error in add user command: {e}")
				await interaction.response.send_message("An error occurred while adding the user to the ticket.", ephemeral=True)

		# Remove user from ticket command
		@tree.command(name="remove", description="Remove a user from the ticket", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def remove_user_cmd(interaction: discord.Interaction, user: discord.Member):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				# Remove user from channel permissions
				overwrites = interaction.channel.overwrites
				if user in overwrites:
					del overwrites[user]
					await interaction.channel.edit(overwrites=overwrites)

					embed = discord.Embed(
						title="User Removed",
						description=f"{user.mention} has been removed from this ticket by {interaction.user.mention}",
						color=0xff0000
					)
					# Send embed as interaction response to complete the command
					await interaction.response.send_message(embed=embed)
				else:
					await interaction.response.send_message(f"{user.mention} was not found in this ticket.", ephemeral=True)

			except Exception as e:
				logger.error(f"Error in remove user command: {e}")
				await interaction.response.send_message("An error occurred while removing the user from the ticket.", ephemeral=True)

		# Claim ticket command
		@tree.command(name="claim", description="Claim the ticket for support", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def claim_ticket_cmd(interaction: discord.Interaction):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				success, error = await claim_ticket(interaction.channel.id, interaction.user)

				if success:
					# Send acknowledgment - the claim_ticket function already sent the embed to channel
					await interaction.response.send_message("✅ Ticket claimed successfully!", ephemeral=True)
				else:
					await interaction.response.send_message(f"Failed to claim ticket: {error}", ephemeral=True)
			except Exception as e:
				logger.error(f"Error in claim ticket command: {e}")
				await interaction.response.send_message("An error occurred while claiming the ticket.", ephemeral=True)

		# Unclaim ticket command
		@tree.command(name="unclaim", description="Unclaim the ticket", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def unclaim_ticket_cmd(interaction: discord.Interaction):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				await interaction.response.defer(ephemeral=True)

				# Check if ticket exists and is claimed
				channel_id_str = str(interaction.channel.id)
				if channel_id_str not in active_tickets:
					await interaction.followup.send("This is not a valid ticket channel.", ephemeral=True)
					return

				ticket_data = active_tickets[channel_id_str]
				if not ticket_data.get('claimed_by'):
					await interaction.followup.send("This ticket is not currently claimed.", ephemeral=True)
					return

				# Unclaim the ticket
				ticket_data['claimed_by'] = None
				await save_ticket_data()

				# Send unclaim notification
				embed = discord.Embed(
					title="Ticket Unclaimed",
					description=f"This ticket has been unclaimed by {interaction.user.mention}",
					color=0x000000
				)
				await interaction.channel.send(embed=embed)
				# Removed duplicate followup message - embed shows the action was successful

			except Exception as e:
				logger.error(f"Error in unclaim ticket command: {e}")
				await interaction.followup.send("An error occurred while unclaiming the ticket.", ephemeral=True)

		# Generate transcript command
		@tree.command(name="transcript", description="Generate ticket transcript", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def transcript_cmd(interaction: discord.Interaction):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				# Use the existing transcript functionality
				await handle_transcript_button(interaction)
			except Exception as e:
				logger.error(f"Error in transcript command: {e}")
				await interaction.response.send_message("An error occurred while generating the transcript.", ephemeral=True)

		# Rename ticket command
		@tree.command(name="rename", description="Rename the ticket channel", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def rename_ticket_cmd(interaction: discord.Interaction, new_name: str):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				await interaction.response.defer(ephemeral=True)

				# Validate new name
				if not new_name or len(new_name) > 100:
					await interaction.followup.send("Please provide a valid name (1-100 characters).", ephemeral=True)
					return

				# Get ticket number to preserve it
				ticket_number = get_ticket_number_from_channel(interaction.channel)
				if not ticket_number:
					await interaction.followup.send("Could not determine ticket number.", ephemeral=True)
					return

				# Create new channel name preserving ticket format
				is_closed = is_closed_ticket_channel(interaction.channel)
				prefix = "closed" if is_closed else "ticket"
				sanitized_name = "".join(c for c in new_name if c.isalnum() or c in "-_").lower()
				new_channel_name = f"{prefix}-{ticket_number}-{sanitized_name}"

				# Rename the channel
				await interaction.channel.edit(name=new_channel_name)

				embed = discord.Embed(
					title="Ticket Renamed",
					description=f"Ticket renamed to: **{new_channel_name}** by {interaction.user.mention}",
					color=0x000000
				)
				await interaction.channel.send(embed=embed)
				# Removed duplicate followup message - embed shows the action was successful

			except Exception as e:
				logger.error(f"Error in rename ticket command: {e}")
				await interaction.followup.send("An error occurred while renaming the ticket.", ephemeral=True)

		# Lock ticket command
		@tree.command(name="lock", description="Lock the ticket channel", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def lock_ticket_cmd(interaction: discord.Interaction):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				# Get current overwrites and modify them to prevent sending messages
				overwrites = interaction.channel.overwrites

				# Lock for everyone except staff
				overwrites[interaction.guild.default_role] = discord.PermissionOverwrite(
					read_messages=False,
					send_messages=False
				)

				# Ensure staff can still access
				for role_id in ticket_config.get("staff_roles", []):
					role = interaction.guild.get_role(role_id)
					if role:
						overwrites[role] = discord.PermissionOverwrite(
							read_messages=True,
							send_messages=True
						)

				# Apply the overwrites
				await interaction.channel.edit(overwrites=overwrites)

				embed = discord.Embed(
					title="🔒 Ticket Locked",
					description=f"This ticket has been locked by {interaction.user.mention}",
					color=0xff0000
				)
				# Send embed as interaction response to complete the command
				await interaction.response.send_message(embed=embed)

			except Exception as e:
				logger.error(f"Error in lock ticket command: {e}")
				await interaction.response.send_message("An error occurred while locking the ticket.", ephemeral=True)

		# Unlock ticket command
		@tree.command(name="unlock", description="Unlock the ticket channel", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def unlock_ticket_cmd(interaction: discord.Interaction):
			# Validate ticket channel context
			is_valid, error_msg = validate_ticket_channel_context(interaction)
			if not is_valid:
				await interaction.response.send_message(error_msg, ephemeral=True)
				return

			# Check permissions
			if not interaction.user.guild_permissions.administrator:
				await interaction.response.send_message("You need administrator permissions to use this command.", ephemeral=True)
				return

			try:
				# Get ticket data to find original user
				channel_id_str = str(interaction.channel.id)
				ticket_data = active_tickets.get(channel_id_str, {})
				user_id = ticket_data.get('user_id')

				# Restore normal ticket permissions
				overwrites = {
					interaction.guild.default_role: discord.PermissionOverwrite(read_messages=False),
					interaction.guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
				}

				# Add original ticket user if found
				if user_id:
					user = interaction.guild.get_member(user_id)
					if user:
						overwrites[user] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

				# Add staff roles
				for role_id in ticket_config.get("staff_roles", []):
					role = interaction.guild.get_role(role_id)
					if role:
						overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

				# Apply the overwrites
				await interaction.channel.edit(overwrites=overwrites)

				embed = discord.Embed(
					title="🔓 Ticket Unlocked",
					description=f"This ticket has been unlocked by {interaction.user.mention}",
					color=0x00ff00
				)
				# Send embed as interaction response to complete the command
				await interaction.response.send_message(embed=embed)

			except Exception as e:
				logger.error(f"Error in unlock ticket command: {e}")
				await interaction.response.send_message("An error occurred while unlocking the ticket.", ephemeral=True)

		# Purge all tickets command
		@tree.command(name="purge", description="Delete's all the tickets in the server (Confirmation required)", guild=guild)
		@app_commands.default_permissions(administrator=True)
		async def purge_tickets_cmd(interaction: discord.Interaction):
			try:
				# Get all active tickets in this guild
				guild_tickets = []
				for channel_id, ticket_data in active_tickets.items():
					channel = interaction.guild.get_channel(int(channel_id))
					if channel:  # Channel exists in this guild
						guild_tickets.append((channel_id, channel, ticket_data))

				if not guild_tickets:
					await interaction.response.send_message("No active tickets found in this server.", ephemeral=True)
					return

				# Create confirmation view
				confirmation_view = PurgeConfirmationView(guild_tickets, interaction.user)

				# Create warning embed
				warning_embed = discord.Embed(
					title="⚠️ DANGER: Purge All Tickets",
					description=f"You are about to **permanently delete ALL {len(guild_tickets)} tickets** in this server.",
					color=0xff0000  # Red for danger
				)

				warning_embed.add_field(
					name="This action will:",
					value=(
						"• Delete all ticket channels\n"
						"• Remove all ticket data from database\n"
						"• **Cannot be undone**"
					),
					inline=False
				)

				warning_embed.add_field(
					name="Tickets to be deleted:",
					value=f"**{len(guild_tickets)}** active tickets",
					inline=True
				)

				warning_embed.set_footer(text="This action requires confirmation and cannot be reversed!")

				await interaction.response.send_message(embed=warning_embed, view=confirmation_view, ephemeral=True)

			except Exception as e:
				logger.error(f"Error in purge tickets command: {e}")
				await interaction.response.send_message("An error occurred while preparing the purge operation.", ephemeral=True)

	async def ensure_commands_for_ticket_channel(self, channel: discord.TextChannel):
		"""Ensure ticket commands are registered for a guild when a ticket channel exists"""
		guild = channel.guild
		if guild.id not in self.registered_guilds:
			await self.register_ticket_commands_for_guild(guild)

	async def cleanup_commands_for_guild_if_no_tickets(self, guild: discord.Guild):
		"""Remove ticket commands from a guild if no ticket channels exist"""
		# Check if there are any ticket channels in the guild
		has_ticket_channels = False
		for channel in guild.text_channels:
			if is_ticket_channel(channel):
				has_ticket_channels = True
				break

		# If no ticket channels exist, unregister commands
		if not has_ticket_channels and guild.id in self.registered_guilds:
			await self.unregister_ticket_commands_for_guild(guild)

	async def unregister_ticket_commands_for_guild(self, guild: discord.Guild):
		"""Remove ticket management commands from a specific guild"""
		if guild.id not in self.registered_guilds:
			logger.debug(f"No ticket commands registered for guild {guild.name}")
			return

		try:
			# Clear the guild's command tree
			tree = self.bot.tree
			tree.clear_commands(guild=guild)
			await tree.sync(guild=guild)

			# Remove from registered guilds
			self.registered_guilds.discard(guild.id)
			logger.info(f"Unregistered ticket commands for guild: {guild.name}")

		except Exception as e:
			logger.error(f"Failed to unregister ticket commands for guild {guild.name}: {e}")
			raise

# Global instance of the ticket command manager
ticket_command_manager = None

def get_ticket_command_manager():
	"""Get the global ticket command manager instance"""
	global ticket_command_manager
	if ticket_command_manager is None:
		from bot_instance import bot
		ticket_command_manager = TicketCommandManager(bot)
	return ticket_command_manager

def get_ticket_commands():
	return [setup_ticket_system]

async def claim_ticket(channel_id: int, staff_member: discord.Member):
	"""Claim a ticket"""
	try:
		if str(channel_id) not in active_tickets:
			return False, "This is not a ticket channel"

		ticket_data = active_tickets[str(channel_id)]

		# Check if ticket is already claimed
		if ticket_data.get('claimed_by'):
			return False, "Ticket is already claimed"

		channel = bot.get_channel(channel_id)
		if not channel:
			return False, "Channel not found"

		# Update ticket data
		ticket_data['claimed_by'] = staff_member.id
		await save_ticket_data()

		# Create claim embed
		embed = discord.Embed(
			title="Ticket Claimed",
			description=f"This ticket has been claimed by {staff_member.mention}",
			color=0x000000  # Pure black color for consistency
		)

		# Send claim notification
		await channel.send(embed=embed)

		# Update original ticket message if it exists
		if 'message_id' in ticket_data:
			try:
				message = await channel.fetch_message(ticket_data['message_id'])
				if message:
					original_embed = message.embeds[0]
					original_embed.add_field(
						name="Claimed By",
						value=staff_member.mention,
						inline=False
					)
					await message.edit(embed=original_embed)
			except:
				pass  # Original message might be deleted or inaccessible

		return True, None

	except Exception as e:
		print(f"Error claiming ticket: {e}")
		traceback.print_exc()
		return False, str(e)

async def reopen_ticket(channel_id: int, user):
	"""Reopen a closed ticket with optimized flow"""
	try:
		channel = user.guild.get_channel(channel_id)
		if not channel:
			return False, "Channel not found"

		if not channel.name.startswith('closed-'):
			return False, "This ticket is not closed"

		# Extract ticket number and prepare new name
		ticket_number = channel.name.split('-')[1]
		new_name = f"ticket-{ticket_number}"

		# Update channel name first
		await asyncio.sleep(1)  # Small initial delay
		await channel.edit(name=new_name)
		await asyncio.sleep(2)  # Wait between operations

		# Update permissions
		overwrites = {
			channel.guild.default_role: discord.PermissionOverwrite(read_messages=False),
			user.guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
		}

		for role_id in ticket_config.get("staff_roles", []):
			role = channel.guild.get_role(role_id)
			if role:
				overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

		await channel.edit(overwrites=overwrites)
		await asyncio.sleep(2)

		# Create single embed with all controls
		embed = discord.Embed(
			description=f"Ticket Controls\nOpened by {user.mention}",
			color=discord.Color.green()
		)

		view = View()
		close_btn = Button(
			label="Close",
			style=discord.ButtonStyle.red,
			custom_id="close_ticket",
			emoji="🔒"
		)
		delete_btn = Button(
			label="Delete",
			style=discord.ButtonStyle.red,
			custom_id="delete_ticket",
			emoji="🗑️"
		)
		transcript_btn = Button(
			label="Transcript",
			style=discord.ButtonStyle.blurple,
			custom_id="view_transcript",
			emoji="📜"
		)

		view.add_item(close_btn)
		view.add_item(delete_btn)
		view.add_item(transcript_btn)

		await channel.send(embed=embed, view=view)

		# Update ticket status
		channel_id_str = str(channel_id)
		active_tickets[channel_id_str] = {
			"status": "open",
			"reopened_by": user.id,
			"reopened_at": datetime.now().isoformat()
		}
		await save_ticket_data()

		return True, None

	except Exception as e:
		print(f"Error reopening ticket: {e}")
		return False, str(e)






class TicketOperationQueue:
	def __init__(self):
		self.queue = asyncio.Queue()
		self._running = False
		self.processing_lock = asyncio.Lock()
		self.operation_delays = {
			'rename': 2.0,
			'permissions': 1.0,
			'message': 0.5
		}

	async def add_operation(self, operation_type, func, *args, **kwargs):
		await self.queue.put((operation_type, func, args, kwargs))
		if not self._running:
			self._running = True
			asyncio.create_task(self._process_queue())

	async def _process_queue(self):
		while True:
			try:
				if self.queue.empty():
					self._running = False
					break

				async with self.processing_lock:
					op_type, func, args, kwargs = await self.queue.get()
					try:
						await func(*args, **kwargs)
						await asyncio.sleep(self.operation_delays.get(op_type, 1.0))
					except AttributeError as e:
						if "'str' object has no attribute 'to_dict'" in str(e):
							# Skip this error - it's a known issue with Discord object serialization
							logger.debug(f"Skipping serialization error in operation {op_type}: {e}")
						else:
							logger.error(f"AttributeError in operation {op_type}: {e}")
					except Exception as e:
						logger.error(f"Error in operation {op_type}: {e}")
					finally:
						self.queue.task_done()
			except Exception as e:
				print(f"Queue processing error: {e}")
				await asyncio.sleep(1)




async def close_ticket(channel_id: int, reason: str = None, closer: discord.Member = None):
    """Close a ticket with enhanced handling for high performance and professional appearance"""
    try:
        # Get channel with error handling
        channel = closer.guild.get_channel(channel_id)
        if not channel:
            return False, "Channel not found"

        # Check if channel is already closed
        if channel.name.startswith('closed-'):
            return False, "Ticket is already closed"

        # Start a task to collect messages while we do other operations
        message_collection_task = asyncio.create_task(collect_ticket_messages(channel))

        # Update channel name with rate limiting - do this first for visual feedback
        ticket_number = channel.name.split('-', 1)[1] if '-' in channel.name else "unknown"
        new_name = f"closed-{ticket_number}"

        try:
            await ticket_rate_limiter.execute(
                'edit_channel',
                channel.edit,
                name=new_name
            )
        except Exception as e:
            logger.warning(f"Error updating channel name: {e}")
            # Continue anyway - this is not critical

        # Send initial closure message to provide immediate feedback
        initial_close_embed = discord.Embed(
            title="Ticket Closing",
            description="This ticket is being closed and archived. Please wait...",
            color=0x2b2d31
        )
        initial_close_embed.set_footer(text="Preparing transcript...")

        initial_message = await channel.send(embed=initial_close_embed)

        # Update permissions with rate limiting
        overwrites = {
            channel.guild.default_role: discord.PermissionOverwrite(read_messages=False),
            closer.guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
        }

        # Add staff roles
        for role_id in ticket_config.get("staff_roles", []):
            role = channel.guild.get_role(role_id)
            if role:
                overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

        # Apply permission changes
        try:
            await ticket_rate_limiter.execute(
                'edit_channel',
                channel.edit,
                overwrites=overwrites
            )
        except Exception as e:
            logger.warning(f"Error updating channel permissions: {e}")
            # Continue anyway - this is not critical

        # Wait for message collection to complete
        try:
            messages = await asyncio.wait_for(message_collection_task, timeout=30.0)
        except asyncio.TimeoutError:
            logger.warning(f"Message collection timed out for ticket {ticket_number}")
            messages = []  # Use empty list if timed out
        except Exception as e:
            logger.error(f"Error collecting messages: {e}")
            messages = []

        # Prepare transcript data with enhanced metadata
        close_time = datetime.now()

        # Try to find when the ticket was created
        created_at = None
        created_by = None
        first_message = None

        if messages and len(messages) > 0:
            # Try to find the first message
            first_message = messages[0]
            created_at = first_message.get("timestamp")
            created_by = first_message.get("author_id")

        # If we couldn't determine creation time from messages, use channel creation time
        if not created_at and hasattr(channel, 'created_at'):
            created_at = channel.created_at.isoformat()

        # Calculate resolution time if possible (ensure timezone consistency)
        resolution_time = None
        if created_at:
            try:
                created_dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                # Ensure close_time is timezone-aware
                if close_time.tzinfo is None:
                    close_time = close_time.replace(tzinfo=timezone.utc)
                resolution_time = (close_time - created_dt).total_seconds() / 60  # in minutes
            except Exception as e:
                logger.error(f"Error calculating resolution time: {e}")
                pass

        # Extract all participants
        participants = set()
        staff_participants = set()

        for msg in messages:
            author_id = msg.get("author_id")
            if author_id:
                participants.add(author_id)

                # Check if author is staff
                author_roles = msg.get("author_roles", [])
                for role_id in ticket_config.get("staff_roles", []):
                    if role_id in author_roles:
                        staff_participants.add(author_id)
                        break

        # Get category information
        category_id = None
        category_name = "Support"
        if channel.category and channel.category.id:
            category_id = channel.category.id
            category_id_str = str(category_id)
            if category_id_str in ticket_config.get("categories", {}):
                category_name = ticket_config["categories"][category_id_str].get("name", "Support")

        # Prepare enhanced transcript data
        transcript_data = {
            "ticket_id": ticket_number,
            "guild_id": channel.guild.id,
            "channel_id": channel.id,
            "category": category_name,
            "category_id": category_id,
            "created_by": created_by,
            "created_at": created_at,
            "closed_by": closer.id,
            "closed_by_name": closer.name,
            "close_reason": reason,
            "closed_at": close_time.isoformat(),
            "resolution_time": resolution_time,
            "message_count": len(messages),
            "participants": list(participants),
            "staff_participants": list(staff_participants),
            "staff_roles": ticket_config.get("staff_roles", []),
            "messages": messages
        }

        # Store transcript in MongoDB asynchronously
        store_task = asyncio.create_task(store_transcript_data(transcript_data))

        # Create professional closure message with buttons
        close_embed = discord.Embed(
            title="🔒 Ticket Closed",
            description=f"This support ticket has been closed by staff and archived.",
            color=0x000000,  # Professional black theme
            timestamp=close_time
        )

        # Add ticket information
        close_embed.add_field(
            name="Ticket Information",
            value=f"**ID:** {ticket_number}\n**Category:** {category_name}",
            inline=True
        )

        # Add closure information
        close_embed.add_field(
            name="Closed By",
            value=f"{closer.mention}\n{closer.name}",
            inline=True
        )

        # Add reason if provided (staff-only feature)
        if reason:
            close_embed.add_field(
                name="Closure Reason",
                value=f"📝 {reason}",
                inline=False
            )

        # Add resolution time if available
        if resolution_time:
            hours = int(resolution_time // 60)
            minutes = int(resolution_time % 60)
            close_embed.add_field(
                name="Resolution Time",
                value=f"{hours}h {minutes}m",
                inline=True
            )

        # Add message count
        close_embed.add_field(
            name="Messages",
            value=str(len(messages)),
            inline=True
        )

        # Add footer
        close_embed.set_footer(text=f"Ticket #{ticket_number} • Transcript saved")

        # Create view with professional buttons
        view = View()

        # Add Reopen button
        reopen_btn = Button(
            label="Reopen Ticket",
            style=discord.ButtonStyle.green,
            custom_id="reopen_ticket",
            emoji="🔓"
        )

        # Add Delete button
        delete_btn = Button(
            label="Delete Ticket",
            style=discord.ButtonStyle.red,
            custom_id="delete_ticket",
            emoji="🗑️"
        )

        # Add Transcript button (staff only - sends transcript in channel)
        transcript_btn = Button(
            label="Transcript",
            style=discord.ButtonStyle.blurple,
            custom_id="view_transcript_channel",
            emoji="📜"
        )

        view.add_item(reopen_btn)
        view.add_item(delete_btn)
        view.add_item(transcript_btn)

        # Send final closure message
        await channel.send(embed=close_embed, view=view)

        # Try to edit the initial message to avoid clutter
        try:
            await initial_message.delete()
        except:
            pass  # Ignore if we can't delete it

        # Wait for transcript storage to complete first to get the transcript ID
        try:
            transcript_id = await asyncio.wait_for(store_task, timeout=10.0)
            logger.info(f"Stored transcript {transcript_id} for ticket {ticket_number}")

            # Add the transcript ID to the transcript data
            transcript_data["_id"] = transcript_id

            # Now send transcript to transcript channel if configured (with proper ID)
            if ticket_config.get("transcript_channel"):
                await send_transcript_to_channel(
                    closer.guild,
                    transcript_data,
                    ticket_config["transcript_channel"]
                )

        except asyncio.TimeoutError:
            logger.warning(f"Transcript storage timed out for ticket {ticket_number}")
            # Still send transcript to channel even if storage timed out, but without ID
            if ticket_config.get("transcript_channel"):
                await send_transcript_to_channel(
                    closer.guild,
                    transcript_data,
                    ticket_config["transcript_channel"]
                )
        except Exception as e:
            logger.error(f"Error storing transcript: {e}")
            # Still send transcript to channel even if storage failed, but without ID
            if ticket_config.get("transcript_channel"):
                await send_transcript_to_channel(
                    closer.guild,
                    transcript_data,
                    ticket_config["transcript_channel"]
                )

        # Update ticket status in memory and database
        channel_id_str = str(channel_id)
        active_tickets[channel_id_str] = {
            "status": "closed",
            "closed_by": closer.id,
            "closed_at": close_time.isoformat(),
            "reason": reason,
            "transcript_id": transcript_id if 'transcript_id' in locals() else None
        }

        # Save ticket data asynchronously
        asyncio.create_task(save_ticket_data())

        # Check if this was the last ticket channel in the guild and cleanup commands if needed
        try:
            command_manager = get_ticket_command_manager()
            await command_manager.cleanup_commands_for_guild_if_no_tickets(closer.guild)
        except Exception as e:
            logger.warning(f"Failed to cleanup ticket commands for guild {closer.guild.name}: {e}")

        return True, None

    except Exception as e:
        logger.error(f"Error closing ticket: {e}")
        traceback.print_exc()
        return False, str(e)

async def purge_all_tickets(guild: discord.Guild, guild_tickets: list):
    """Purge all tickets in a guild - DESTRUCTIVE OPERATION"""
    try:
        deleted_count = 0
        errors = []

        logger.warning(f"PURGE OPERATION INITIATED: Deleting {len(guild_tickets)} tickets in guild {guild.name}")

        # Delete each ticket channel
        for channel_id, channel, ticket_data in guild_tickets:
            try:
                # Remove from active tickets first
                if channel_id in active_tickets:
                    del active_tickets[channel_id]

                # Delete the Discord channel
                await channel.delete(reason="Ticket purge operation")
                deleted_count += 1

                logger.info(f"Deleted ticket channel: {channel.name} (ID: {channel_id})")

            except discord.NotFound:
                # Channel already deleted
                if channel_id in active_tickets:
                    del active_tickets[channel_id]
                deleted_count += 1

            except discord.Forbidden:
                errors.append(f"No permission to delete {channel.name}")
                logger.error(f"No permission to delete ticket channel: {channel.name}")

            except Exception as e:
                errors.append(f"Error deleting {channel.name}: {str(e)}")
                logger.error(f"Error deleting ticket channel {channel.name}: {e}")

        # Save the updated active_tickets to database
        await save_ticket_data()

        # Log the completion
        logger.warning(f"PURGE OPERATION COMPLETED: Deleted {deleted_count}/{len(guild_tickets)} tickets in guild {guild.name}")

        return True, deleted_count, errors

    except Exception as e:
        logger.error(f"Critical error during purge operation: {e}")
        traceback.print_exc()
        return False, 0, [f"Critical error: {str(e)}"]

async def collect_ticket_messages(channel):
    """Collect all messages from a ticket channel with enhanced metadata"""
    messages = []
    try:
        async for message in channel.history(limit=None, oldest_first=True):
            # Skip system messages
            if message.type != discord.MessageType.default and message.type != discord.MessageType.reply:
                continue

            # Extract attachments
            attachments = []
            for att in message.attachments:
                attachments.append(att.url)

            # Extract embeds
            embeds_content = []
            for embed in message.embeds:
                if embed.description:
                    embeds_content.append(embed.description)

            # Extract author roles if available
            author_roles = []
            if hasattr(message.author, 'roles'):
                author_roles = [role.id for role in message.author.roles]

            # Create message data with Discord username only (no display name fallbacks)
            # Enhanced reply information capture
            reply_info = None
            if message.reference and message.reference.message_id:
                reply_info = message.reference.message_id

            msg_data = {
                "message_id": message.id,
                "timestamp": message.created_at.isoformat(),
                "author_id": message.author.id,
                "author_name": message.author.name if message.author.name else "Unknown",
                "author_roles": author_roles,
                "content": message.content,
                "attachments": attachments,
                "embeds": embeds_content,
                "is_reply": message.reference is not None,
                "reply_to": reply_info
            }

            messages.append(msg_data)

    except Exception as e:
        logger.error(f"Error collecting ticket messages: {e}")

    return messages

async def store_transcript_data(transcript_data):
    """Store transcript data in MongoDB with optimized handling"""
    try:
        # Use the transcript manager for better performance
        from transcript_manager import transcript_manager

        # Store with enhanced manager
        transcript_id = await transcript_manager.store_transcript(transcript_data, transcript_data.get("messages", []))
        return transcript_id

    except ImportError:
        # Fall back to direct MongoDB storage if transcript manager not available
        try:
            result = transcript_collection.insert_one(transcript_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Error storing transcript directly: {e}")
            return None
    except Exception as e:
        logger.error(f"Error storing transcript: {e}")
        return None

async def send_transcript_to_channel(guild, transcript_data, transcript_channel_id):
    """Send a professional transcript notification to the transcript channel with enhanced formatting"""
    try:
        transcript_channel = guild.get_channel(transcript_channel_id)
        if not transcript_channel:
            logger.warning(f"Transcript channel {transcript_channel_id} not found")
            return False

        # Get basic ticket info
        ticket_id = transcript_data.get("ticket_id", "unknown")
        category = transcript_data.get("category", "Support")
        closed_by_id = transcript_data.get("closed_by")
        closed_by_name = transcript_data.get("closed_by_name", "Unknown")
        close_reason = transcript_data.get("close_reason", "No reason provided")
        message_count = transcript_data.get("message_count", 0)

        # Get resolution time if available
        resolution_time = transcript_data.get("resolution_time", 0)
        resolution_str = "N/A"
        if resolution_time is not None and resolution_time > 0:
            hours = int(resolution_time // 60)
            minutes = int(resolution_time % 60)
            resolution_str = f"{hours}h {minutes}m"

        # Get first response time if available
        first_response_time = transcript_data.get("first_response_time", 0)
        response_str = "N/A"
        if first_response_time is not None and first_response_time > 0:
            hours = int(first_response_time // 60)
            minutes = int(first_response_time % 60)
            response_str = f"{hours}h {minutes}m"

        # Get participant count
        participants = transcript_data.get("participants", [])
        participant_count = len(participants)

        # Get closer mention if possible
        closer_mention = f"<@{closed_by_id}>" if closed_by_id else closed_by_name

        # Format timestamps
        created_at = transcript_data.get('created_at', 'Unknown')
        closed_at = transcript_data.get('closed_at', 'Unknown')

        # Try to parse and format dates if they're ISO format
        try:
            if created_at != 'Unknown':
                created_dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                created_at = created_dt.strftime('%Y-%m-%d %H:%M:%S UTC')
        except:
            pass

        try:
            if closed_at != 'Unknown':
                closed_dt = datetime.fromisoformat(closed_at.replace('Z', '+00:00'))
                closed_at = closed_dt.strftime('%Y-%m-%d %H:%M:%S UTC')
        except:
            pass

        # Create a professional embed with modern design
        embed = discord.Embed(
            title=f"📜 Ticket #{ticket_id} Transcript",
            description=f"A support ticket has been closed by staff and archived.",
            color=0x000000,  # Professional black theme
            timestamp=datetime.now()
        )

        # Add ticket information section
        embed.add_field(
            name="Ticket Information",
            value=f"**ID:** {ticket_id}\n**Category:** {category}\n**Created:** {created_at}\n**Closed:** {closed_at}",
            inline=True
        )

        # Add metrics section
        embed.add_field(
            name="Metrics",
            value=f"**Resolution Time:** {resolution_str}\n**First Response:** {response_str}\n**Messages:** {message_count}\n**Participants:** {participant_count}",
            inline=True
        )

        # Add closure information
        embed.add_field(
            name="Closed By",
            value=closer_mention,
            inline=False
        )

        # Add reason if provided (staff-only feature)
        if close_reason and close_reason != "No reason provided":
            embed.add_field(
                name="Closure Reason",
                value=f"📝 {close_reason}",
                inline=False
            )

        # Add footer with transcript ID
        embed.set_footer(text=f"Transcript ID: {transcript_data.get('_id', 'Unknown')}")

        # Generate enhanced transcript files using the new system
        messages = transcript_data.get("messages", [])

        # Only create files if we have messages
        if messages:
            # Send the consolidated transcript embed to transcript channel
            await ticket_rate_limiter.execute(
                'send_transcript',
                transcript_channel.send,
                embed=embed
            )

            # Generate text transcript file
            try:
                text_content = await transcript_formatter.create_enhanced_text_transcript(transcript_data, guild)

                # Create text file
                transcript_file = discord.File(
                    io.StringIO(text_content),
                    filename=f"ticket-transcript-{ticket_id}.txt"
                )

                # Send text file with the main embed
                await ticket_rate_limiter.execute(
                    'send_transcript',
                    transcript_channel.send,
                    file=transcript_file
                )

            except Exception as text_error:
                logger.error(f"Error generating enhanced text transcript: {text_error}")
                # Fall back to basic text transcript
                transcript_lines = []
                for msg in messages:
                    timestamp = msg.get("timestamp", "")
                    author = msg.get("author_name", "Unknown")
                    content = msg.get("content", "")

                    # Try to format timestamp
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                    transcript_lines.append(f"[{timestamp}] {author}: {content}")

                    # Add attachments if any
                    for attachment in msg.get("attachments", []):
                        transcript_lines.append(f"[Attachment: {attachment}]")

                basic_transcript_content = "\n".join(transcript_lines)

                # Create basic text file
                basic_transcript_file = discord.File(
                    io.StringIO(basic_transcript_content),
                    filename=f"transcript-{ticket_id}.txt"
                )

                # Send basic text file
                await ticket_rate_limiter.execute(
                    'send_transcript',
                    transcript_channel.send,
                    file=basic_transcript_file
                )
        else:
            # Send embed only if no messages
            await ticket_rate_limiter.execute(
                'send_transcript',
                transcript_channel.send,
                embed=embed
            )

        return True

    except Exception as e:
        logger.error(f"Error sending transcript to channel: {e}")
        traceback.print_exc()
        return False

async def send_transcript_saved_notification(channel, ticket_id, transcript_id=None):
    """Send a professional notification that a transcript has been saved"""
    try:
        # Create a professional black-themed embed
        embed = discord.Embed(
            title="📜 Transcript Saved",
            description="The ticket transcript has been successfully saved and archived.",
            color=0x000000,  # Professional black theme
            timestamp=datetime.now()
        )

        # Add ticket information
        embed.add_field(
            name="Ticket Information",
            value=f"**Ticket ID:** {ticket_id}\n**Status:** Archived\n**Saved:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}",
            inline=False
        )

        # Add transcript ID if available
        if transcript_id:
            embed.add_field(
                name="Transcript ID",
                value=f"`{transcript_id}`",
                inline=True
            )

        # Add footer
        embed.set_footer(text="Ticket System • Transcript Archive")

        # Send the notification
        await channel.send(embed=embed)

    except Exception as e:
        logger.error(f"Error sending transcript saved notification: {e}")

class CloseWithReasonModal(discord.ui.Modal):
	"""Modal for staff to enter a closure reason"""
	def __init__(self):
		super().__init__(title="Close Ticket with Reason")

		self.reason_input = discord.ui.TextInput(
			label="Closure Reason",
			placeholder="Enter the reason for closing this ticket...",
			style=discord.TextStyle.paragraph,
			max_length=500,
			required=True
		)
		self.add_item(self.reason_input)

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle the modal submission"""
		try:
			# Check if user has staff role
			has_staff_role = False
			for role_id in ticket_config.get("staff_roles", []):
				role = interaction.guild.get_role(role_id)
				if role and role in interaction.user.roles:
					has_staff_role = True
					break

			if not has_staff_role:
				# Create professional error embed
				error_embed = discord.Embed(
					title="❌ Access Denied",
					description="Only staff members can close tickets with reasons.",
					color=0xff0000  # Red color for error
				)
				error_embed.add_field(
					name="Required Permission",
					value="Staff Role",
					inline=True
				)
				await interaction.response.send_message(
					embed=error_embed,
					ephemeral=True
				)
				return

			# Defer the response
			await interaction.response.defer(ephemeral=True)

			# Close the ticket with the provided reason
			reason = self.reason_input.value
			success, error = await close_ticket(interaction.channel_id, closer=interaction.user, reason=reason)

			if not success:
				await interaction.followup.send(
					f"Error closing ticket: {error}",
					ephemeral=True
				)

		except Exception as e:
			logger.error(f"Error in close with reason modal: {e}")
			traceback.print_exc()
			await interaction.followup.send(
				"An error occurred while closing the ticket.",
				ephemeral=True
			)

# CloseConfirmation class removed - not needed for current implementation
# Normal ticket closure is handled directly without confirmation modal

async def handle_claim_button(interaction: discord.Interaction):
	"""Handle the claim button click - staff only"""
	# Check if user has staff role
	has_staff_role = False
	for role_id in ticket_config.get("staff_roles", []):
		role = interaction.guild.get_role(role_id)
		if role and role in interaction.user.roles:
			has_staff_role = True
			break

	if not has_staff_role:
		await interaction.response.send_message(
			"Only staff members can claim tickets.",
			ephemeral=True
		)
		return

	# Claim the ticket - silently acknowledge the interaction
	await interaction.response.defer(ephemeral=True)
	success, error = await claim_ticket(interaction.channel_id, interaction.user)

	# Only send a message if there was an error
	if not success:
		await interaction.followup.send(
			f"Error claiming ticket: {error}",
			ephemeral=True
		)

# handle_close_button function removed - not needed for current implementation
# Normal ticket closure is handled directly by bot.py without confirmation modal








async def reopen_callback(interaction: discord.Interaction):
	try:
		# Check if user has staff role
		has_staff_role = False
		for role_id in ticket_config["staff_roles"]:
			role = interaction.guild.get_role(role_id)
			if role and role in interaction.user.roles:
				has_staff_role = True
				break

		if not has_staff_role:
			await interaction.response.send_message("You don't have permission to reopen tickets.", ephemeral=True)
			return

		# Defer the response immediately
		await interaction.response.defer(ephemeral=True)

		success, error = await reopen_ticket(interaction.channel.id, interaction.user)

		if success:
			await interaction.followup.send("Ticket reopened successfully!", ephemeral=True)
		else:
			await interaction.followup.send(f"Error reopening ticket: {error}", ephemeral=True)

	except Exception as e:
		print(f"Error in reopen_callback: {e}")
		await interaction.followup.send(f"Error reopening ticket: {str(e)}", ephemeral=True)


async def setup_buttons(channel, close_embed):
	view = View()

	# Add Reopen button
	reopen_btn = Button(label="🔓 Reopen", custom_id="reopen_ticket", style=discord.ButtonStyle.green)
	reopen_btn.callback = reopen_callback
	view.add_item(reopen_btn)

	# Add Transcript button (staff only - sends to channel)
	transcript_btn = Button(
		label="📜 Transcript",
		style=discord.ButtonStyle.blurple,
		custom_id="view_transcript_channel"
	)
	transcript_btn.callback = handle_transcript_button_channel
	view.add_item(transcript_btn)

	# Add Delete Channel button
	delete_channel_btn = discord.ui.Button(
		label="🗑️ Delete",
		style=discord.ButtonStyle.red,
		custom_id="delete_channel"
	)

	async def delete_channel_callback(interaction: discord.Interaction):
		try:
			# Check if user has staff role
			has_staff_role = False
			for role_id in ticket_config["staff_roles"]:
				role = interaction.guild.get_role(role_id)
				if role and role in interaction.user.roles:
					has_staff_role = True
					break

			if not has_staff_role:
				await interaction.response.send_message(
					"You don't have permission to delete tickets.",
					ephemeral=True
				)
				return

			# Store guild reference before deleting channel
			guild = channel.guild
			await channel.delete()

			# Cleanup commands if this was the last ticket channel
			try:
				command_manager = get_ticket_command_manager()
				await command_manager.cleanup_commands_for_guild_if_no_tickets(guild)
			except Exception as e:
				logger.warning(f"Failed to cleanup ticket commands for guild {guild.name}: {e}")

		except discord.Forbidden:
			await interaction.response.send_message(
				"Missing permissions to delete channel.",
				ephemeral=True
			)
		except Exception as e:
			await interaction.response.send_message(
				f"Error deleting channel: {str(e)}",
				ephemeral=True
			)

	delete_channel_btn.callback = delete_channel_callback
	view.add_item(delete_channel_btn)

	try:
		await channel.send(embed=close_embed, view=view)
		return True, None
	except Exception as e:
		print(f"Error setting up buttons: {e}")
		return False, str(e)


async def handle_transcript_button(interaction: discord.Interaction):
    """Handle transcript button click - sends transcript as ephemeral message to user"""
    try:
        # Check if user has staff role
        has_staff_role = False
        for role_id in ticket_config.get("staff_roles", []):
            role = interaction.guild.get_role(role_id)
            if role and role in interaction.user.roles:
                has_staff_role = True
                break

        if not has_staff_role:
            await interaction.response.send_message(
                "You don't have permission to view transcripts.",
                ephemeral=True
            )
            return

        # Defer response while we gather messages
        await interaction.response.defer(ephemeral=True)

        # Try to get stored transcript first
        try:
            from transcript_manager import get_transcript
            channel_id = str(interaction.channel.id)
            stored_transcript = await get_transcript(ticket_id=channel_id)

            if stored_transcript and stored_transcript.get("messages"):
                logger.info(f"Using stored transcript for channel {channel_id}")
                messages = stored_transcript["messages"]

                # Format stored messages for display
                formatted_messages = []
                for msg in messages:
                    timestamp = msg.get("timestamp", "Unknown time")
                    author = msg.get("author_name", "Unknown user")
                    content = msg.get("content", "")

                    # Handle embeds in stored messages
                    if msg.get("embeds"):
                        for embed in msg["embeds"]:
                            if embed.get("description"):
                                content += f"\nEmbed: {embed['description']}"

                    formatted_messages.append(f"[{timestamp}] {author}: {content}")

                transcript_content = "\n".join(formatted_messages)
            else:
                raise Exception("No stored transcript found")

        except ImportError:
            logger.warning("Transcript manager not available, falling back to channel history")
        except Exception as db_error:
            logger.error(f"Error retrieving transcript from database: {db_error}")

        # If we get here, either there's no stored transcript or we couldn't retrieve it
        # Fall back to gathering messages from channel history
        if 'transcript_content' not in locals():
            logger.info("Gathering messages from channel history for transcript")
            messages = []
            async for message in interaction.channel.history(limit=None, oldest_first=True):
                timestamp = message.created_at.strftime("%Y-%m-%d %H:%M:%S")
                content = message.content

                # Handle embeds
                if message.embeds:
                    for embed in message.embeds:
                        if embed.description:
                            content += f"\nEmbed: {embed.description}"

                # Handle attachments
                if message.attachments:
                    for attachment in message.attachments:
                        content += f"\nAttachment: {attachment.filename} ({attachment.url})"

                messages.append(f"[{timestamp}] {message.author}: {content}")

            transcript_content = "\n".join(messages)

        # Create embed for transcript
        transcript_embed = discord.Embed(
            title=f"📜 Transcript for {interaction.channel.name}",
            color=0x2b2d31,
            timestamp=datetime.now()
        )

        # Add metadata
        transcript_embed.add_field(
            name="Channel",
            value=interaction.channel.mention,
            inline=True
        )
        transcript_embed.add_field(
            name="Generated by",
            value=interaction.user.mention,
            inline=True
        )
        transcript_embed.add_field(
            name="Message Count",
            value=str(len(messages)),
            inline=True
        )

        # Split transcript into chunks if it's too long (Discord embed description limit is 4096)
        max_chunk_size = 4000  # Leave some room for formatting
        chunks = [transcript_content[i:i+max_chunk_size] for i in range(0, len(transcript_content), max_chunk_size)]

        # Send first chunk in embed description
        if chunks:
            transcript_embed.description = f"```\n{chunks[0]}\n```"

        # If transcript is too long, create additional files
        if len(chunks) > 1:
            # Create file for remaining chunks
            remaining_content = "\n".join(chunks[1:])
            transcript_file = discord.File(
                io.StringIO(remaining_content),
                filename=f"transcript-{interaction.channel.name}-continued.txt"
            )
            await interaction.followup.send(
                embed=transcript_embed,
                file=transcript_file,
                ephemeral=True
            )
        else:
            await interaction.followup.send(
                embed=transcript_embed,
                ephemeral=True
            )

        logger.info(f"Transcript generated for {interaction.channel.name} by {interaction.user}")

    except Exception as e:
        logger.error(f"Error viewing transcript: {e}")
        try:
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "An error occurred while generating the transcript.",
                    ephemeral=True
                )
            else:
                await interaction.followup.send(
                    "An error occurred while generating the transcript.",
                    ephemeral=True
                )
        except:
            pass  # If we can't send error message, just log it

async def handle_transcript_button_channel(interaction: discord.Interaction):
    """Handle transcript button click - sends notification to ticket channel and transcript to transcript channel (staff only)"""
    try:
        # Check if user has staff role
        has_staff_role = False
        for role_id in ticket_config.get("staff_roles", []):
            role = interaction.guild.get_role(role_id)
            if role and role in interaction.user.roles:
                has_staff_role = True
                break

        if not has_staff_role:
            await interaction.response.send_message(
                "You don't have permission to view transcripts.",
                ephemeral=True
            )
            return

        # Check if transcript channel is configured
        transcript_channel_id = ticket_config.get("transcript_channel")
        if not transcript_channel_id:
            await interaction.response.send_message(
                "No transcript channel is configured. Please contact an administrator.",
                ephemeral=True
            )
            return

        transcript_channel = interaction.guild.get_channel(transcript_channel_id)
        if not transcript_channel:
            await interaction.response.send_message(
                "Transcript channel not found. Please contact an administrator.",
                ephemeral=True
            )
            return

        # Defer response while we process the transcript
        await interaction.response.defer(ephemeral=True)

        # Extract ticket number from channel name
        ticket_number = None
        if interaction.channel.name.startswith('ticket-'):
            ticket_number = interaction.channel.name.split('-')[1]
        elif interaction.channel.name.startswith('closed-'):
            ticket_number = interaction.channel.name.split('-')[1]

        if not ticket_number:
            await interaction.followup.send(
                "Could not determine ticket number from channel name.",
                ephemeral=True
            )
            return

        # Try to get transcript from database first
        transcript_data = None
        try:
            # Import transcript manager
            from transcript_manager import transcript_manager

            # Try to get transcript by ticket ID
            transcript_data = await transcript_manager.get_transcript(ticket_id=ticket_number)

            if transcript_data:
                # Send the stored transcript to transcript channel
                await send_transcript_to_channel(
                    interaction.guild,
                    transcript_data,
                    transcript_channel_id
                )

                # Send simple notification to ticket channel
                notification_embed = discord.Embed(
                    title="📜 Transcript Saved",
                    description="The transcript has been saved to the transcript channel.",
                    color=0x000000,  # Professional black theme
                    timestamp=datetime.now(timezone.utc)
                )

                notification_embed.add_field(
                    name="Ticket ID",
                    value=f"#{ticket_number}",
                    inline=True
                )

                notification_embed.add_field(
                    name="Requested by",
                    value=interaction.user.mention,
                    inline=True
                )

                # Send notification to ticket channel
                await interaction.channel.send(embed=notification_embed)

                return

        except ImportError:
            logger.warning("Transcript manager not available, falling back to channel history")
        except Exception as db_error:
            logger.error(f"Error retrieving transcript from database: {db_error}")

        # If we get here, either there's no stored transcript or we couldn't retrieve it
        # Fall back to gathering messages from channel history

        # Acknowledge the request
        await interaction.followup.send("Generating transcript...", ephemeral=True)

        # Gather messages with enhanced metadata using the improved collection system
        messages = await collect_ticket_messages(interaction.channel)

        # Extract participants and staff participants
        participants = set()
        staff_participants = set()
        staff_role_ids = ticket_config.get("staff_roles", [])

        for msg in messages:
            author_id = msg.get("author_id")
            if author_id:
                participants.add(author_id)

                # Check if author is staff based on roles
                author_roles = msg.get("author_roles", [])
                if any(role_id in staff_role_ids for role_id in author_roles):
                    staff_participants.add(author_id)

        # Determine ticket creation time from first message
        created_at = datetime.now().isoformat()
        if messages:
            first_msg = messages[0]
            created_at = first_msg.get("timestamp", created_at)

        # Get category information
        category_name = "Support"
        category_id = None
        if interaction.channel.category:
            category_id = interaction.channel.category.id
            category_id_str = str(category_id)
            if category_id_str in ticket_config.get("categories", {}):
                category_name = ticket_config["categories"][category_id_str].get("name", "Support")

        # Create enhanced transcript data structure
        transcript_data = {
            "ticket_id": ticket_number,
            "guild_id": interaction.guild.id,
            "channel_id": interaction.channel.id,
            "category": category_name,
            "category_id": category_id,
            "created_at": created_at,
            "closed_at": datetime.now().isoformat(),
            "closed_by": interaction.user.id,
            "closed_by_name": interaction.user.name,
            "close_reason": "Manual transcript request by staff",
            "messages": messages,
            "message_count": len(messages),
            "participants": list(participants),
            "staff_participants": list(staff_participants),
            "staff_roles": staff_role_ids,
            "resolution_time": None,  # Cannot calculate for manual requests
            "first_response_time": None  # Cannot calculate for manual requests
        }

        # Store the transcript first to get the proper ID
        try:
            transcript_id = await store_transcript_data(transcript_data)
            if transcript_id:
                # Add the transcript ID to the data
                transcript_data["_id"] = transcript_id
                logger.info(f"Stored manual transcript {transcript_id} for ticket {ticket_number}")
        except Exception as e:
            logger.error(f"Error storing manual transcript: {e}")
            # Continue without ID if storage fails

        # Send the transcript to transcript channel (now with proper ID if available)
        await send_transcript_to_channel(
            interaction.guild,
            transcript_data,
            transcript_channel_id
        )

        # Send simple notification to ticket channel
        notification_embed = discord.Embed(
            title="📜 Transcript Saved",
            description="The transcript has been saved to the transcript channel.",
            color=0x000000,  # Professional black theme
            timestamp=datetime.now(timezone.utc)
        )

        notification_embed.add_field(
            name="Ticket ID",
            value=f"#{ticket_number}",
            inline=True
        )

        notification_embed.add_field(
            name="Requested by",
            value=interaction.user.mention,
            inline=True
        )

        # Send notification to ticket channel
        await interaction.channel.send(embed=notification_embed)

    except Exception as e:
        logger.error(f"Error processing transcript: {e}")
        traceback.print_exc()
        await interaction.followup.send(
            "An error occurred while processing the transcript.",
            ephemeral=True
        )

async def add_category(guild, name: str, description: str):
	"""Add a new ticket category"""
	try:
		# Create category in Discord
		category = await guild.create_category(name)

		# Add to ticket config
		ticket_config["categories"][str(category.id)] = {
			"name": name,
			"description": description
		}

		# Save changes
		await save_ticket_data()

		print(f"Added category {name} with ID {category.id}")
		print(f"Updated categories: {ticket_config['categories']}")

		# Update ticket panel
		if ticket_config.get("ticket_channel"):
			channel = bot.get_channel(ticket_config["ticket_channel"])
			if channel:
				# TODO: Replace with create_modern_ticket_panel(channel)
				pass

		return True, category

	except Exception as e:
		print(f"Error adding category: {e}")
		traceback.print_exc()
		return False, str(e)


async def remove_category(category_id: int):
	"""Remove a ticket category"""
	if category_id not in ticket_config["categories"]:
		return False, "Category not found"

	try:
		del ticket_config["categories"][category_id]
		await save_ticket_data()
		return True, None
	except Exception as e:
		return False, str(e)

async def set_staff_role(role_id: int):
	"""Add a staff role to the ticket system"""
	if "staff_roles" not in ticket_config:
		ticket_config["staff_roles"] = []

	if role_id in ticket_config["staff_roles"]:
		return False

	ticket_config["staff_roles"].append(role_id)
	await save_ticket_data()
	return True

async def set_transcript_channel(channel_id: int):
	"""Set the transcript channel for closed tickets"""
	ticket_config["transcript_channel"] = channel_id
	await save_ticket_data()
	return True

async def get_channel(channel_id: int):
	"""Helper function to get channel from ID"""
	for guild in bot.guilds:
		channel = guild.get_channel(channel_id)
		if channel:
			return channel
	return None

async def set_ticket_channel(channel_id: int):
	"""Set the channel where users can create tickets"""
	try:
		ticket_config["ticket_channel"] = channel_id
		await save_ticket_data()

		# Get channel and create panel
		channel = bot.get_channel(channel_id)
		if channel:
			# TODO: Replace with create_modern_ticket_panel(channel)
			pass

		return True
	except Exception as e:
		print(f"Error setting ticket channel: {e}")
		return False
